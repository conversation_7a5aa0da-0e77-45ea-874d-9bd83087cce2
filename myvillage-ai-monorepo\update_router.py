import sys
import os

file_path = r"d:\Projects\myvillage\Git_Repo\myvillageai\myvillage-ai-monorepo\orchestrator\app\routers\chat_router.py"

if not os.path.exists(file_path):
    print(f"File not found: {file_path}")
    sys.exit(1)

with open(file_path, 'r', encoding='utf-8') as f:
    content = f.read()

# Replace return ChatResponse( with return await save_and_return(ChatResponse(
new_content = content.replace("return ChatResponse(", "return await save_and_return(ChatResponse(")

with open(file_path, 'w', encoding='utf-8') as f:
    f.write(new_content)

print("Successfully updated chat_router.py")
