@echo off
REM Start all services using Docker Compose

echo ========================================
echo Starting MyVillage AI with Docker
echo ========================================
echo.

REM Check if .env exists
if not exist .env (
    echo WARNING: .env file not found!
    echo Copying .env.example to .env...
    copy .env.example .env
    echo.
    echo Please edit .env with your actual values before continuing.
    echo Press any key to continue anyway or Ctrl+C to exit...
    pause >nul
)

echo Building Docker images...
docker-compose build

echo.
echo Starting all services...
docker-compose up -d

echo.
echo Waiting for services to start...
timeout /t 10 /nobreak >nul

echo.
echo ========================================
echo Services Status
echo ========================================
docker-compose ps

echo.
echo ========================================
echo Health Checks
echo ========================================
echo.

curl -s http://localhost:8100/health
echo.
curl -s http://localhost:8001/health
echo.
curl -s http://localhost:8002/health
echo.
curl -s http://localhost:8003/health
echo.

echo.
echo ========================================
echo All services are running!
echo ========================================
echo.
echo Services:
echo   - Orchestrator:     http://localhost:8100
echo   - Onboarding MCP:   http://localhost:8001
echo   - Activities MCP:   http://localhost:8002
echo   - Rewards MCP:      http://localhost:8003
echo.
echo View logs: docker-compose logs -f
echo Stop services: docker-compose down
echo.
pause
