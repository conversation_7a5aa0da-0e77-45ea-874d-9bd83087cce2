# Code Extraction Guide: From Monolith to MCPs
## Detailed Step-by-Step Instructions for Service Isolation

**Document Version:** 1.0  
**Companion Documents:** `MVP_MODULARIZATION_PLAN.md`, `IMPLEMENTATION_QUICK_START.md`

---

## Table of Contents

1. [Extraction Overview](#extraction-overview)
2. [OnboardingMCP Extraction](#onboardingmcp-extraction)
3. [ActivityMCP Extraction](#activitymcp-extraction)
4. [ChatMCP Extraction](#chatmcp-extraction)
5. [Shared Libraries Setup](#shared-libraries-setup)
6. [Refactoring Services](#refactoring-services)
7. [Testing After Extraction](#testing-after-extraction)

---

## Extraction Overview

### Current State
```
my_onboarding_api/app/
├── main.py (routes all requests)
├── services/ (6 monolithic services)
│   ├── auth_service.py
│   ├── session_service.py
│   ├── intent_service.py
│   ├── gemini_service.py
│   ├── activity_service.py
│   └── submission_service.py
└── api/routes.py (all endpoints)
```

### Target State
```
my_onboarding_api/
├── mcp/
│   ├── onboarding_mcp/      (auth_service + session_service)
│   ├── activity_mcp/        (activity_service + submission_service)
│   ├── chat_mcp/            (intent_service + gemini_service)
│   └── orchestrator/        (new router)
├── shared/                  (extracted shared code)
└── app/                     (deprecating - kept for compatibility)
```

### Extraction Strategy

**Phase 1: Copy & Adapt (Keep Original)**
1. Copy services to MCP directories
2. Adapt imports and configurations
3. Remove monolith dependencies
4. Add MCP-specific adapters
5. Keep original monolith running

**Phase 2: Consolidate**
1. Make monolith proxy to MCPs
2. Run both in parallel
3. Monitor for issues

**Phase 3: Decommission**
1. Switch all traffic to Orchestrator + MCPs
2. Remove monolith code

---

## OnboardingMCP Extraction

### Step 1: Copy Auth & Session Services

```bash
# Create directory
mkdir -p my_onboarding_api/mcp/onboarding_mcp/services

# Copy files (don't move yet)
cp my_onboarding_api/app/services/auth_service.py \
   my_onboarding_api/mcp/onboarding_mcp/services/

cp my_onboarding_api/app/services/session_service.py \
   my_onboarding_api/mcp/onboarding_mcp/services/
```

### Step 2: Refactor auth_service.py

**Current Code (Monolith):**

```python
# my_onboarding_api/app/services/auth_service.py
from ..core.config import settings
from ..core.logging import get_logger
from ..models.requests import LoginRequest, SignupRequest

class AuthenticationService:
    def __init__(self):
        self.auth_url = settings.auth_api_url
        # ...

auth_service = AuthenticationService()
```

**Target Code (MCP):**

```python
# my_onboarding_api/mcp/onboarding_mcp/services/auth_service.py
"""
Authentication service for OnboardingMCP.
Extracted from monolith, adapted for MCP usage.
"""

import httpx
from typing import Dict, Any, Optional
import os
from ...shared.logging import get_logger  # NEW: shared logging
from ...shared.exceptions import AuthenticationError  # NEW: shared exceptions

logger = get_logger(__name__)

class AuthenticationService:
    """Authentication service for user signup/login."""
    
    def __init__(self):
        """Initialize auth service with environment config."""
        self.auth_url = os.getenv(
            "AUTH_API_URL",
            "http://auth-service:8000/auth/login"
        )
        self.signup_url = os.getenv(
            "SIGNUP_API_URL",
            "http://auth-service:8000/auth/signup"
        )
        self.timeout = int(os.getenv("AUTH_API_TIMEOUT", "10"))
        self.bearer_token = os.getenv("API_BEARER_TOKEN", "")
        self._client: Optional[httpx.AsyncClient] = None
    
    async def initialize(self):
        """Initialize async HTTP client."""
        self._client = httpx.AsyncClient()
        logger.info("Auth service initialized")
    
    async def cleanup(self):
        """Cleanup resources."""
        if self._client:
            await self._client.aclose()
    
    async def login(self, email: str, password: str) -> Dict[str, Any]:
        """
        Authenticate user login.
        
        Args:
            email: User email
            password: User password
            
        Returns:
            Authentication response with user data and token
            
        Raises:
            AuthenticationError: If login fails
        """
        if not self._client:
            raise RuntimeError("Auth service not initialized. Call initialize() first.")
        
        logger.info(f"Attempting login for: {email}")
        
        try:
            request_data = {
                "email": email,
                "password": password
            }
            
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "OnboardingMCP/1.0"
            }
            
            if self.bearer_token:
                headers["Authorization"] = f"Bearer {self.bearer_token}"
            
            response = await self._client.post(
                self.auth_url,
                json=request_data,
                headers=headers,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            response_data = response.json()
            
            if response_data.get("success"):
                logger.info(f"Login successful for: {email}")
                return {
                    "status": "success",
                    "message": "Login successful",
                    "data": response_data
                }
            else:
                logger.warning(f"Login failed for: {email}")
                raise AuthenticationError(
                    f"Login failed: {response_data.get('message', 'Unknown error')}",
                    status_code=response.status_code
                )
        
        except httpx.HTTPStatusError as e:
            logger.error(f"Auth API error: {e.status_code}")
            raise AuthenticationError(
                f"Authentication service error: {e.status_code}",
                status_code=e.status_code
            )
        except httpx.RequestError as e:
            logger.error(f"Request error to auth API: {str(e)}")
            raise AuthenticationError(
                f"Failed to connect to authentication service: {str(e)}"
            )
    
    async def signup(self, name: str, email: str, password: str, 
                     **kwargs) -> Dict[str, Any]:
        """
        Register new user.
        
        Args:
            name: User full name
            email: User email
            password: User password
            **kwargs: Additional user fields
            
        Returns:
            Signup response with user data
            
        Raises:
            AuthenticationError: If signup fails
        """
        if not self._client:
            raise RuntimeError("Auth service not initialized. Call initialize() first.")
        
        logger.info(f"Attempting signup for: {email}")
        
        try:
            request_data = {
                "email": email,
                "password": password,
                "name": name,
                **kwargs
            }
            
            response = await self._client.post(
                self.signup_url,
                json=request_data,
                headers={"Content-Type": "application/json"},
                timeout=self.timeout
            )
            
            response.raise_for_status()
            response_data = response.json()
            
            if response_data.get("success"):
                logger.info(f"Signup successful for: {email}")
                return {
                    "status": "success",
                    "message": "Account created successfully",
                    "data": response_data
                }
            else:
                logger.warning(f"Signup failed for: {email}")
                raise AuthenticationError(
                    f"Signup failed: {response_data.get('message', 'Unknown error')}",
                    status_code=response.status_code
                )
        
        except httpx.HTTPStatusError as e:
            logger.error(f"Signup API error: {e.status_code}")
            raise AuthenticationError(
                f"Signup service error: {e.status_code}",
                status_code=e.status_code
            )
        except httpx.RequestError as e:
            logger.error(f"Request error during signup: {str(e)}")
            raise AuthenticationError(
                f"Failed to connect to signup service: {str(e)}"
            )

# Singleton instance for MCP
auth_service = AuthenticationService()
```

**Key Changes:**
1. ✅ Removed monolith config imports → Use environment variables
2. ✅ Changed to async (httpx instead of requests)
3. ✅ Added initialize/cleanup for async lifecycle
4. ✅ Better error handling
5. ✅ Imports from `shared` instead of `app`

### Step 3: Refactor session_service.py

**Current Code (Monolith):**

```python
# my_onboarding_api/app/services/session_service.py
from ..models.session import SessionData, FlowType

class SessionService:
    def __init__(self):
        self._sessions = {}  # In-memory
    
    def start_flow(self, session_id: str, flow_type: FlowType):
        # Logic...
```

**Target Code (MCP):**

```python
# my_onboarding_api/mcp/onboarding_mcp/services/session_service.py
"""
Session management service for OnboardingMCP.
Handles user flow state across signup/login.
"""

from typing import Dict, Optional, Any
from datetime import datetime, timezone
import json

from ...shared.models import SessionData, FlowType, FlowStep  # NEW: shared models
from ...shared.logging import get_logger

logger = get_logger(__name__)

class SessionService:
    """
    Manage user session state for onboarding flows.
    
    TODO: Replace in-memory storage with Redis or DynamoDB for production.
    """
    
    def __init__(self):
        """Initialize session service."""
        self._sessions: Dict[str, SessionData] = {}
        logger.info("SessionService initialized with in-memory storage")
    
    def get_session(self, session_id: str) -> SessionData:
        """
        Get or create session.
        
        Args:
            session_id: Unique session identifier
            
        Returns:
            SessionData object
        """
        if session_id not in self._sessions:
            logger.debug(f"Creating new session: {session_id}")
            self._sessions[session_id] = SessionData(
                id=session_id,
                created_at=datetime.now(timezone.utc).isoformat(),
                last_activity=datetime.now(timezone.utc).isoformat(),
                flow_type=None,
                flow_step=None,
                collected_data={}
            )
        else:
            # Update last activity
            self._sessions[session_id].last_activity = datetime.now(timezone.utc).isoformat()
        
        return self._sessions[session_id]
    
    def start_flow(self, session_id: str, flow_type: str) -> SessionData:
        """
        Initialize a flow (signup or login).
        
        Args:
            session_id: Session ID
            flow_type: "signup" or "login"
            
        Returns:
            Updated session
        """
        session = self.get_session(session_id)
        
        if flow_type == "signup":
            session.flow_type = "signup"
            session.flow_step = "email"
            session.collected_data = {}
        elif flow_type == "login":
            session.flow_type = "login"
            session.flow_step = "email"
            session.collected_data = {}
        else:
            raise ValueError(f"Unknown flow type: {flow_type}")
        
        logger.info(f"Started {flow_type} flow for session: {session_id}")
        return session
    
    def advance_flow(self, session_id: str, field_name: str, value: Any) -> Optional[str]:
        """
        Advance flow by one step.
        
        Args:
            session_id: Session ID
            field_name: Field being filled (email, password, etc.)
            value: Field value
            
        Returns:
            Next step name, or None if flow complete
        """
        session = self.get_session(session_id)
        
        # Validate email format
        if field_name == "email":
            if not self._validate_email(value):
                raise ValueError("Invalid email format")
        
        # Store value
        session.collected_data[field_name] = value
        
        # Determine next step
        if session.flow_type == "signup":
            SIGNUP_STEPS = ["email", "password", "name", "phone"]
            current_idx = SIGNUP_STEPS.index(session.flow_step) if session.flow_step in SIGNUP_STEPS else -1
            next_idx = current_idx + 1
            
            if next_idx < len(SIGNUP_STEPS):
                session.flow_step = SIGNUP_STEPS[next_idx]
                return session.flow_step
            else:
                session.flow_step = "complete"
                return None  # Flow complete
        
        elif session.flow_type == "login":
            LOGIN_STEPS = ["email", "password"]
            current_idx = LOGIN_STEPS.index(session.flow_step) if session.flow_step in LOGIN_STEPS else -1
            next_idx = current_idx + 1
            
            if next_idx < len(LOGIN_STEPS):
                session.flow_step = LOGIN_STEPS[next_idx]
                return session.flow_step
            else:
                session.flow_step = "complete"
                return None  # Flow complete
        
        return None
    
    def complete_flow(self, session_id: str) -> Dict[str, Any]:
        """
        Mark flow as complete and return collected data.
        
        Args:
            session_id: Session ID
            
        Returns:
            Collected user data
        """
        session = self.get_session(session_id)
        logger.info(f"Completing {session.flow_type} flow for session: {session_id}")
        
        data = session.collected_data.copy()
        
        # Reset flow
        session.flow_type = None
        session.flow_step = None
        session.collected_data = {}
        
        return data
    
    def get_flow_message(self, session_id: str) -> str:
        """
        Get user-facing message for current flow step.
        
        Args:
            session_id: Session ID
            
        Returns:
            Message to display to user
        """
        session = self.get_session(session_id)
        
        messages = {
            ("signup", "email"): "What's your email address?",
            ("signup", "password"): "Create a password (min 8 characters):",
            ("signup", "name"): "What's your name?",
            ("signup", "phone"): "What's your phone number? (optional)",
            ("login", "email"): "Enter your email:",
            ("login", "password"): "Enter your password:",
        }
        
        key = (session.flow_type, session.flow_step)
        return messages.get(key, "Continue with onboarding:")
    
    @staticmethod
    def _validate_email(email: str) -> bool:
        """Simple email validation."""
        return "@" in email and "." in email.split("@")[1]

# Singleton instance
session_service = SessionService()
```

### Step 4: Create OnboardingMCP API Routes

**File: `mcp/onboarding_mcp/api/routes.py`**

```python
"""
OnboardingMCP API routes.
Handles signup/login flows via standardized MCP endpoints.
"""

from fastapi import APIRouter, HTTPException, status
from typing import Dict, Any, Optional
from pydantic import BaseModel

from ..services.auth_service import auth_service
from ..services.session_service import session_service
from ...shared.logging import get_logger
from ...shared.exceptions import AuthenticationError, SessionError

logger = get_logger(__name__)
router = APIRouter()

# Request models
class SessionInitRequest(BaseModel):
    session_id: str
    user_agent: Optional[str] = None
    client_ip: Optional[str] = None

class MessageRequest(BaseModel):
    session_id: str
    message: str
    detected_intent: Optional[str] = None
    context: Optional[Dict[str, Any]] = None

# Response models
class FlowResponseData(BaseModel):
    flow_type: str
    current_step: str
    message: str
    flow_state: Dict[str, Any]

class FlowCompleteData(BaseModel):
    user: Dict[str, Any]
    token: str
    message: str

# Endpoints
@router.post("/session/init")
async def init_session(request: SessionInitRequest) -> Dict[str, Any]:
    """Initialize onboarding session."""
    try:
        session = session_service.get_session(request.session_id)
        
        return {
            "success": True,
            "type": "session_initialized",
            "data": {
                "session_id": request.session_id,
                "created_at": session.created_at,
                "status": "active"
            }
        }
    except Exception as e:
        logger.error(f"Failed to initialize session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/message")
async def process_message(request: MessageRequest) -> Dict[str, Any]:
    """
    Process user message in onboarding flow.
    
    Routes to signup or login handler based on detected intent.
    """
    session_id = request.session_id
    message = request.message
    detected_intent = request.detected_intent or "unknown"
    context = request.context or {}
    
    logger.info(f"Processing {detected_intent} for session {session_id}")
    
    try:
        session = session_service.get_session(session_id)
        
        # Route based on intent and current flow
        if detected_intent in ["signup", "register"]:
            return await _handle_signup(session_id, message)
        elif detected_intent in ["login", "signin"]:
            return await _handle_login(session_id, message)
        else:
            # Continue active flow if any
            if session.flow_type:
                if session.flow_type == "signup":
                    return await _handle_signup(session_id, message)
                elif session.flow_type == "login":
                    return await _handle_login(session_id, message)
            
            return {
                "success": False,
                "type": "error",
                "error": {
                    "code": "unknown_intent",
                    "message": "Please specify signup or login"
                }
            }
    
    except AuthenticationError as e:
        logger.error(f"Auth error: {str(e)}")
        return {
            "success": False,
            "type": "error",
            "error": {
                "code": "auth_error",
                "message": str(e)
            }
        }
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return {
            "success": False,
            "type": "error",
            "error": {
                "code": "internal_error",
                "message": "Internal server error"
            }
        }

async def _handle_signup(session_id: str, user_input: str) -> Dict[str, Any]:
    """Handle signup flow."""
    session = session_service.get_session(session_id)
    
    # Start flow if not already started
    if not session.flow_type:
        session_service.start_flow(session_id, "signup")
        session = session_service.get_session(session_id)
    
    current_step = session.flow_step
    
    # Advance flow
    try:
        next_step = session_service.advance_flow(session_id, current_step, user_input)
    except ValueError as e:
        # Validation error, ask for retry
        return {
            "success": False,
            "type": "validation_error",
            "data": {
                "flow_type": "signup",
                "current_step": current_step,
                "message": f"Error: {str(e)}. Please try again.",
                "error": str(e)
            }
        }
    
    # Check if complete
    if next_step is None:
        # Flow complete, call auth service to create user
        logger.info(f"Signup flow complete for session {session_id}")
        
        user_data = session_service.complete_flow(session_id)
        
        try:
            # Call authentication service
            result = await auth_service.signup(
                email=user_data.get("email"),
                password=user_data.get("password"),
                name=user_data.get("name"),
                phone=user_data.get("phone")
            )
            
            if result["status"] == "success":
                return {
                    "success": True,
                    "type": "signup_complete",
                    "data": {
                        "user": result["data"],
                        "message": "Welcome! Your account has been created.",
                        "token": result["data"].get("token")
                    }
                }
            else:
                return {
                    "success": False,
                    "type": "error",
                    "error": {
                        "code": "signup_failed",
                        "message": result.get("message", "Signup failed")
                    }
                }
        
        except AuthenticationError as e:
            logger.error(f"Signup service error: {str(e)}")
            return {
                "success": False,
                "type": "error",
                "error": {
                    "code": "auth_service_error",
                    "message": str(e)
                }
            }
    
    else:
        # Continue flow
        flow_message = session_service.get_flow_message(session_id)
        return {
            "success": True,
            "type": "flow_response",
            "data": {
                "flow_type": "signup",
                "current_step": next_step,
                "message": flow_message,
                "flow_state": session.collected_data
            }
        }

async def _handle_login(session_id: str, user_input: str) -> Dict[str, Any]:
    """Handle login flow."""
    session = session_service.get_session(session_id)
    
    # Start flow if not already started
    if not session.flow_type:
        session_service.start_flow(session_id, "login")
        session = session_service.get_session(session_id)
    
    current_step = session.flow_step
    
    # Advance flow
    try:
        next_step = session_service.advance_flow(session_id, current_step, user_input)
    except ValueError as e:
        return {
            "success": False,
            "type": "validation_error",
            "data": {
                "flow_type": "login",
                "current_step": current_step,
                "message": f"Error: {str(e)}. Please try again.",
                "error": str(e)
            }
        }
    
    # Check if complete
    if next_step is None:
        # Flow complete, call auth service
        logger.info(f"Login flow complete for session {session_id}")
        
        user_data = session_service.complete_flow(session_id)
        
        try:
            # Call authentication service
            result = await auth_service.login(
                email=user_data.get("email"),
                password=user_data.get("password")
            )
            
            if result["status"] == "success":
                return {
                    "success": True,
                    "type": "login_complete",
                    "data": {
                        "user": result["data"],
                        "message": "Welcome back!",
                        "token": result["data"].get("token")
                    }
                }
            else:
                return {
                    "success": False,
                    "type": "error",
                    "error": {
                        "code": "login_failed",
                        "message": result.get("message", "Login failed")
                    }
                }
        
        except AuthenticationError as e:
            logger.error(f"Login service error: {str(e)}")
            return {
                "success": False,
                "type": "error",
                "error": {
                    "code": "auth_service_error",
                    "message": str(e)
                }
            }
    
    else:
        # Continue flow
        flow_message = session_service.get_flow_message(session_id)
        return {
            "success": True,
            "type": "flow_response",
            "data": {
                "flow_type": "login",
                "current_step": next_step,
                "message": flow_message,
                "flow_state": session.collected_data
            }
        }

@router.get("/session/{session_id}/state")
async def get_flow_state(session_id: str) -> Dict[str, Any]:
    """Get current flow state."""
    try:
        session = session_service.get_session(session_id)
        return {
            "success": True,
            "type": "flow_state",
            "data": {
                "flow_type": session.flow_type,
                "current_step": session.flow_step,
                "collected_data": session.collected_data
            }
        }
    except Exception as e:
        logger.error(f"Error getting state: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "onboarding_mcp",
        "version": "1.0.0"
    }
```

### Step 5: Create OnboardingMCP Main App

**File: `mcp/onboarding_mcp/main.py`**

```python
"""
OnboardingMCP - MCP service for user authentication and flows.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import os

from .api import routes
from .services.auth_service import auth_service
from ...shared.logging import setup_logging, get_logger

logger = get_logger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan."""
    # Startup
    logger.info("Starting OnboardingMCP")
    await auth_service.initialize()
    
    yield  # App runs here
    
    # Shutdown
    logger.info("Shutting down OnboardingMCP")
    await auth_service.cleanup()

def create_app() -> FastAPI:
    """Create FastAPI application."""
    setup_logging()
    
    app = FastAPI(
        title="OnboardingMCP",
        version="1.0.0",
        description="MCP service for user onboarding (signup/login)",
        lifespan=lifespan
    )
    
    # CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Routes
    app.include_router(routes.router, prefix="/mcp/onboarding")
    
    return app

app = create_app()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=os.getenv("HOST", "0.0.0.0"),
        port=int(os.getenv("PORT", "8001")),
        reload=os.getenv("DEBUG", "false").lower() == "true"
    )
```

---

## ActivityMCP Extraction

### Similar Steps to OnboardingMCP

**Key Differences:**
1. Extract `activity_service.py` and `submission_service.py`
2. Create endpoints for CRUD operations
3. Add activity intent classification endpoint
4. Adjust for async patterns

**File: `mcp/activity_mcp/services/activity_service.py`**

```python
"""Activity management service for ActivityMCP."""

from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
import uuid

from ...shared.logging import get_logger

logger = get_logger(__name__)

class ActivityService:
    """Manage activities."""
    
    def __init__(self):
        """Initialize service."""
        self._activities: Dict[str, Dict[str, Any]] = {}
    
    async def create_activity(self, title: str, description: str, 
                              created_by: str, **kwargs) -> Dict[str, Any]:
        """Create new activity."""
        activity_id = str(uuid.uuid4())
        activity = {
            "id": activity_id,
            "title": title,
            "description": description,
            "created_by": created_by,
            "created_at": datetime.now(timezone.utc).isoformat(),
            **kwargs
        }
        
        self._activities[activity_id] = activity
        logger.info(f"Created activity: {activity_id}")
        
        return activity
    
    async def get_activity(self, activity_id: str) -> Optional[Dict[str, Any]]:
        """Get activity by ID."""
        activity = self._activities.get(activity_id)
        if not activity:
            logger.warning(f"Activity not found: {activity_id}")
        return activity
    
    async def list_activities(self, limit: int = 10, 
                             offset: int = 0) -> tuple[List[Dict[str, Any]], int]:
        """List activities with pagination."""
        activities = list(self._activities.values())
        total = len(activities)
        return activities[offset:offset+limit], total
    
    async def update_activity(self, activity_id: str, 
                             **updates) -> Optional[Dict[str, Any]]:
        """Update activity."""
        activity = self._activities.get(activity_id)
        if not activity:
            return None
        
        activity.update(updates)
        logger.info(f"Updated activity: {activity_id}")
        return activity
    
    async def delete_activity(self, activity_id: str) -> bool:
        """Delete activity."""
        if activity_id in self._activities:
            del self._activities[activity_id]
            logger.info(f"Deleted activity: {activity_id}")
            return True
        return False

activity_service = ActivityService()
```

---

## Shared Libraries Setup

### Create Shared Models

**File: `shared/models/base.py`**

```python
"""Shared data models."""

from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime

class SessionData(BaseModel):
    """Session data model."""
    id: str
    created_at: str
    last_activity: str
    flow_type: Optional[str] = None
    flow_step: Optional[str] = None
    collected_data: Dict[str, Any] = {}

class User(BaseModel):
    """User model."""
    id: str
    email: str
    name: str
    created_at: str

class Activity(BaseModel):
    """Activity model."""
    id: str
    title: str
    description: str
    created_by: str
    created_at: str
```

### Create Shared Exceptions

**File: `shared/exceptions/base.py`**

```python
"""Shared exceptions."""

class OnboardingException(Exception):
    """Base exception."""
    def __init__(self, message: str, code: str = "error", status_code: int = 500):
        self.message = message
        self.code = code
        self.status_code = status_code
        super().__init__(message)

class AuthenticationError(OnboardingException):
    """Authentication error."""
    def __init__(self, message: str, status_code: int = 401):
        super().__init__(message, "auth_error", status_code)

class SessionError(OnboardingException):
    """Session error."""
    def __init__(self, message: str, status_code: int = 400):
        super().__init__(message, "session_error", status_code)

class ValidationError(OnboardingException):
    """Validation error."""
    def __init__(self, message: str, status_code: int = 422):
        super().__init__(message, "validation_error", status_code)
```

### Create Shared Logging

**File: `shared/logging/config.py`**

```python
"""Shared logging configuration."""

import logging
import os
from typing import Optional

def setup_logging(level: Optional[str] = None):
    """Set up logging for MCPs."""
    level = level or os.getenv("LOG_LEVEL", "INFO")
    
    logging.basicConfig(
        level=getattr(logging, level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def get_logger(name: str) -> logging.Logger:
    """Get logger instance."""
    return logging.getLogger(name)
```

---

## Testing After Extraction

### Unit Test Example

**File: `tests/unit/test_onboarding_service.py`**

```python
import pytest
from mcp.onboarding_mcp.services.session_service import session_service

def test_start_signup_flow():
    """Test starting signup flow."""
    session = session_service.start_flow("test-123", "signup")
    assert session.flow_type == "signup"
    assert session.flow_step == "email"

def test_advance_signup_flow():
    """Test advancing signup flow."""
    session_service.start_flow("test-456", "signup")
    next_step = session_service.advance_flow("test-456", "email", "<EMAIL>")
    assert next_step == "password"

def test_email_validation():
    """Test email validation."""
    with pytest.raises(ValueError):
        session_service.advance_flow("test-789", "email", "invalid")
```

### Integration Test Example

**File: `tests/integration/test_onboarding_mcp.py`**

```python
import pytest
import httpx

@pytest.mark.asyncio
async def test_signup_flow_e2e():
    """Test complete signup flow."""
    async with httpx.AsyncClient() as client:
        # Init session
        response = await client.post(
            "http://localhost:8001/mcp/onboarding/session/init",
            json={"session_id": "e2e-test-1"}
        )
        assert response.status_code == 200
        
        # Request email
        response = await client.post(
            "http://localhost:8001/mcp/onboarding/message",
            json={
                "session_id": "e2e-test-1",
                "message": "I want to sign up",
                "detected_intent": "signup"
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] == True
        assert data["data"]["flow_type"] == "signup"
```

---

## Summary Checklist

- [ ] Created MCP directory structures
- [ ] Extracted and refactored auth_service
- [ ] Extracted and refactored session_service
- [ ] Created OnboardingMCP routes and main app
- [ ] Extracted and refactored activity_service
- [ ] Created ActivityMCP routes and main app
- [ ] Created shared libraries (models, exceptions, logging)
- [ ] Updated imports across all files
- [ ] Wrote unit tests for services
- [ ] Wrote integration tests for MCPs
- [ ] Created Dockerfiles for each MCP
- [ ] Created docker-compose.yml for local testing
- [ ] Tested with docker-compose up
- [ ] Created Orchestrator routing logic
- [ ] Tested end-to-end flow through Orchestrator

---

**Next:** Review `IMPLEMENTATION_QUICK_START.md` for running everything locally.
