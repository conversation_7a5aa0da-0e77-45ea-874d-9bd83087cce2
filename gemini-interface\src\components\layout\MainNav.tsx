'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Home, BookOpen, Users, Brain, MessageSquare, User, LogOut } from 'lucide-react';
import MCPDropdown from './MCPDropdown';
import { useAuth } from '@/contexts/AuthContext';

const navigation = [
  { name: 'Home', href: '/', icon: Home },
  { name: 'Learn', href: '/learn', icon: BookOpen },
  { name: 'Community', href: '/community', icon: Users },
  { name: 'AI Assistant', href: '/chat', icon: Brain },
  { name: 'Messages', href: '/messages', icon: MessageSquare },
  { name: 'Profile', href: '/profile', icon: User },
];

export default function MainNav() {
  const pathname = usePathname();
  const { user, isAuthenticated, logout, isLoading } = useAuth();

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 h-16 flex items-center justify-between px-4 bg-card border-b border-border shadow-sm">
      <div className="flex items-center">
        <Link href="/" className="flex items-center">
          <span className="text-xl font-bold text-primary">VillageOS</span>
        </Link>
      </div>

      <div className="flex items-center space-x-4">
        {isAuthenticated && user?.isStakeholder && (
          <div className="flex items-center bg-primary-light text-primary px-3 py-1 rounded-full text-sm font-medium">
            🪙 {user?.mvpPoints || 0} MVP
          </div>
        )}

        <MCPDropdown />

        <Link
          href="/about"
          className="text-sm font-medium text-muted-foreground hover:text-foreground px-3 py-2 rounded-md hover:bg-muted"
        >
          About VillageOS
        </Link>
      </div>
    </nav>
  );
}
