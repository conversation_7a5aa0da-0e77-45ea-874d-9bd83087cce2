export interface ModelConfig {
  id: string;
  name: string;
  description: string;
  maxTokens: number;
  endpoint: string;
  isDefault?: boolean;
}

export const AVAILABLE_MODELS: ModelConfig[] = [
  {
    id: 'gemini-pro',
    name: 'Gemini Pro',
    description: 'Best for most use cases with balanced performance',
    maxTokens: 32000,
    endpoint: '/api/gemini',
    isDefault: true,
  },
  {
    id: 'gemini-pro-vision',
    name: 'Gemini Pro Vision',
    description: 'Supports both text and image inputs',
    maxTokens: 16000,
    endpoint: '/api/gemini-vision',
  },
  {
    id: 'gemini-ultra',
    name: 'Gemini Ultra',
    description: 'Most capable model for complex tasks',
    maxTokens: 32000,
    endpoint: '/api/gemini-ultra',
  },
];

export const DEFAULT_MODEL = AVAILABLE_MODELS.find(model => model.isDefault) || AVAILABLE_MODELS[0];
