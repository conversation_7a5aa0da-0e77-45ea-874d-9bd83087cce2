"""Chat router - main endpoint for message processing."""

import logging
from fastapi import API<PERSON>out<PERSON>, HTTPException
from typing import Dict, Any

from ..schemas.message_schema import ChatRequest, ChatResponse
from ..services.intent_client import intent_client
from ..services.onboarding_client import onboarding_client
from ..services.activity_client import activity_client

logger = logging.getLogger(__name__)

router = APIRouter()

# Session state to track active flows
# In production, this should use Redis or a proper session store
active_sessions: Dict[str, Dict[str, Any]] = {}


@router.post("/chat", response_model=ChatResponse)
async def process_chat_message(request: ChatRequest) -> ChatResponse:
    """
    Main chat endpoint - receives user message and routes to appropriate MCP.
    
    Flow:
    1. Check if session has active flow
    2. If no active flow, detect intent
    3. Route to appropriate client
    4. Track session state
    5. Return unified response
    """
    logger.info("=" * 80)
    logger.info(f"[ChatRouter] NEW MESSAGE from user: {request.user_id}")
    logger.info(f"[ChatRouter] Text: '{request.text}'")
    logger.info(f"[ChatRouter] Session ID: {request.session_id}")
    logger.info("=" * 80)
    
    session_key = request.session_id or request.user_id
    
    try:
        # Step 1: Check for active session flow
        active_flow = active_sessions.get(session_key, {}).get("active_flow")
        
        if active_flow:
            logger.info(f"[ChatRouter] Active flow detected: {active_flow}")
            detected_intent = active_flow
        else:
            # Step 2: Detect intent for new conversation
            logger.info("[ChatRouter] No active flow, detecting intent...")
            intent_result = await intent_client.detect_intent(request.text)
            detected_intent = intent_result.get("intent", "activity")
            confidence = intent_result.get("confidence", 0.0)
            logger.info(f"[ChatRouter] Intent detected: {detected_intent} (confidence: {confidence})")
        
        # Step 3: Route to appropriate MCP client
        logger.info(f"[ChatRouter] Routing to {detected_intent.upper()} client...")
        
        if detected_intent == "onboarding":
            mcp_response = await onboarding_client.process_message(
                user_id=request.user_id,
                text=request.text,
                session_id=request.session_id
            )
            routed_to = "onboarding_mcp"
            
            # Step 4: Track session state
            # If response has flow data, mark session as having active flow
            if mcp_response.get("data", {}).get("flow_type"):
                active_sessions[session_key] = {
                    "active_flow": "onboarding",
                    "flow_type": mcp_response["data"]["flow_type"],
                    "current_step": mcp_response["data"].get("current_step")
                }
                logger.info(f"[ChatRouter] Session {session_key} marked with active onboarding flow")
            else:
                # Flow completed or no flow, clear session
                if session_key in active_sessions:
                    del active_sessions[session_key]
                    logger.info(f"[ChatRouter] Session {session_key} flow completed, cleared")
        else:
            # Default to activity for everything else
            mcp_response = await activity_client.process_message(
                user_id=request.user_id,
                text=request.text,
                session_id=request.session_id
            )
            routed_to = "activity_mcp"
            
            # Clear any active session for activity requests
            if session_key in active_sessions:
                del active_sessions[session_key]
        
        logger.info(f"[ChatRouter] MCP response received from {routed_to}")
        logger.debug(f"[ChatRouter] Response data: {mcp_response}")
        
        # Step 5: Build unified response
        logger.info("[ChatRouter] Building unified response...")
        response = ChatResponse(
            success=mcp_response.get("success", True),
            message=mcp_response.get("message", ""),
            intent=detected_intent,
            routed_to=routed_to,
            data=mcp_response.get("data", {})
        )
        
        logger.info("[ChatRouter] ✓ Request processed successfully")
        logger.info("=" * 80)
        
        return response
        
    except Exception as e:
        logger.error(f"[ChatRouter] ✗ Error processing message: {str(e)}", exc_info=True)
        logger.info("=" * 80)
        raise HTTPException(
            status_code=500,
            detail=f"Error processing message: {str(e)}"
        )
