import { colors } from './design-system';

export const lightTheme = {
  colors: {
    background: colors.neutral[50],
    foreground: colors.neutral[900],
    primary: colors.primary[500],
    'primary-foreground': colors.neutral[50],
    secondary: colors.secondary[500],
    'secondary-foreground': colors.neutral[50],
    muted: colors.neutral[100],
    'muted-foreground': colors.neutral[500],
    accent: colors.primary[100],
    'accent-foreground': colors.primary[700],
    destructive: colors.error.main,
    'destructive-foreground': colors.neutral[50],
    border: colors.neutral[200],
    input: colors.neutral[200],
    ring: colors.primary[500],
  },
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    DEFAULT: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
    inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
    none: '0 0 #0000',
  },
  borderRadius: {
    none: '0px',
    sm: '0.125rem',
    DEFAULT: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    '3xl': '1.5rem',
    full: '9999px',
  },
};

export const darkTheme = {
  colors: {
    background: colors.neutral[900],
    foreground: colors.neutral[50],
    primary: colors.primary[500],
    'primary-foreground': colors.neutral[50],
    secondary: colors.secondary[500],
    'secondary-foreground': colors.neutral[50],
    muted: colors.neutral[800],
    'muted-foreground': colors.neutral[400],
    accent: colors.primary[800],
    'accent-foreground': colors.primary[100],
    destructive: colors.error.dark,
    'destructive-foreground': colors.neutral[50],
    border: colors.neutral[700],
    input: colors.neutral[700],
    ring: colors.primary[500],
  },
  // Other theme values remain the same as light theme
  ...lightTheme,
};

export type Theme = typeof lightTheme;
