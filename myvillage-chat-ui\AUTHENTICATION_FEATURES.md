# 🔐 Authentication Features

## Overview

The chat UI now includes authentication state management with protected features.

---

## ✨ Features

### 1. **User State Management**
- User info stored in localStorage
- Persists across page refreshes
- Automatic login state detection

### 2. **Protected Features**
- Activities listing requires login
- Rewards checking requires login
- Guest users see login prompt

### 3. **User Display**
- **Logged In**: Shows user name and email in header
- **Guest**: Shows "Guest" badge in header
- Logout button for logged-in users

### 4. **Dynamic Quick Actions**
- **Guest Users**: See "Sign Up" and "Log In" buttons
- **Logged In Users**: See "Activities" and "Rewards" buttons

---

## 🎯 User Flow

### Guest User Experience

```
1. Open chat → Header shows "Guest" badge
2. Try to access activities → Blocked with login prompt
3. Click "Sign Up" or "Log In" quick action
4. Complete signup/login flow
5. Automatically logged in
6. <PERSON>er shows user name
7. Can now access activities and rewards
```

### Logged In User Experience

```
1. Open chat → Header shows user name and email
2. See "Activities" and "Rewards" quick actions
3. Can access all features
4. Click logout button to sign out
5. Returns to guest mode
```

---

## 🔒 Protected Features

### Activities
- **Requires**: Login
- **Guest Action**: Shows login prompt
- **Logged In**: Shows activities list

### Rewards
- **Requires**: Login
- **Guest Action**: Shows login prompt
- **Logged In**: Shows rewards balance

### Public Features
- Signup flow
- Login flow
- General chat

---

## 💾 Data Storage

### localStorage Keys
- `myvillage_user` - Stores user object

### User Object Structure
```typescript
{
  id: string
  email: string
  name: string
  token?: string
}
```

---

## 🎨 UI Components

### Header (Logged Out)
```
┌─────────────────────────────────────┐
│ 💬 MyVillage AI Assistant           │
│    Powered by MCP Services          │
│                          [Guest] 🟢 │
└─────────────────────────────────────┘
```

### Header (Logged In)
```
┌─────────────────────────────────────┐
│ 💬 MyVillage AI Assistant           │
│    Powered by MCP Services          │
│              👤 John Doe            │
│              <EMAIL>  🚪 🟢│
└─────────────────────────────────────┘
```

### Quick Actions (Guest)
```
┌──────────────┐  ┌──────────────┐
│   ✨ Sign Up │  │   👤 Log In  │
│   Create an  │  │   Access your│
│   account    │  │   account    │
└──────────────┘  └──────────────┘
```

### Quick Actions (Logged In)
```
┌──────────────┐  ┌──────────────┐
│ ✨ Activities│  │   🎁 Rewards │
│   Explore    │  │   View your  │
│   activities │  │   rewards    │
└──────────────┘  └──────────────┘
```

---

## 🧪 Testing

### Test Guest Protection

```
1. Open chat as guest
2. Type: "Show me activities"
3. Expected: "🔒 Please log in or sign up first..."
```

### Test Signup Flow

```
1. Type: "I want to sign up"
2. Follow prompts (name, email, password)
3. After completion, header shows your name
4. Quick actions change to Activities/Rewards
```

### Test Login Flow

```
1. Type: "I want to log in"
2. Provide email and password
3. After login, header shows your name
4. Can now access protected features
```

### Test Logout

```
1. Click logout button in header
2. Header changes to "Guest"
3. Quick actions change to Sign Up/Log In
4. Protected features blocked again
```

---

## 🔧 Technical Implementation

### Auth Utilities (`lib/auth.ts`)
```typescript
authUtils.saveUser(user)      // Save to localStorage
authUtils.getUser()           // Get current user
authUtils.clearUser()         // Logout
authUtils.isLoggedIn()        // Check login status
authUtils.getDisplayName()    // Get name or "Guest"
```

### Protected Feature Check
```typescript
const isProtectedRequest = protectedIntents.some(intent => 
  lowerText.includes(intent)
)

if (isProtectedRequest && !user) {
  // Show login prompt
}
```

### Auto-Login on Success
```typescript
if (response.data.intent === 'login' && response.data.success) {
  const userData = response.data.data.user
  authUtils.saveUser(userData)
  setUser(userData)
}
```

---

## 🎯 Benefits

1. **Security**: Protected features require authentication
2. **UX**: Clear indication of login status
3. **Persistence**: User stays logged in across refreshes
4. **Flexibility**: Easy to add more protected features
5. **Feedback**: Clear prompts for guests

---

## 📝 Future Enhancements

- [ ] Add token expiration handling
- [ ] Add "Remember me" option
- [ ] Add profile editing
- [ ] Add session timeout warning
- [ ] Add multiple user accounts support

---

## 🎉 Complete!

Your chat UI now has full authentication with:
- ✅ Login/Signup flows
- ✅ Protected features
- ✅ User display in header
- ✅ Logout functionality
- ✅ Guest mode
- ✅ Persistent sessions

Test it now at http://localhost:3000! 🚀
