{"name": "activities-mcp", "version": "1.0.0", "description": "Activity management and submission handling", "author": "MyVillage AI Team", "tools": [{"name": "list_activities", "description": "List available activities with optional filters", "parameters": {"type": "object", "properties": {"activity_type": {"type": "string", "description": "Filter by activity type (assignment, event, survey, discussion, volunteer)", "enum": ["assignment", "event", "survey", "discussion", "volunteer"]}, "status": {"type": "string", "description": "Filter by status (draft, published, archived, completed)", "enum": ["draft", "published", "archived", "completed"]}, "limit": {"type": "integer", "description": "Maximum number of results (1-100)", "minimum": 1, "maximum": 100, "default": 10}}}}, {"name": "get_activity", "description": "Get detailed information about a specific activity", "parameters": {"type": "object", "properties": {"activity_id": {"type": "string", "description": "Unique identifier of the activity"}}, "required": ["activity_id"]}}, {"name": "create_activity", "description": "Create a new activity", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "Activity title", "minLength": 1, "maxLength": 200}, "description": {"type": "string", "description": "Detailed description of the activity"}, "activity_type": {"type": "string", "description": "Type of activity", "enum": ["assignment", "event", "survey", "discussion", "volunteer"]}, "created_by": {"type": "string", "description": "User ID of the creator"}, "due_date": {"type": "string", "format": "date-time", "description": "Due date in ISO format (optional)"}, "points": {"type": "integer", "description": "Points awarded for completion (optional)", "minimum": 0}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Tags for categorization (optional)"}}, "required": ["title", "description", "activity_type", "created_by"]}}, {"name": "submit_assignment", "description": "Submit an assignment for an activity", "parameters": {"type": "object", "properties": {"activity_id": {"type": "string", "description": "ID of the activity"}, "user_id": {"type": "string", "description": "ID of the user submitting"}, "title": {"type": "string", "description": "Submission title", "minLength": 1, "maxLength": 200}, "description": {"type": "string", "description": "Submission description (optional)"}, "content": {"type": "string", "description": "Submission content (optional)"}, "is_public": {"type": "boolean", "description": "Whether the submission is public", "default": true}}, "required": ["activity_id", "user_id", "title"]}}, {"name": "grade_submission", "description": "Grade a submission with feedback", "parameters": {"type": "object", "properties": {"submission_id": {"type": "string", "description": "ID of the submission to grade"}, "grade": {"type": "number", "description": "Grade value (0-100)", "minimum": 0, "maximum": 100}, "feedback": {"type": "string", "description": "Feedback for the student"}, "grader_id": {"type": "string", "description": "ID of the grader"}}, "required": ["submission_id", "grade", "feedback", "grader_id"]}}, {"name": "list_submissions", "description": "List submissions with optional filters", "parameters": {"type": "object", "properties": {"activity_id": {"type": "string", "description": "Filter by activity ID (optional)"}, "user_id": {"type": "string", "description": "Filter by user ID (optional)"}, "status": {"type": "string", "description": "Filter by status (draft, submitted, graded, returned)", "enum": ["draft", "submitted", "graded", "returned"]}, "limit": {"type": "integer", "description": "Maximum number of results (1-100)", "minimum": 1, "maximum": 100, "default": 10}}}}]}