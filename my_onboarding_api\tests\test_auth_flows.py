"""
Authentication and authorization flow tests.

This module combines tests for:
- Signup flow
- Login flow
- Authentication API endpoints
"""

import pytest
from fastapi import status
from unittest.mock import Mock, patch
from app.models.requests import SignupRequest
from app.services.auth_service import AuthenticationService
from app.core.exceptions import AuthenticationError

class TestSignupRequest:
    """Test SignupRequest model with all parameters."""
    
    def test_minimal_signup_request(self):
        """Test signup request with minimal required fields."""
        request = SignupRequest(
            name="<PERSON>",
            firstName="<PERSON>",
            lastName="<PERSON><PERSON>",
            email="<EMAIL>",
            password="password123"
        )
        
        assert request.name == "<PERSON>"
        assert request.firstName == "John"
        assert request.lastName == "Doe"
        assert request.email == "<EMAIL>"
        assert request.password == "password123"
    
    def test_full_signup_request(self):
        """Test signup request with all parameters."""
        request = SignupRequest(
            name="<PERSON>",
            firstName="<PERSON>",
            lastName="Doe",
            email="<EMAIL>",
            password="password123",
            phoneNumber="+1234567890",
            cityId="city_123",
            cityName="New York",
            city="New York",
            state="NY",
            zipCode="10001",
            streetAddressOne="123 Main St",
            streetAddressTwo="Apt 4B",
            role="citizen",
            assignedRole="resident"
        )
        
        assert request.name == "John Doe"
        assert request.email == "<EMAIL>"
        assert request.city == "New York"
        assert request.zipCode == "10001"


class TestAuthenticationServiceSignup:
    """Test AuthenticationService signup method."""
    
    @patch('app.services.auth_service.requests.post')
    def test_successful_signup(self, mock_post):
        """Test successful signup API call."""
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "user_id": "user_123",
            "message": "User created successfully"
        }
        mock_post.return_value = mock_response
        
        # Create a SignupRequest object with all required fields
        signup_data = SignupRequest(
            name="John Doe",
            firstName="John",
            lastName="Doe",
            email="<EMAIL>",
            password="password123",
            phoneNumber="+1234567890",
            cityId="city_123",
            cityName="New York",
            city="New York",
            state="NY",
            zipCode="10001",
            streetAddressOne="123 Main St",
            role="citizen",
            assignedRole="resident",
            userType="individual",
            registeredFrom="web_app",
            userAddedFrom="self_registration"
        )
        
        auth_service = AuthenticationService()
        result = auth_service.signup(signup_data)
        
        # Check the response structure matches what AuthenticationService.signup() returns
        assert "status" in result
        assert "message" in result
        assert "data" in result
        assert result["status"] == "success"
        assert "success" in result["data"]
        assert result["data"]["success"] is True
        assert "user_id" in result["data"], f"Expected 'user_id' in data, got {result['data'].keys()}"
        assert result["data"]["user_id"] == "user_123"
        assert "Signup completed successfully" in result["message"]
    
    @patch('app.services.auth_service.requests.post')
    def test_signup_api_failure(self, mock_post):
        """Test signup API failure."""
        # Mock failed response
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.json.return_value = {
            "success": False,
            "error": "Invalid request"
        }
        mock_post.return_value = mock_response
        
        # Create a SignupRequest object with valid format but will fail in the API
        signup_data = SignupRequest(
            name="John Doe",
            firstName="John",
            lastName="Doe",
            email="<EMAIL>",  # Valid email format
            password="validpassword123"  # Valid password
        )
        
        auth_service = AuthenticationService()

        # The signup method should raise an ExternalServiceError for API failures
        from app.core.exceptions import ExternalServiceError
        with pytest.raises(ExternalServiceError) as exc_info:
            auth_service.signup(signup_data)

        # Check that the exception contains the expected error message
        assert "Invalid request" in str(exc_info.value)
        assert exc_info.value.status_code == 400


class TestLoginFlow:
    """Test login flow and authentication."""
    
    def test_successful_login(self, client, mock_auth_service):
        """Test successful login flow."""
        # Mock successful login
        mock_auth_service.login.return_value = {
            "status": "success",
            "message": "Login successful",
            "data": {
                "success": True,
                "token": "test-token-123",
                "user": {"id": "user_123", "email": "<EMAIL>"}
            }
        }
        
        # Start login flow - use the correct endpoint path with /auth prefix
        response = client.post(
            "/auth/login",
            data={
                "username": "<EMAIL>",
                "password": "password123"
            },
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
    
    def test_invalid_credentials(self, client, mock_auth_service):
        """Test login with invalid credentials."""
        from app.core.exceptions import AuthenticationError
        
        # Mock failed login with AuthenticationError
        mock_auth_service.login.side_effect = AuthenticationError(
            message="Invalid credentials",
            details={"email": "<EMAIL>"},
            status_code=401
        )
        
        response = client.post(
            "/auth/login",
            data={
                "username": "<EMAIL>",
                "password": "wrongpass"
            },
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        data = response.json()
        assert "message" in data
        assert data["message"] == "Incorrect username or password"


# Fixtures for the tests
@pytest.fixture
def client():
    """Test client fixture."""
    from fastapi.testclient import TestClient
    from app.main import app
    return TestClient(app)

@pytest.fixture
def mock_auth_service():
    """Mock auth service fixture."""
    with patch('app.api.auth.auth_service') as mock, \
         patch('app.services.auth_service.auth_service', mock):
        yield mock

if __name__ == "__main__":
    pytest.main([__file__])
