@import "tailwindcss";

/* ==========================================
   ⚡ Modern Design System (2025 Edition)
   Primary: Tech Blue | Secondary: Electric Violet | Accent: Emerald
   Neutral tones and soft gradients
   ========================================== */

:root {
  /* Light mode (default) */
  --background: 0 0% 100%; /* White */
  --foreground: 222 47% 11%; /* Deep slate text */

  --card: 0 0% 100%;
  --card-foreground: 222 47% 11%;

  --popover: 0 0% 100%;
  --popover-foreground: 222 47% 11%;

  --primary: 210 90% 56%; /* Modern blue (#1e90ff) */
  --primary-foreground: 0 0% 100%;
  --primary-light: 210 90% 95%; /* Light blue background */
  --primary-dark: 210 90% 40%; /* Dark blue for hover */

  --secondary: 268 71% 60%; /* Electric violet (#9b51e0) */
  --secondary-foreground: 0 0% 100%;
  --secondary-light: 268 71% 95%; /* Light violet background */
  --secondary-dark: 268 71% 45%; /* Dark violet for hover */

  --muted: 220 14% 96%; /* Soft gray background */
  --muted-foreground: 220 9% 46%;

  --accent: 158 64% 52%; /* Emerald (#10b981) */
  --accent-foreground: 0 0% 100%;
  --accent-light: 158 64% 95%; /* Light emerald background */
  --accent-dark: 158 64% 40%; /* Dark emerald for hover */

  --success: 142 71% 45%; /* Green */
  --success-foreground: 0 0% 100%;
  --success-light: 142 71% 95%;
  --success-dark: 142 71% 35%;

  --warning: 45 93% 47%; /* Orange/Yellow */
  --warning-foreground: 0 0% 100%;
  --warning-light: 45 93% 95%;
  --warning-dark: 45 93% 35%;

  --destructive: 0 84% 60%; /* Red */
  --destructive-foreground: 0 0% 100%;
  --destructive-light: 0 84% 95%;
  --destructive-dark: 0 84% 45%;

  --info: 199 89% 48%; /* Info blue */
  --info-foreground: 0 0% 100%;
  --info-light: 199 89% 95%;
  --info-dark: 199 89% 35%;

  --border: 220 13% 91%;
  --input: 0 0% 100%;
  --ring: 210 90% 56%; /* Matches primary blue */
  --radius: 0.75rem;

  /* Neutral shades for consistent grays */
  --neutral-50: 220 14% 98%;
  --neutral-100: 220 14% 96%;
  --neutral-200: 220 13% 91%;
  --neutral-300: 220 13% 82%;
  --neutral-400: 220 9% 64%;
  --neutral-500: 220 9% 46%;
  --neutral-600: 220 13% 36%;
  --neutral-700: 220 17% 26%;
  --neutral-800: 222 47% 16%;
  --neutral-900: 222 47% 11%;
}

/* ==========================================
   🌙 Dark Mode — Futuristic & Soft Contrast
   ========================================== */
.dark {
  --background: 222 47% 8%; /* Deep slate */
  --foreground: 210 40% 96%; /* Soft light gray */

  --card: 222 47% 10%;
  --card-foreground: 210 40% 96%;

  --popover: 222 47% 10%;
  --popover-foreground: 210 40% 96%;

  --primary: 210 90% 66%; /* Bright blue pop */
  --primary-foreground: 222 47% 8%;
  --primary-light: 210 90% 20%;
  --primary-dark: 210 90% 75%;

  --secondary: 268 71% 70%; /* Vibrant violet */
  --secondary-foreground: 222 47% 8%;
  --secondary-light: 268 71% 20%;
  --secondary-dark: 268 71% 80%;

  --muted: 223 15% 20%; /* Muted gray surface */
  --muted-foreground: 220 10% 65%;

  --accent: 158 64% 60%; /* Neon emerald */
  --accent-foreground: 222 47% 8%;
  --accent-light: 158 64% 20%;
  --accent-dark: 158 64% 70%;

  --success: 142 71% 55%;
  --success-foreground: 222 47% 8%;
  --success-light: 142 71% 20%;
  --success-dark: 142 71% 65%;

  --warning: 45 93% 57%;
  --warning-foreground: 222 47% 8%;
  --warning-light: 45 93% 20%;
  --warning-dark: 45 93% 65%;

  --destructive: 0 80% 65%;
  --destructive-foreground: 0 0% 100%;
  --destructive-light: 0 80% 20%;
  --destructive-dark: 0 80% 75%;

  --info: 199 89% 58%;
  --info-foreground: 222 47% 8%;
  --info-light: 199 89% 20%;
  --info-dark: 199 89% 68%;

  --border: 223 15% 20%;
  --input: 223 15% 18%;
  --ring: 210 90% 66%;

  /* Neutral shades for dark mode */
  --neutral-50: 222 47% 11%;
  --neutral-100: 222 47% 16%;
  --neutral-200: 220 17% 26%;
  --neutral-300: 220 13% 36%;
  --neutral-400: 220 9% 46%;
  --neutral-500: 220 9% 64%;
  --neutral-600: 220 13% 82%;
  --neutral-700: 220 13% 91%;
  --neutral-800: 220 14% 96%;
  --neutral-900: 220 14% 98%;
}

@theme inline {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* ==========================================
   🧩 Base Styles
   ========================================== */

* {
  border-color: hsl(var(--border));
}

body {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: var(--font-geist-sans), Inter, system-ui, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ==========================================
   🖱️ Custom Scrollbar (Modern)
   ========================================== */

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}

/* ==========================================
   ✨ Focus & Animation Utilities
   ========================================== */

.focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

.animate-message-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-typing {
  animation: typing 1.5s ease-in-out infinite;
}

/* ==========================================
   🌅 Gradients
   ========================================== */

.bg-modern-gradient {
  background: linear-gradient(
    135deg,
    hsl(var(--primary)) 0%,
    hsl(var(--secondary)) 100%
  );
}

.bg-modern-gradient-soft {
  background: linear-gradient(
    135deg,
    hsl(var(--muted)) 0%,
    hsl(var(--background)) 100%
  );
}

/* ==========================================
   🎬 Keyframes
   ========================================== */

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

/* ==========================================
   🎨 Color Utility Classes
   ========================================== */

/* Primary Colors */
.bg-primary { background-color: hsl(var(--primary)); }
.bg-primary-light { background-color: hsl(var(--primary-light)); }
.bg-primary-dark { background-color: hsl(var(--primary-dark)); }
.text-primary { color: hsl(var(--primary)); }
.text-primary-foreground { color: hsl(var(--primary-foreground)); }
.border-primary { border-color: hsl(var(--primary)); }
.hover\:bg-primary:hover { background-color: hsl(var(--primary)); }
.hover\:bg-primary-dark:hover { background-color: hsl(var(--primary-dark)); }

/* Secondary Colors */
.bg-secondary { background-color: hsl(var(--secondary)); }
.bg-secondary-light { background-color: hsl(var(--secondary-light)); }
.bg-secondary-dark { background-color: hsl(var(--secondary-dark)); }
.text-secondary { color: hsl(var(--secondary)); }
.text-secondary-foreground { color: hsl(var(--secondary-foreground)); }
.border-secondary { border-color: hsl(var(--secondary)); }
.hover\:bg-secondary:hover { background-color: hsl(var(--secondary)); }
.hover\:bg-secondary-dark:hover { background-color: hsl(var(--secondary-dark)); }

/* Accent Colors */
.bg-accent { background-color: hsl(var(--accent)); }
.bg-accent-light { background-color: hsl(var(--accent-light)); }
.bg-accent-dark { background-color: hsl(var(--accent-dark)); }
.text-accent { color: hsl(var(--accent)); }
.text-accent-foreground { color: hsl(var(--accent-foreground)); }
.border-accent { border-color: hsl(var(--accent)); }

/* Success Colors */
.bg-success { background-color: hsl(var(--success)); }
.bg-success-light { background-color: hsl(var(--success-light)); }
.bg-success-dark { background-color: hsl(var(--success-dark)); }
.text-success { color: hsl(var(--success)); }
.text-success-foreground { color: hsl(var(--success-foreground)); }
.border-success { border-color: hsl(var(--success)); }

/* Warning Colors */
.bg-warning { background-color: hsl(var(--warning)); }
.bg-warning-light { background-color: hsl(var(--warning-light)); }
.bg-warning-dark { background-color: hsl(var(--warning-dark)); }
.text-warning { color: hsl(var(--warning)); }
.text-warning-foreground { color: hsl(var(--warning-foreground)); }
.border-warning { border-color: hsl(var(--warning)); }

/* Destructive/Error Colors */
.bg-destructive { background-color: hsl(var(--destructive)); }
.bg-destructive-light { background-color: hsl(var(--destructive-light)); }
.bg-destructive-dark { background-color: hsl(var(--destructive-dark)); }
.text-destructive { color: hsl(var(--destructive)); }
.text-destructive-foreground { color: hsl(var(--destructive-foreground)); }
.border-destructive { border-color: hsl(var(--destructive)); }

/* Info Colors */
.bg-info { background-color: hsl(var(--info)); }
.bg-info-light { background-color: hsl(var(--info-light)); }
.bg-info-dark { background-color: hsl(var(--info-dark)); }
.text-info { color: hsl(var(--info)); }
.text-info-foreground { color: hsl(var(--info-foreground)); }
.border-info { border-color: hsl(var(--info)); }

/* Neutral Colors */
.bg-neutral-50 { background-color: hsl(var(--neutral-50)); }
.bg-neutral-100 { background-color: hsl(var(--neutral-100)); }
.bg-neutral-200 { background-color: hsl(var(--neutral-200)); }
.bg-neutral-300 { background-color: hsl(var(--neutral-300)); }
.bg-neutral-400 { background-color: hsl(var(--neutral-400)); }
.bg-neutral-500 { background-color: hsl(var(--neutral-500)); }
.bg-neutral-600 { background-color: hsl(var(--neutral-600)); }
.bg-neutral-700 { background-color: hsl(var(--neutral-700)); }
.bg-neutral-800 { background-color: hsl(var(--neutral-800)); }
.bg-neutral-900 { background-color: hsl(var(--neutral-900)); }

.text-neutral-50 { color: hsl(var(--neutral-50)); }
.text-neutral-100 { color: hsl(var(--neutral-100)); }
.text-neutral-200 { color: hsl(var(--neutral-200)); }
.text-neutral-300 { color: hsl(var(--neutral-300)); }
.text-neutral-400 { color: hsl(var(--neutral-400)); }
.text-neutral-500 { color: hsl(var(--neutral-500)); }
.text-neutral-600 { color: hsl(var(--neutral-600)); }
.text-neutral-700 { color: hsl(var(--neutral-700)); }
.text-neutral-800 { color: hsl(var(--neutral-800)); }
.text-neutral-900 { color: hsl(var(--neutral-900)); }

.border-neutral-100 { border-color: hsl(var(--neutral-100)); }
.border-neutral-200 { border-color: hsl(var(--neutral-200)); }
.border-neutral-300 { border-color: hsl(var(--neutral-300)); }

.hover\:bg-neutral-50:hover { background-color: hsl(var(--neutral-50)); }
.hover\:bg-neutral-100:hover { background-color: hsl(var(--neutral-100)); }
.hover\:bg-neutral-200:hover { background-color: hsl(var(--neutral-200)); }
.hover\:text-neutral-700:hover { color: hsl(var(--neutral-700)); }
.hover\:text-neutral-900:hover { color: hsl(var(--neutral-900)); }
