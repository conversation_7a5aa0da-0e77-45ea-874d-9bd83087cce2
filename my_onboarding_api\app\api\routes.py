"""
API routes for the onboarding application.

This module defines all the API endpoints with proper error handling and validation.
"""

from ..core.config import settings

from typing import Union
from typing import Annotated
from fastapi import APIR<PERSON>er, <PERSON><PERSON>, HTTPException, status, Depends
from fastapi.security import <PERSON>Auth2Pass<PERSON><PERSON>earer
from pydantic import ValidationError

from ..core.logging import get_logger
from ..core.exceptions import (
    OnboardingAPIException,
    ExternalServiceError,
    IntentClassificationError,
    SessionError,
    AuthenticationError,
    create_validation_error,
    internal_server_error
)
from ..models.requests import MessageRequest, LoginRequest
from ..models.responses import (
    OnboardingResponse,
    GeminiChatResponse,
    CombinedChatResponse,
    GeminiModelsResponse,
    SessionFlowResponse,
    AuthenticationResponse,
    SignupResponse
)
from ..models.session import FlowType
from ..services.intent_service import intent_service
from ..services.gemini_service import gemini_service
from ..services.auth_service import auth_service
from ..services.session_service import session_service
from ..services.orchestrator_client import send_to_orchestrator, is_orchestrator_available

# Import activity and submission routers
from .endpoints import activities, submissions

# Import auth router
from . import auth

logger = get_logger(__name__)

# Create main router
router = APIRouter()

# Health check endpoint
@router.get("/health", include_in_schema=False)
async def health_check():
    """Health check endpoint."""
    try:
        # Check if Gemini service is initialized
        gemini_ready = gemini_service.is_service_ready()
        intent_ready = intent_service.is_pipeline_ready()
        
        return {
            "status": "ok" if gemini_ready and intent_ready else "error",
            "services": {
                "gemini": "ready" if gemini_ready else "not ready",
                "intent_classification": "ready" if intent_ready else "not ready"
            },
            "config": {
                "gemini_model": settings.gemini_model,
                "gemini_ready": gemini_ready,
                "intent_model": settings.model_name if hasattr(settings, 'model_name') else 'not set'
            }
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "details": str(e.__class__.__name__)
        }

# Include all routers
router.include_router(auth.router, prefix="/auth", tags=["authentication"])
router.include_router(activities.router, prefix="/activities", tags=["activities"])
router.include_router(submissions.router, prefix="/submissions", tags=["submissions"])


@router.post("/onboarding", response_model=OnboardingResponse)
async def onboarding_intent(msg: MessageRequest) -> OnboardingResponse:
    """
    Classify user intent using Hugging Face model.
    
    Args:
        msg: Message request containing user input
        
    Returns:
        Intent classification results
        
    Raises:
        HTTPException: If classification fails
    """
    try:
        logger.info(f"Processing intent classification for message: {msg.message[:50]}...")
        
        # Classify intent
        intent_results = intent_service.classify_intent(msg.message)
        
        # Convert intent results to list of dicts for JSON serialization
        intent_dicts = [
            {"label": result.label, "score": float(result.score)}
            for result in intent_results
        ]
        
        logger.info(f"Intent classification completed. Top intent: {intent_dicts[0]['label']}")
        
        return {
            "success": True,
            "input": msg.message,
            "intent": intent_dicts,
            "message": "Intent classification completed successfully"
        }
        
    except IntentClassificationError as e:
        logger.error(f"Intent classification error: {str(e)}")
        logger.error(f"Error details: {e.details}")
        error_detail = {
            "error": "Intent classification failed",
            "message": str(e),
            "details": str(e.details) if e.details else None
        }
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_detail
        )
    except ValueError as e:
        logger.error(f"Value error in intent classification: {str(e)}")
        error_detail = {
            "error": "Invalid input",
            "message": str(e)
        }
        if settings.debug:
            error_detail["details"] = str(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_detail
        )
    except Exception as e:
        logger.error(f"Unexpected error in intent classification: {str(e)}")
        error_detail = {
            "error": "Internal Server Error",
            "message": "Failed to process intent classification"
        }
        if settings.debug:
            error_detail["details"] = str(e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_detail
        )


@router.post("/gemini-chat", response_model=GeminiChatResponse)
async def gemini_chat(msg: MessageRequest) -> GeminiChatResponse:
    """
    Generate chat response using Gemini AI.
    
    Args:
        msg: Message request containing user input
        
    Returns:
        Gemini AI response
        
    Raises:
        HTTPException: If chat generation fails
    """
    try:
        logger.info(f"Processing Gemini chat for message: {msg.message[:50]}...")
        
        # Generate response
        response_text = gemini_service.generate_response(msg.message)
        
        logger.info("Gemini chat response generated successfully")
        
        return {
            "success": True,
            "input": msg.message,
            "response": response_text,
            "message": "Chat response generated successfully"
        }
        
    except ExternalServiceError as e:
        logger.error(f"Gemini service error: {str(e)}")
        error_detail = {
            "error": "Gemini service unavailable",
            "message": str(e),
            "service": getattr(e, "service_name", "gemini")
        }
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=error_detail
        )
    except ValueError as e:
        logger.error(f"Invalid input in Gemini chat: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error": "Invalid input",
                "message": str(e)
            }
        )
    except Exception as e:
        logger.error(f"Unexpected error in Gemini chat: {str(e)}")
        error_detail = {
            "error": "Internal Server Error",
            "message": "Failed to generate chat response"
        }
        if settings.debug:
            error_detail["details"] = str(e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_detail
        )


@router.post("/gemini-chat-with-intent", response_model=Union[CombinedChatResponse, SessionFlowResponse, AuthenticationResponse, SignupResponse])
async def gemini_chat_with_intent(
    msg: MessageRequest,
        session_id: Annotated[str, Header(alias="session_id")]
) -> Union[CombinedChatResponse, SessionFlowResponse, AuthenticationResponse, SignupResponse]:
    """
    Handle chat with intent detection and session-based flows.
    
    Args:
        msg: Message request containing user input
        session_id: Session identifier from header
        
    Returns:
        Combined response with intent detection and chat, or flow-specific response
        
    Raises:
        HTTPException: If processing fails
    """
    logger.info(f"[DEBUG] Starting gemini_chat_with_intent for session: {session_id}")
    logger.info(f"[DEBUG] Message received: {msg.message}")
    
    try:
        logger.info(f"[DEBUG] Checking for active flow for session: {session_id}")
        # Check if there's an active flow
        if session_service.is_flow_active(session_id):
            logger.info(f"[DEBUG] Active flow found, handling flow for session: {session_id}")
            return _handle_active_flow(session_id, msg.message)
        
        logger.info("[DEBUG] No active flow, starting intent classification")
        # No active flow - perform intent classification
        try:
            detected_intent = intent_service.get_top_intent(msg.message)
            logger.info(f"[DEBUG] Intent classification successful. Detected intent: {detected_intent}")
        except Exception as e:
            logger.error(f"[ERROR] Intent classification failed: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "Intent classification failed",
                    "message": str(e),
                    "type": type(e).__name__
                }
            )
        
        # Handle intent-based flow initiation
        if detected_intent == "signup":
            logger.info(f"[DEBUG] Starting signup flow for session: {session_id}")
            session_service.start_flow(session_id, FlowType.SIGNUP)
            flow_message = session_service.get_flow_message(session_id)

            # Get the actual current step from the session
            session = session_service.get_session(session_id)
            current_step = session.flow_step.value if session.flow_step else "name"
            logger.info(f"[DEBUG] Signup flow started. Current step: {current_step}")

            return SessionFlowResponse(
                message=flow_message,
                flow_step=current_step,
                flow_type="signup"
            )
        
        elif detected_intent == "login":
            logger.info(f"[DEBUG] Starting login flow for session: {session_id}")
            session_service.start_flow(session_id, FlowType.LOGIN)
            flow_message = session_service.get_flow_message(session_id)
            logger.info("[DEBUG] Login flow started")
            
            return SessionFlowResponse(
                message=flow_message,
                flow_step="email",
                flow_type="login"
            )
        
        # Check if this is an activity intent
        elif detected_intent.startswith("activity_"):
            logger.info(f"[DEBUG] Processing activity intent: {detected_intent}")
            from ..services.activity_service import activity_service
            
            try:
                # Strip the 'activity_' prefix to get the actual intent
                activity_type = detected_intent.replace("activity_", "")
                activities_data = await activity_service.get_activities(activity_type, session_id)
                
                logger.info(f"[DEBUG] Retrieved {len(activities_data)} activities from service")
                
                # Generate a response that includes both activities and Gemini's interpretation
                gemini_response = gemini_service.generate_chat_response(
                    user_message=msg.message,
                    detected_intent=detected_intent,
                    additional_context={"activities": activities_data}
                )
                
                return {
                    "success": True,
                    "input": msg.message,
                    "detected_intent": detected_intent,
                    "activities": activities_data,
                    "gemini_response": gemini_response,
                    "message": "Activity response generated successfully"
                }
                
            except ExternalServiceError as e:
                logger.error(f"Activity service error: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail={
                        "error": "Activity service unavailable",
                        "message": str(e),
                        "service": "activities"
                    }
                )
        
        # Generate regular chat response
        logger.info("[DEBUG] Generating chat response with Gemini")
        try:
            gemini_response = gemini_service.generate_chat_response(
                user_message=msg.message,
                detected_intent=detected_intent
            )
            logger.info("[DEBUG] Gemini response generated successfully")
            
            return {
                "success": True,
                "input": msg.message,
                "detected_intent": detected_intent,
                "gemini_response": gemini_response,
                "message": "Response generated successfully"
            }
            
        except ValueError as e:
            logger.error(f"Invalid input in chat with intent: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "error": "Invalid input",
                    "message": str(e)
                }
            )
        
    except AuthenticationError as e:
        logger.error(f"Authentication error in combined chat: {str(e)}")
        status_code = status.HTTP_401_UNAUTHORIZED
        error_detail = {
            "error": "Authentication failed",
            "message": str(e)
        }
        if hasattr(e, "details"):
            error_detail["details"] = str(e.details) if e.details else None
        raise HTTPException(status_code=status_code, detail=error_detail)
        
    except SessionError as e:
        logger.error(f"Session error in combined chat: {str(e)}")
        status_code = status.HTTP_400_BAD_REQUEST
        error_detail = {
            "error": "Session error",
            "message": str(e)
        }
        if hasattr(e, "details"):
            error_detail["details"] = str(e.details) if e.details else None
        raise HTTPException(status_code=status_code, detail=error_detail)
        
    except ExternalServiceError as e:
        logger.error(f"External service error in combined chat: {str(e)}")
        status_code = getattr(e, "status_code", status.HTTP_503_SERVICE_UNAVAILABLE)
        error_detail = {
            "error": "Service unavailable",
            "message": str(e),
            "service": getattr(e, "service_name", "external")
        }
        if hasattr(e, "details"):
            error_detail["details"] = str(e.details) if e.details else None
        raise HTTPException(status_code=status_code, detail=error_detail)
        
    except Exception as e:
        import uuid
        error_id = str(uuid.uuid4())
        logger.error(f"Unexpected error in combined chat (ID: {error_id}): {str(e)}", exc_info=True)
        error_detail = {
            "error": "Internal Server Error",
            "message": "An unexpected error occurred"
        }
        if settings.debug:
            error_detail["details"] = str(e)
        error_message = f"An unexpected error occurred (ID: {error_id})"
        if settings.debug:
            error_message = f"{str(e)}\nError ID: {error_id}"
            
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Internal server error",
                "error_type": "internal_error",
                "message": error_message,
                "error_id": error_id,
                "details": {"error": str(e)} if settings.debug else None
            }
        )


@router.post("/chat-orchestrated", response_model=Union[CombinedChatResponse, SessionFlowResponse, AuthenticationResponse, SignupResponse])
async def chat_orchestrated(
    msg: MessageRequest,
    session_id: Annotated[str, Header(alias="session_id")]
) -> Union[CombinedChatResponse, SessionFlowResponse, AuthenticationResponse, SignupResponse]:
    """
    Handle chat with external orchestrator for intent detection and routing.
    
    This endpoint delegates conversation orchestration to the standalone
    MVP Conversation Orchestrator service instead of using internal logic.
    
    Args:
        msg: Message request containing user input
        session_id: Session identifier from header
        
    Returns:
        Orchestrator response (format depends on detected intent and routing)
        
    Raises:
        HTTPException: If orchestrator is unavailable or processing fails
    """
    logger.info(f"[ChatOrchestrated] Processing message via orchestrator for session: {session_id}")
    logger.info(f"[ChatOrchestrated] Message: {msg.message[:50]}...")
    
    # Check if orchestrator is enabled
    if not settings.use_orchestrator:
        logger.warning("[ChatOrchestrated] Orchestrator disabled, falling back to legacy endpoint")
        raise HTTPException(
            status_code=503,
            detail={
                "error": "Orchestrator disabled",
                "message": "External orchestrator is disabled. Use /gemini-chat-with-intent instead."
            }
        )
    
    try:
        # Check if there's an active flow - if so, handle it locally
        if session_service.is_flow_active(session_id):
            logger.info(f"[ChatOrchestrated] Active flow detected, handling locally for session: {session_id}")
            return _handle_active_flow(session_id, msg.message)
        
        # No active flow - delegate to orchestrator
        logger.info("[ChatOrchestrated] No active flow, delegating to orchestrator")
        
        try:
            orchestrator_response = await send_to_orchestrator(
                user_id=session_id,  # Using session_id as user_id for now
                text=msg.message,
                session_id=session_id
            )
            
            logger.info(f"[ChatOrchestrated] Orchestrator response received")
            logger.info(f"[ChatOrchestrated] Intent: {orchestrator_response.get('intent')}")
            logger.info(f"[ChatOrchestrated] Routed to: {orchestrator_response.get('routed_to')}")
            
            # Extract orchestrator response
            detected_intent = orchestrator_response.get('intent', 'general')
            routed_to = orchestrator_response.get('routed_to', 'unknown')
            orchestrator_message = orchestrator_response.get('message', '')
            orchestrator_data = orchestrator_response.get('data', {})
            
            # Log the delegation
            logger.info(f"[ChatOrchestrated] ✓ Orchestrator delegated to {routed_to} MCP")
            logger.info(f"[ChatOrchestrated] ✓ Detected intent: {detected_intent}")
            
            # Handle intent-based flow initiation based on orchestrator response
            if detected_intent == "onboarding" and routed_to == "onboarding_mcp":
                # Check if it's signup or login from orchestrator data
                flow_type_from_orchestrator = orchestrator_data.get('flow_type', '')
                
                if flow_type_from_orchestrator == 'signup':
                    logger.info(f"[ChatOrchestrated] Starting signup flow for session: {session_id}")
                    session_service.start_flow(session_id, FlowType.SIGNUP)
                    flow_message = session_service.get_flow_message(session_id)
                    session = session_service.get_session(session_id)
                    current_step = session.flow_step.value if session.flow_step else "name"
                    
                    return SessionFlowResponse(
                        message=flow_message,
                        flow_step=current_step,
                        flow_type="signup"
                    )
                    
                elif flow_type_from_orchestrator == 'login':
                    logger.info(f"[ChatOrchestrated] Starting login flow for session: {session_id}")
                    session_service.start_flow(session_id, FlowType.LOGIN)
                    flow_message = session_service.get_flow_message(session_id)
                    
                    return SessionFlowResponse(
                        message=flow_message,
                        flow_step="email",
                        flow_type="login"
                    )
            
            # For activity or general intents, return orchestrator response as-is
            logger.info("[ChatOrchestrated] Returning orchestrator response to frontend")
            
            return CombinedChatResponse(
                success=orchestrator_response.get('success', True),
                input=msg.message,
                detected_intent=detected_intent,
                gemini_response=orchestrator_message,
                message=f"Response from {routed_to}: {orchestrator_message}"
            )
            
        except Exception as e:
            logger.error(f"[ChatOrchestrated] Error calling orchestrator: {str(e)}", exc_info=True)
            
            # Check if orchestrator is available
            if not is_orchestrator_available():
                raise HTTPException(
                    status_code=503,
                    detail={
                        "error": "Orchestrator unavailable",
                        "message": "External orchestrator service is not available. Please ensure it's running at " + settings.orchestrator_url
                    }
                )
            
            # Re-raise the exception
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "Orchestrator error",
                    "message": str(e)
                }
            )
    
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
        
    except Exception as e:
        logger.error(f"[ChatOrchestrated] Unexpected error: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Internal server error",
                "message": "An unexpected error occurred while processing your request"
            }
        )


def _handle_active_flow(session_id: str, message: str) -> Union[SessionFlowResponse, AuthenticationResponse, SignupResponse]:
    """
    Handle active flow processing.

    Args:
        session_id: Session identifier
        message: User message

    Returns:
        Flow-specific response

    Raises:
        SessionError: If session handling fails
        AuthenticationError: If authentication fails
        ValidationError: If data validation fails
    """
    try:
        session = session_service.get_session(session_id)
        current_step = session.flow_step
        flow_type = session.flow_type

        logger.debug(f"Handling active flow: {flow_type}, step: {current_step}")

        # Determine field name based on current step
        field_name = current_step.value if current_step else "unknown"

        # Advance the flow (this may raise SessionError for validation failures)
        try:
            next_step = session_service.advance_flow(session_id, field_name, message)
        except SessionError as e:
            # Handle field validation errors during flow advancement
            logger.warning(f"Field validation failed during flow advancement: {str(e)}")

            # Return a flow response asking the user to re-enter the field
            flow_message = session_service.get_flow_message(session_id)
            error_message = f"{str(e)} {flow_message}"

            return SessionFlowResponse(
                message=error_message,
                flow_step=current_step.value,  # Stay on the same step
                flow_type=flow_type.value
            )
    
        if next_step:
            # Flow continues
            flow_message = session_service.get_flow_message(session_id)
            return SessionFlowResponse(
                message=flow_message,
                flow_step=next_step.value,
                flow_type=flow_type.value
            )
        else:
            # Flow complete - process the collected data
            logger.info(f"FLOW DEBUG: Flow completed for session {session_id}")
            user_data = session_service.complete_flow(session_id)
            logger.info(f"FLOW DEBUG: Collected user data fields: {list(user_data.to_dict().keys())}")

            if flow_type == FlowType.SIGNUP:
                # Validate required fields for signup
                # Check if we have either full name OR first/last name, plus email and password
                has_name = user_data.name or (user_data.firstName and user_data.lastName)
                if not has_name or not user_data.email or not user_data.password:
                    logger.error(f"Missing required signup data: name={user_data.name}, firstName={user_data.firstName}, lastName={user_data.lastName}, email={user_data.email}, password={'***' if user_data.password else None}")
                    raise SessionError("Missing required signup information")

                from ..models.requests import SignupRequest
                from pydantic import ValidationError

                try:
                    # Attempt to create and validate the signup request with all parameters
                    # Handle name field logic - use collected firstName/lastName or derive from name
                    first_name = user_data.firstName
                    last_name = user_data.lastName
                    full_name = user_data.name

                    # If we have firstName and lastName but no full name, construct it
                    if not full_name and (first_name or last_name):
                        full_name = f"{first_name or ''} {last_name or ''}".strip()

                    # If we have full name but missing firstName/lastName, try to derive them
                    if full_name and not first_name:
                        name_parts = full_name.split()
                        first_name = name_parts[0] if name_parts else None
                        if not last_name and len(name_parts) > 1:
                            last_name = name_parts[-1]

                    signup_request = SignupRequest(
                        name=full_name,
                        firstName=first_name,
                        lastName=last_name,
                        email=user_data.email,
                        password=user_data.password,
                        phoneNumber=getattr(user_data, 'phoneNumber', None),
                        cityId=getattr(user_data, 'cityId', None),
                        cityName=getattr(user_data, 'cityName', None),
                        city=getattr(user_data, 'city', None),
                        state=getattr(user_data, 'state', None),
                        zipCode=getattr(user_data, 'zipCode', None),
                        streetAddressOne=getattr(user_data, 'streetAddressOne', None),
                        streetAddressTwo=getattr(user_data, 'streetAddressTwo', None),
                        role=getattr(user_data, 'role', None),
                        assignedRole=getattr(user_data, 'assignedRole', None),
                        userType=getattr(user_data, 'userType', None),
                        type=getattr(user_data, 'type', None),
                        registeredFrom=getattr(user_data, 'registeredFrom', None) or "web_app",
                        userAddedFrom=getattr(user_data, 'userAddedFrom', None) or "self_registration",
                        createdBy=getattr(user_data, 'createdBy', None),
                        isStakeholder=getattr(user_data, 'isStakeholder', None) or False,
                        isAssociated=getattr(user_data, 'isAssociated', None) or False,
                        gender=getattr(user_data, 'gender', None),
                        birthday=getattr(user_data, 'birthday', None),
                        citiesArray=getattr(user_data, 'citiesArray', None),
                        cityNamesArray=getattr(user_data, 'cityNamesArray', None)
                    )
                except ValidationError as e:
                    # Handle validation errors (e.g., invalid email format)
                    logger.warning(f"Signup validation failed for session {session_id}: {str(e)}")

                    # Extract the specific validation error
                    error_details = e.errors()[0] if e.errors() else {}
                    field_name = error_details.get('loc', ['unknown'])[-1]
                    error_msg = error_details.get('msg', 'Invalid input')

                    if field_name == 'email':
                        error_message = "Please provide a valid email address (e.g., <EMAIL>)"
                    elif field_name == 'password':
                        error_message = "Please provide a valid password (minimum 8 characters)"
                    elif field_name in ['name', 'firstName', 'lastName']:
                        error_message = "Please provide a valid name"
                    elif field_name == 'phoneNumber':
                        error_message = "Please provide a valid phone number"
                    elif field_name == 'zipCode':
                        error_message = "Please provide a valid ZIP code"
                    elif field_name == 'birthday':
                        error_message = "Please provide a valid birthday in YYYY-MM-DD format"
                    elif field_name == 'citiesArray':
                        error_message = "Please provide valid city IDs"
                    elif field_name == 'cityNamesArray':
                        error_message = "Please provide valid city names"
                    elif field_name == 'cityName':
                        error_message = "Please provide a valid city name"
                    else:
                        error_message = f"Invalid {field_name}: {error_msg}"

                    return SignupResponse(
                        success=False,
                        status="error",
                        message=error_message,
                        user=None
                    )

                logger.info(f"Calling signup API for session: {session_id}, email: {signup_request.email}")
                result = auth_service.signup(signup_request)
                logger.info(f"Signup API returned for session: {session_id}, result status: {result.get('status')}")
                
                # Create the response object with proper structure
                response = SignupResponse(
                    success=result.get("status") == "success",
                    status=result.get("status", "error"),
                    message=result.get("message", ""),
                    user=result.get("user", {})
                )
                
                logger.debug(f"Signup response: {response.dict()}")
                return response

            elif flow_type == FlowType.LOGIN:
                # Validate required fields for login
                if not user_data.email or not user_data.password:
                    logger.error(f"Missing required login data: email={user_data.email}, password={'***' if user_data.password else None}")
                    raise SessionError("Missing required login information")

                from ..models.requests import LoginRequest
                from pydantic import ValidationError

                try:
                    # Attempt to create and validate the login request
                    login_request = LoginRequest(
                        email=user_data.email,
                        password=user_data.password
                    )
                except ValidationError as e:
                    # Handle validation errors (e.g., invalid email format)
                    logger.warning(f"Login validation failed for session {session_id}: {str(e)}")

                    # Extract the specific validation error
                    error_details = e.errors()[0] if e.errors() else {}
                    field_name = error_details.get('loc', ['unknown'])[-1]
                    error_msg = error_details.get('msg', 'Invalid input')

                    if field_name == 'email':
                        error_message = "Please provide a valid email address (e.g., <EMAIL>)"
                    elif field_name == 'password':
                        error_message = "Please provide a valid password"
                    else:
                        error_message = f"Invalid {field_name}: {error_msg}"

                    return AuthenticationResponse(
                        success=False,
                        status="error",
                        message=error_message,
                        data={"field": field_name, "validation_error": error_msg}
                    )

                try:
                    result = auth_service.login(login_request)
                    # Create the response object with proper structure
                    response = AuthenticationResponse(
                        success=result.get("status") == "success",
                        status=result.get("status", "error"),
                        message=result.get("message", ""),
                        data=result.get("data", {})
                    )
                    logger.debug(f"Login response: {response.dict()}")
                    return response
                except ExternalServiceError as e:
                    logger.error(f"External service error during login: {str(e)}")
                    # Forward the upstream auth service response if available
                    error_details = getattr(e, "details", {})
                    status_code = getattr(e, "status_code", 500)
                    
                    # Check if we have a response_data with a user-friendly message
                    if isinstance(error_details, dict) and "response_data" in error_details:
                        response_data = error_details["response_data"]
                        if isinstance(response_data, dict):
                            # Extract the user-friendly message if available
                            user_message = response_data.get('message', 'An error occurred during login')
                            return AuthenticationResponse(
                                success=False,
                                status="error",
                                message=user_message,
                                data=response_data
                            )
                        # If response_data is not a dict, use it as the message
                        return AuthenticationResponse(
                            success=False,
                            status="error",
                            message=str(response_data),
                            data={"error": str(e), "details": error_details}
                        )
                    
                    # Fallback: return a generic error response
                    return AuthenticationResponse(
                        success=False,
                        status="error",
                        message="An error occurred during login. Please try again.",
                        data={"error": str(e), "details": error_details}
                    )
                    
                except Exception as e:
                    logger.error(f"Unexpected error during login: {str(e)}", exc_info=True)
                    return AuthenticationResponse(
                        success=False,
                        status="error",
                        message="An unexpected error occurred during login. Please try again.",
                        data={
                            "error": str(e),
                            "type": type(e).__name__
                        }
                    )
                    logger.debug(f"Login error response: {response.dict()}")
                    return response

            else:
                logger.error(f"Unknown flow type: {flow_type}")
                raise SessionError(f"Unknown flow type: {flow_type}")

    except ExternalServiceError as e:
        logger.error(f"External service error in flow handling: {str(e)}")
        # If the error originates from the authentication service, forward upstream response
        if "authentication" in str(e).lower() or getattr(e, "service_name", "") == "authentication":
            error_details = getattr(e, "details", {})
            status_code = getattr(e, "status_code", 401)

            if isinstance(error_details, dict) and "response_data" in error_details:
                response_data = error_details["response_data"]
                # Raise HTTPException with upstream status and exact payload
                raise HTTPException(status_code=status_code, detail=response_data)

            # Fallback: raise HTTPException with available details
            raise HTTPException(
                status_code=status_code,
                detail={
                    "error": str(e),
                    "details": error_details
                }
            )
        
        # For other external service errors, return as session error
        return AuthenticationResponse(
            success=False,
            status="error",
            message=f"Service error: {str(e)}",
            data={
                "error": str(e),
                "service": getattr(e, "service_name", "unknown"),
                "status_code": getattr(e, "status_code", 500)
            }
        )
        
    except (SessionError, AuthenticationError) as e:
        logger.error(f"Flow handling error: {str(e)}")
        error_details = getattr(e, "details", {"message": str(e), "type": e.__class__.__name__})
        return AuthenticationResponse(
            success=False,
            status="error",
            message=str(e),
            data=error_details
        )
        
    except Exception as e:
        logger.error(f"Unexpected error in flow handling: {str(e)}")
        import traceback
        error_trace = traceback.format_exc()
        logger.error(f"Traceback: {error_trace}")
        
        return AuthenticationResponse(
            success=False,
            status="error",
            message="An unexpected error occurred. Please try again.",
            data={
                "message": str(e),
                "type": e.__class__.__name__,
                "traceback": error_trace if settings.debug else None
            }
        )


@router.post("/debug-intent", response_model=dict)
async def debug_intent_classification(msg: MessageRequest) -> dict:
    """
    Debug endpoint to test intent classification pipeline.

    Args:
        msg: Message request containing user input

    Returns:
        Debug information about the classification process
    """
    try:
        logger.info(f"Debug: Processing intent classification for: {msg.message[:50]}...")

        # Check if pipeline is ready
        if not intent_service.is_pipeline_ready():
            return {
                "error": "Pipeline not ready",
                "pipeline_status": "not_initialized"
            }

        # Get raw pipeline output for debugging
        raw_results = intent_service._pipeline(msg.message.strip())

        return {
            "input": msg.message,
            "raw_results_type": str(type(raw_results)),
            "raw_results": raw_results,
            "pipeline_status": "ready"
        }

    except Exception as e:
        logger.error(f"Debug endpoint error: {str(e)}")
        import traceback
        return {
            "error": str(e),
            "traceback": traceback.format_exc(),
            "input": msg.message
        }


@router.get("/gemini-models", response_model=GeminiModelsResponse)
async def list_gemini_models() -> GeminiModelsResponse:
    """
    List available Gemini models.
    
    Returns:
        List of available Gemini models
        
    Raises:
        HTTPException: If model listing fails
    """
    try:
        logger.info("Fetching available Gemini models")
        
        models = gemini_service.list_available_models()
        
        logger.info(f"Retrieved {len(models)} Gemini models")
        
        return GeminiModelsResponse(
            available_models=models,
            message=f"Retrieved {len(models)} available models"
        )
        
    except ExternalServiceError as e:
        logger.error(f"Failed to list Gemini models: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={
                "error": "Gemini service unavailable",
                "message": str(e),
                "service": e.service_name
            }
        )
    except Exception as e:
        logger.error(f"Unexpected error listing Gemini models: {str(e)}")
        raise internal_server_error("Failed to list Gemini models")
