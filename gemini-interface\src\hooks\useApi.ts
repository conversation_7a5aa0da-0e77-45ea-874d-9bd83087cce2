import { useMutation, useQuery, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import apiClient from '@/lib/api/apiClient';

type QueryKey = (string | number)[];

export const useApiQuery = <T>(
  key: QueryKey,
  url: string,
  options?: Omit<UseQueryOptions<T, Error, T, QueryKey>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: key,
    queryFn: async () => {
      const { data } = await apiClient.get<T>(url);
      return data;
    },
    ...options,
  });
};

export const useApiMutation = <T, V = void>(
  url: string,
  method: 'post' | 'put' | 'delete' | 'patch' = 'post',
  options?: {
    onSuccess?: (data: T) => void;
    onError?: (error: Error) => void;
    invalidateQueries?: QueryKey[];
  }
) => {
  const queryClient = useQueryClient();

  return useMutation<T, Error, V>({
    mutationFn: async (variables) => {
      const { data } = await apiClient.request<T>({
        method,
        url,
        data: variables,
      });
      return data;
    },
    onSuccess: (data) => {
      // Invalidate and refetch specified queries
      if (options?.invalidateQueries?.length) {
        options.invalidateQueries.forEach((queryKey) => {
          queryClient.invalidateQueries({ queryKey });
        });
      }
      options?.onSuccess?.(data);
    },
    onError: (error) => {
      options?.onError?.(error);
    },
  });
};
