"""Tool: Get Rewards."""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "common"))

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
import logging

from common.models.base import BaseResponse
from ..services.rewards_service import RewardsService

logger = logging.getLogger(__name__)
router = APIRouter()
rewards_service = RewardsService()


class GetRewardsRequest(BaseModel):
    """Get rewards request model."""
    user_id: str = Field(..., description="User ID")


@router.post("/get_rewards", response_model=BaseResponse)
async def get_rewards(request: GetRewardsRequest):
    """
    Get user's reward points and history.
    
    This tool retrieves the user's current reward balance and recent transaction history.
    
    Args:
        request: Get rewards request
        
    Returns:
        BaseResponse with reward data
    """
    try:
        logger.info(f"Getting rewards for user: {request.user_id}")
        
        # Get reward balance
        reward = await rewards_service.get_user_rewards(request.user_id)
        
        if not reward:
            raise HTTPException(status_code=404, detail="Rewards not found")
        
        # Get transaction history
        transactions = await rewards_service.get_transaction_history(request.user_id, limit=10)
        
        transactions_data = [
            {
                "id": t.id,
                "points": t.points,
                "type": t.transaction_type,
                "description": t.description,
                "created_at": t.created_at.isoformat()
            }
            for t in transactions
        ]
        
        logger.info(f"Rewards retrieved: {reward.available_points} points available")
        
        return BaseResponse(
            success=True,
            message="Rewards retrieved successfully",
            data={
                "user_id": reward.user_id,
                "total_points": reward.total_points,
                "available_points": reward.available_points,
                "redeemed_points": reward.redeemed_points,
                "recent_transactions": transactions_data
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting rewards: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get rewards: {str(e)}"
        )
