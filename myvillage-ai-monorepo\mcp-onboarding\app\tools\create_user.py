"""Tool: Create User Account."""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "common"))

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, EmailStr, Field
import logging

from common.models.base import BaseResponse
from ..services.auth_service import AuthService

logger = logging.getLogger(__name__)
router = APIRouter()
auth_service = AuthService()


class CreateUserRequest(BaseModel):
    """Create user request model."""
    name: str = Field(..., min_length=1, max_length=100, description="Full name of the user")
    email: EmailStr = Field(..., description="Email address of the user")
    password: str = Field(..., min_length=8, description="Password (minimum 8 characters)")
    phone_number: str | None = Field(None, description="Optional phone number")


@router.post("/create_user", response_model=BaseResponse)
async def create_user(request: CreateUserRequest):
    """
    Create a new user account.
    
    This tool creates a new user in the system with the provided information.
    The password is securely hashed before storage.
    
    Args:
        request: User creation request
        
    Returns:
        BaseResponse with user data
        
    Raises:
        HTTPException: If user creation fails
    """
    try:
        logger.info(f"Creating user with email: {request.email}")
        
        user = await auth_service.create_user(
            name=request.name,
            email=request.email,
            password=request.password,
            phone_number=request.phone_number
        )
        
        logger.info(f"User created successfully: {user.id}")
        
        return BaseResponse(
            success=True,
            message="User created successfully",
            data={
                "user_id": user.id,
                "email": user.email,
                "name": user.name,
                "is_verified": user.is_verified
            }
        )
    except ValueError as e:
        logger.warning(f"User creation failed: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error creating user: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create user: {str(e)}"
        )
