# Quick Start: Using the Orchestrator

## 🚀 5-Minute Setup

### Step 1: Configure Environment

Add to your `.env` file:

```env
ORCHESTRATOR_URL=http://localhost:8100/chat
USE_ORCHESTRATOR=true
```

### Step 2: Start the Orchestrator

```bash
cd mvp-conversation-orchestrator
python -m app.main
```

Verify it's running:
```bash
curl http://localhost:8100/health
```

Expected response:
```json
{
  "status": "healthy",
  "service": "MVP Conversation Orchestrator",
  "version": "1.0.0"
}
```

### Step 3: Start Your Backend

```bash
cd my_onboarding_api
python main.py
```

### Step 4: Test the Integration

```bash
python test_orchestrator_integration.py
```

Expected output:
```
✅ Orchestrator is available and healthy
✅ Test 1: Signup Intent - PASSED
✅ Test 2: Login Intent - PASSED
✅ Test 3: Activity Intent - PASSED
✅ Test 4: General Query - PASSED
✅ ALL TESTS PASSED!
```

## 📡 API Usage

### New Endpoint

**URL:** `POST /api/v1/chat-orchestrated`

**Headers:**
```
Content-Type: application/json
session_id: <your-session-id>
```

**Body:**
```json
{
  "message": "I want to sign up"
}
```

### Example with curl

```bash
curl -X POST http://localhost:8000/api/v1/chat-orchestrated \
  -H "Content-Type: application/json" \
  -H "session_id: my-session-123" \
  -d '{"message": "I want to sign up"}'
```

### Example with Python

```python
import requests

response = requests.post(
    'http://localhost:8000/api/v1/chat-orchestrated',
    headers={
        'Content-Type': 'application/json',
        'session_id': 'my-session-123'
    },
    json={'message': 'I want to sign up'}
)

print(response.json())
```

### Example with JavaScript/TypeScript

```typescript
const response = await fetch('/api/v1/chat-orchestrated', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'session_id': sessionId
  },
  body: JSON.stringify({ message: 'I want to sign up' })
});

const data = await response.json();
console.log(data);
```

## 🔍 What Gets Logged

When you send a message, you'll see:

```
[ChatOrchestrated] Processing message via orchestrator for session: my-session-123
[ChatOrchestrated] Message: I want to sign up...
[OrchestratorClient] Delegating message to orchestrator
[OrchestratorClient] Orchestrator routed to: onboarding_mcp
[OrchestratorClient] Detected intent: onboarding
[ChatOrchestrated] ✓ Orchestrator delegated to onboarding_mcp MCP
[ChatOrchestrated] Starting signup flow
```

## ⚙️ Configuration Options

### Enable/Disable Orchestrator

```env
# Use orchestrator (recommended)
USE_ORCHESTRATOR=true

# Use internal routing (fallback)
USE_ORCHESTRATOR=false
```

### Change Orchestrator URL

```env
# Local development
ORCHESTRATOR_URL=http://localhost:8100/chat

# Docker Compose
ORCHESTRATOR_URL=http://orchestrator:8100/chat

# Production
ORCHESTRATOR_URL=https://orchestrator.myvillage.com/chat
```

## 🐛 Troubleshooting

### "Orchestrator unavailable"

**Problem:** Orchestrator service is not running

**Solution:**
```bash
cd mvp-conversation-orchestrator
python -m app.main
```

### "Connection refused"

**Problem:** Wrong URL or port

**Solution:** Check `ORCHESTRATOR_URL` in `.env` matches the running service

### "Orchestrator disabled"

**Problem:** `USE_ORCHESTRATOR=false` in `.env`

**Solution:** Set `USE_ORCHESTRATOR=true` and restart backend

## 📊 Response Formats

### Signup/Login Response

```json
{
  "message": "Great! Let's get you signed up. What's your name?",
  "flow_step": "name",
  "flow_type": "signup"
}
```

### Activity Response

```json
{
  "success": true,
  "input": "Show me activities",
  "detected_intent": "activity",
  "gemini_response": "Here are some activities...",
  "message": "Response from activity_mcp: Here are some activities..."
}
```

### Error Response

```json
{
  "detail": {
    "error": "Orchestrator unavailable",
    "message": "External orchestrator service is not available..."
  }
}
```

## 🎯 Quick Tests

### Test Signup
```bash
curl -X POST http://localhost:8000/api/v1/chat-orchestrated \
  -H "Content-Type: application/json" \
  -H "session_id: test-1" \
  -d '{"message": "I want to sign up"}'
```

### Test Login
```bash
curl -X POST http://localhost:8000/api/v1/chat-orchestrated \
  -H "Content-Type: application/json" \
  -H "session_id: test-2" \
  -d '{"message": "I need to log in"}'
```

### Test Activity
```bash
curl -X POST http://localhost:8000/api/v1/chat-orchestrated \
  -H "Content-Type: application/json" \
  -H "session_id: test-3" \
  -d '{"message": "Show me activities"}'
```

## 📚 More Information

- **Full Integration Guide:** `ORCHESTRATOR_INTEGRATION.md`
- **Integration Summary:** `INTEGRATION_SUMMARY.md`
- **Orchestrator Docs:** `../mvp-conversation-orchestrator/README.md`

---

**Ready to go!** 🚀
