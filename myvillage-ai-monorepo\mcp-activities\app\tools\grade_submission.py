"""Tool: Grade Submission."""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "common"))

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
import logging

from common.models.base import BaseResponse
from ..services.submission_service import SubmissionService

logger = logging.getLogger(__name__)
router = APIRouter()
submission_service = SubmissionService()


class GradeSubmissionRequest(BaseModel):
    """Grade submission request model."""
    submission_id: str = Field(..., description="Submission ID")
    grade: float = Field(..., ge=0, le=100, description="Grade (0-100)")
    feedback: str = Field(..., description="Feedback for the submission")
    grader_id: str = Field(..., description="ID of the grader")


@router.post("/grade_submission", response_model=BaseResponse)
async def grade_submission(request: GradeSubmissionRequest):
    """
    Grade a submission.
    
    This tool assigns a grade and feedback to a submission.
    The submission status is automatically updated to 'graded'.
    
    Args:
        request: Grading request
        
    Returns:
        BaseResponse with graded submission data
        
    Raises:
        HTTPException: If grading fails
    """
    try:
        logger.info(f"Grading submission: {request.submission_id} by grader: {request.grader_id}")
        
        # Grade submission
        submission = await submission_service.grade_submission(
            submission_id=request.submission_id,
            grade=request.grade,
            feedback=request.feedback,
            grader_id=request.grader_id
        )
        
        if not submission:
            raise HTTPException(status_code=404, detail="Submission not found")
        
        logger.info(f"Submission graded successfully: {submission.id} - Grade: {submission.grade}")
        
        return BaseResponse(
            success=True,
            message="Submission graded successfully",
            data={
                "submission_id": submission.id,
                "activity_id": submission.activity_id,
                "user_id": submission.user_id,
                "grade": submission.grade,
                "feedback": submission.feedback,
                "graded_by": submission.graded_by,
                "graded_at": submission.graded_at.isoformat() if submission.graded_at else None,
                "status": submission.status.value
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error grading submission: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to grade submission: {str(e)}"
        )
