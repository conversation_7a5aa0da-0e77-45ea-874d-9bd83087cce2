"""Onboarding MCP client - handles signup/login flows."""

import logging
import httpx
from typing import Dict, Any
from ..core.config import settings

logger = logging.getLogger(__name__)


class OnboardingClient:
    """Client for OnboardingMCP service - calls real MCP API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.timeout = 30.0
        logger.info(f"[OnboardingClient] Initialized with base_url: {base_url}")
    
    async def process_message(
        self, 
        user_id: str, 
        text: str, 
        session_id: str = None
    ) -> Dict[str, Any]:
        """
        Process onboarding-related message by calling the real MCP service.
        
        Args:
            user_id: User identifier
            text: User message
            session_id: Optional session ID
            
        Returns:
            Response from OnboardingMCP service
        """
        logger.info(f"[OnboardingClient] Processing message for user: {user_id}")
        logger.debug(f"[OnboardingClient] Message text: '{text}'")
        logger.debug(f"[OnboardingClient] Session ID: {session_id}")
        
        try:
            # Call the real onboarding MCP service
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                url = f"{self.base_url}/api/v1/gemini-chat-with-intent"
                headers = {
                    "Content-Type": "application/json",
                    "session_id": session_id or user_id
                }
                payload = {"message": text}
                
                logger.info(f"[OnboardingClient] Calling MCP at {url}")
                logger.debug(f"[OnboardingClient] Headers: {headers}")
                logger.debug(f"[OnboardingClient] Payload: {payload}")
                
                response = await client.post(url, json=payload, headers=headers)
                response.raise_for_status()
                
                data = response.json()
                logger.info(f"[OnboardingClient] Received response from MCP")
                logger.debug(f"[OnboardingClient] Response data: {data}")
                
                # Transform the response to match our expected format
                return {
                    "success": data.get("status") == "success",
                    "message": data.get("message", ""),
                    "data": {
                        "flow_type": data.get("flow_type"),
                        "current_step": data.get("flow_step"),
                        "session_id": session_id,
                        **data.get("data", {})
                    }
                }
                
        except httpx.HTTPError as e:
            logger.error(f"[OnboardingClient] HTTP error calling MCP: {str(e)}")
            return {
                "success": False,
                "message": f"Error connecting to onboarding service: {str(e)}",
                "data": {}
            }
        except Exception as e:
            logger.error(f"[OnboardingClient] Unexpected error: {str(e)}", exc_info=True)
            return {
                "success": False,
                "message": f"Unexpected error: {str(e)}",
                "data": {}
            }


# Singleton instance
onboarding_client = OnboardingClient(base_url=settings.onboarding_mcp_url)
