#!/bin/bash

# Restart MyVillage AI Services with selection

# Get the absolute path of the monorepo root
MONOREPO_ROOT="$(cd "$(dirname "$0")" && pwd)"

# Setup Python command
if command -v python &> /dev/null; then
    PYTHON_CMD=python
elif command -v python3 &> /dev/null; then
    PYTHON_CMD=python3
else
    echo "❌ ERROR: Python not found! Please install Python 3.11+"
    exit 1
fi

# Function to stop a service by port (from stop-all.sh)
stop_service_by_port() {
    local service_name=$1
    local port=$2
    
    echo "Stopping $service_name (port $port)..."
    
    # Find PIDs using the port on Windows
    local pids=$(netstat -ano | grep ":$port " | grep "LISTENING" | awk '{print $5}' | sort -u)
    
    if [ -z "$pids" ]; then
        echo "  ⚠️  No process found on port $port"
    else
        for pid in $pids; do
            echo "  🔍 Found PID: $pid"
            taskkill //PID $pid //F > /dev/null 2>&1
            if [ $? -eq 0 ]; then
                echo "  ✅ Killed process $pid"
            else
                echo "  ❌ Failed to kill process $pid"
            fi
        done
    fi
    echo ""
}

# Function to start a service (adapted from start-all.sh)
start_service_func() {
    local service_name=$1
    local service_dir=$2
    local port=$3
    
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "🚀 Starting $service_name"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # Check if directory exists
    if [ ! -d "$service_dir" ]; then
        echo "❌ ERROR: Directory $service_dir not found!"
        return 1
    fi
    
    # Check if app/main.py exists
    if [ ! -f "$service_dir/app/main.py" ]; then
        echo "❌ ERROR: $service_dir/app/main.py not found!"
        return 1
    fi
    
    # Load environment variables if .env exists
    if [ -f .env ]; then
        export $(cat .env | grep -v '^#' | xargs)
    fi
    
    # Add monorepo root to PYTHONPATH
    export PYTHONPATH="$MONOREPO_ROOT:$PYTHONPATH"
    
    mkdir -p logs
    
    echo "📂 Service directory: $service_dir"
    echo "🌐 Port: $port"
    echo "⏳ Starting process..."
    
    # Start the service
    cd "$MONOREPO_ROOT"
    $PYTHON_CMD -m "${service_dir/\//.}.app.main" > "logs/${service_name}.log" 2>&1 &
    local pid=$!
    
    echo "   🆔 Process ID: $pid"
    echo "   📝 Log file: logs/${service_name}.log"
    echo ""
    echo "⏳ Waiting for service to initialize (3 seconds)..."
    sleep 3
    
    # Check if process is still running
    if ps -p $pid > /dev/null 2>&1; then
        echo "✅ $service_name started successfully!"
        
        # Show first few lines of log
        echo ""
        echo "📄 Initial log output:"
        head -n 5 "logs/${service_name}.log" | sed 's/^/   │ /'
        echo "   └─ (see logs/${service_name}.log for full output)"
    else
        echo "❌ $service_name failed to start!"
        echo ""
        echo "📄 Error log (last 15 lines):"
        tail -n 15 "logs/${service_name}.log" | sed 's/^/   │ /'
        echo ""
        return 1
    fi
    echo ""
}

show_menu() {
    echo "========================================"
    echo "🔄 MyVillage AI Service Restarter"
    echo "========================================"
    echo "1) All Services"
    echo "2) Onboarding MCP (Port 8001)"
    echo "3) Activities MCP (Port 8002)"
    echo "4) Rewards MCP    (Port 8003)"
    echo "5) Chat MCP       (Port 8005)"
    echo "6) Orchestrator   (Port 8100)"
    echo "q) Quit"
    echo "========================================"
    read -p "Select a service to restart [1-6]: " choice
    echo ""
}

# Main logic
show_menu

case $choice in
    1)
        echo "🔄 Restarting ALL services..."
        echo ""
        echo "🛑 Stopping all services..."
        ./stop-all.sh
        echo ""
        echo "⏳ Waiting 3 seconds..."
        sleep 3
        echo ""
        echo "🚀 Starting all services..."
        ./start-all.sh
        ;;
    2)
        echo "🔄 Restarting Onboarding MCP..."
        stop_service_by_port "Onboarding MCP" "8001"
        sleep 1
        start_service_func "onboarding-mcp" "mcp-onboarding" "8001"
        ;;
    3)
        echo "🔄 Restarting Activities MCP..."
        stop_service_by_port "Activities MCP" "8002"
        sleep 1
        start_service_func "activities-mcp" "mcp-activities" "8002"
        ;;
    4)
        echo "🔄 Restarting Rewards MCP..."
        stop_service_by_port "Rewards MCP" "8003"
        sleep 1
        start_service_func "rewards-mcp" "mcp-rewards" "8003"
        ;;
    5)
        echo "🔄 Restarting Chat MCP..."
        stop_service_by_port "Chat MCP" "8005"
        sleep 1
        export API_PORT=8005
        start_service_func "chat-mcp" "mcp-chat" "8005"
        ;;
    6)
        echo "🔄 Restarting Orchestrator..."
        stop_service_by_port "Orchestrator" "8100"
        sleep 1
        start_service_func "orchestrator" "orchestrator" "8100"
        ;;
    q|Q)
        echo "Exiting..."
        exit 0
        ;;
    *)
        echo "❌ Invalid option"
        exit 1
        ;;
esac
