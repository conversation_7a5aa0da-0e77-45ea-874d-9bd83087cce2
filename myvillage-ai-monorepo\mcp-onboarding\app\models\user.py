"""User models."""
from pydantic import BaseModel, EmailStr, Field
from typing import Optional
from datetime import datetime
from enum import Enum


class UserRole(str, Enum):
    """User role enumeration."""
    USER = "user"
    ADMIN = "admin"
    MODERATOR = "moderator"
    SUPER_ADMIN = "super_admin"


class UserBase(BaseModel):
    """Base user model."""
    email: EmailStr
    name: str
    phone_number: Optional[str] = None
    role: UserRole = UserRole.USER


class UserCreate(BaseModel):
    """User creation model."""
    name: str = Field(..., min_length=1, max_length=100)
    email: EmailStr
    password: str = Field(..., min_length=8)
    phone_number: Optional[str] = None
    role: UserRole = UserRole.USER


class UserLogin(BaseModel):
    """User login model."""
    email: EmailStr
    password: str


class UserUpdate(BaseModel):
    """User update model."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    phone_number: Optional[str] = None
    role: Optional[UserRole] = None


class User(UserBase):
    """User model."""
    id: str
    created_at: datetime
    updated_at: datetime
    is_active: bool = True
    is_verified: bool = False

    class Config:
        from_attributes = True


class UserInDB(User):
    """User model with password hash."""
    password_hash: str
    password_salt: str
