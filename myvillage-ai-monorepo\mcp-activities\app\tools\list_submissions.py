"""List submissions tool."""
from fastapi import APIRouter, Query
from typing import Optional
import logging

from ..services.submission_service import SubmissionService
from common.models import BaseResponse

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/list_submissions")
async def list_submissions(
    activity_id: Optional[str] = Query(None, description="Filter by activity ID"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    status: Optional[str] = Query(None, description="Filter by status (draft, submitted, graded, returned)"),
    city_id: Optional[str] = Query(None, description="Filter by city ID"),
    limit: int = Query(10, ge=1, le=100, description="Maximum number of results")
):
    """
    List submissions with optional filters.
    
    Query Parameters:
    - activity_id: Filter submissions by activity
    - user_id: Filter submissions by user
    - status: Filter by submission status
    - city_id: Filter by city
    - limit: Maximum results (1-100, default: 10)
    
    Returns:
        List of submissions sorted by most recent
    """
    try:
        service = SubmissionService()
        
        # Get submissions with filters
        submissions = await service.list_submissions(
            activity_id=activity_id,
            user_id=user_id,
            status=status,
            city_id=city_id,
            limit=limit
        )
        
        # Sort by updated_at descending (most recent first)
        submissions_sorted = sorted(
            submissions,
            key=lambda x: x.updated_at,
            reverse=True
        )
        
        # Convert to dict for response
        submissions_data = [
            {
                "id": sub.id,
                "activity_id": sub.activity_id,
                "user_id": sub.user_id,
                "title": sub.title,
                "description": sub.description,
                "status": sub.status.value,
                "is_public": sub.is_public,
                "created_at": sub.created_at.isoformat() if hasattr(sub.created_at, 'isoformat') else sub.created_at,
                "updated_at": sub.updated_at.isoformat() if hasattr(sub.updated_at, 'isoformat') else sub.updated_at,
                "submitted_at": sub.submitted_at.isoformat() if sub.submitted_at and hasattr(sub.submitted_at, 'isoformat') else sub.submitted_at,
                "grade": sub.grade,
                "feedback": sub.feedback,
                "graded_by": sub.graded_by,
                "graded_at": sub.graded_at.isoformat() if sub.graded_at and hasattr(sub.graded_at, 'isoformat') else sub.graded_at,
                "likes_count": sub.likes_count
            }
            for sub in submissions_sorted
        ]
        
        # Build message
        if not submissions_data:
            message = "No submissions found"
            if activity_id:
                message += f" for activity {activity_id}"
            if user_id:
                message += f" by user {user_id}"
            if status:
                message += f" with status {status}"
        else:
            message = f"Found {len(submissions_data)} submission(s)"
        
        logger.info(f"Listed {len(submissions_data)} submissions")
        
        return BaseResponse(
            success=True,
            message=message,
            data={
                "submissions": submissions_data,
                "count": len(submissions_data)
            }
        )
    
    except Exception as e:
        logger.error(f"Error listing submissions: {e}")
        return BaseResponse(
            success=False,
            message=f"Failed to list submissions: {str(e)}",
            data=None
        )
