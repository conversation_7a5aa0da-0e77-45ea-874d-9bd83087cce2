"""Tool: Verify OTP."""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "common"))

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, EmailStr, Field
import logging

from common.models.base import BaseResponse
from ..services.user_service import UserService

logger = logging.getLogger(__name__)
router = APIRouter()
user_service = UserService()


class VerifyOTPRequest(BaseModel):
    """Verify OTP request model."""
    email: EmailStr = Field(..., description="User's email address")
    otp: str = Field(..., min_length=6, max_length=6, description="6-digit OTP code")


@router.post("/verify_otp", response_model=BaseResponse)
async def verify_otp(request: VerifyOTPRequest):
    """
    Verify OTP code for user.
    
    This tool verifies the OTP code sent to the user's email or phone.
    In a production system, this would check against a stored OTP.
    For now, it's a simplified implementation.
    
    Args:
        request: OTP verification request
        
    Returns:
        BaseResponse with verification status
        
    Raises:
        HTTPException: If verification fails
    """
    try:
        logger.info(f"OTP verification attempt for: {request.email}")
        
        # Get user
        user = await user_service.get_user_by_email(request.email)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # In production, verify OTP against stored value
        # For now, accept any 6-digit code as valid
        if len(request.otp) != 6 or not request.otp.isdigit():
            raise HTTPException(status_code=400, detail="Invalid OTP format")
        
        # Mark user as verified
        success = await user_service.verify_user(user.id)
        
        if not success:
            raise HTTPException(
                status_code=500,
                detail="Failed to verify user"
            )
        
        logger.info(f"OTP verified successfully for: {user.id}")
        
        return BaseResponse(
            success=True,
            message="OTP verified successfully",
            data={
                "user_id": user.id,
                "email": user.email,
                "is_verified": True
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error verifying OTP: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"OTP verification failed: {str(e)}"
        )
