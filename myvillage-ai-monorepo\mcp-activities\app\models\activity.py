"""Activity models."""
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum


class ActivityType(str, Enum):
    """Activity type enumeration."""
    ASSIGNMENT = "assignment"
    EVENT = "event"
    SURVEY = "survey"
    DISCUSSION = "discussion"
    VOLUNTEER = "volunteer"


class ActivityStatus(str, Enum):
    """Activity status enumeration."""
    DRAFT = "draft"
    PUBLISHED = "published"
    ARCHIVED = "archived"
    COMPLETED = "completed"


class ActivityBase(BaseModel):
    """Base activity model."""
    title: str = Field(..., min_length=1, max_length=200)
    description: str
    activity_type: ActivityType
    status: ActivityStatus = ActivityStatus.DRAFT
    city_id: Optional[str] = None


class ActivityCreate(BaseModel):
    """Activity creation model."""
    title: str = Field(..., min_length=1, max_length=200)
    description: str
    activity_type: ActivityType
    created_by: str
    city_id: Optional[str] = None
    due_date: Optional[datetime] = None
    points: Optional[int] = Field(None, ge=0)
    tags: Optional[List[str]] = []


class ActivityUpdate(BaseModel):
    """Activity update model."""
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    status: Optional[ActivityStatus] = None
    due_date: Optional[datetime] = None
    points: Optional[int] = Field(None, ge=0)
    tags: Optional[List[str]] = None


class Activity(ActivityBase):
    """Activity model."""
    id: str
    created_by: str
    created_at: datetime
    updated_at: datetime
    due_date: Optional[datetime] = None
    points: Optional[int] = 0
    tags: Optional[List[str]] = []
    submission_count: int = 0

    class Config:
        from_attributes = True
