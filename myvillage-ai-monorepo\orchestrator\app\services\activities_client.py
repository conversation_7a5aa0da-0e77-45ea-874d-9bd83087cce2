"""Real HTTP client for Activities MCP."""
import httpx
from typing import Dict, Any, Optional
import logging
from ..core.config import settings

logger = logging.getLogger(__name__)


class ActivitiesClient:
    """Client for calling Activities MCP service."""
    
    def __init__(self):
        self.base_url = settings.activities_mcp_url
        self.timeout = settings.mcp_timeout
    
    async def list_activities(
        self,
        activity_type: Optional[str] = None,
        status: Optional[str] = None,
        city_id: Optional[str] = None,
        limit: int = 10
    ) -> Dict[str, Any]:
        """Call list_activities tool on Activities MCP."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                logger.info(f"Calling activities MCP: list_activities (city_id={city_id})")
                params = {"limit": limit}
                if activity_type:
                    params["activity_type"] = activity_type
                if status:
                    params["status"] = status
                if city_id:
                    params["city_id"] = city_id
                
                response = await client.get(
                    f"{self.base_url}/tools/list_activities",
                    params=params
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"Error calling activities MCP: {e}")
                raise
    
    async def create_activity(
        self,
        title: str,
        description: str,
        activity_type: str,
        created_by: str
    ) -> Dict[str, Any]:
        """Call create_activity tool on Activities MCP."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                logger.info(f"Calling activities MCP: create_activity")
                response = await client.post(
                    f"{self.base_url}/tools/create_activity",
                    json={
                        "title": title,
                        "description": description,
                        "activity_type": activity_type,
                        "created_by": created_by
                    }
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"Error calling activities MCP: {e}")
                raise
    
    async def list_submissions(
        self,
        activity_id: Optional[str] = None,
        user_id: Optional[str] = None,
        status: Optional[str] = None,
        city_id: Optional[str] = None,
        limit: int = 10
    ) -> Dict[str, Any]:
        """Call list_submissions tool on Activities MCP."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                logger.info(f"Calling activities MCP: list_submissions")
                params = {"limit": limit}
                if activity_id:
                    params["activity_id"] = activity_id
                if user_id:
                    params["user_id"] = user_id
                if status:
                    params["status"] = status
                if city_id:
                    params["city_id"] = city_id
                
                response = await client.get(
                    f"{self.base_url}/tools/list_submissions",
                    params=params
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"Error calling activities MCP: {e}")
                raise
    
    async def list_cities(
        self,
        limit: int = 100
    ) -> Dict[str, Any]:
        """Call list_cities tool on Activities MCP."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                logger.info(f"Calling activities MCP: list_cities")
                params = {"limit": limit}
                
                response = await client.get(
                    f"{self.base_url}/tools/list_cities",
                    params=params
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"Error calling activities MCP: {e}")
                raise
    
    async def health_check(self) -> bool:
        """Check if activities MCP is healthy."""
        async with httpx.AsyncClient(timeout=5) as client:
            try:
                response = await client.get(f"{self.base_url}/health")
                return response.status_code == 200
            except:
                return False
