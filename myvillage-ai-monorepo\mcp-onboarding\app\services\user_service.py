"""User service."""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "common"))

from datetime import datetime
from typing import Optional
import logging

from ..models.user import User, UserUpdate
from ..core.database import get_db

logger = logging.getLogger(__name__)


class UserService:
    """User management service."""
    
    def __init__(self):
        """Initialize user service."""
        self.db = get_db()
        self.users_table = self.db.get_table("users")
    
    async def get_user(self, user_id: str) -> Optional[User]:
        """
        Get user by ID.
        
        Args:
            user_id: User's ID
            
        Returns:
            User if found, None otherwise
        """
        try:
            response = self.users_table.get_item(Key={"id": user_id})
            if "Item" in response:
                return User(**response["Item"])
            return None
        except Exception as e:
            logger.error(f"Error getting user: {e}")
            return None
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """
        Get user by email.
        
        Args:
            email: User's email
            
        Returns:
            User if found, None otherwise
        """
        try:
            response = self.users_table.get_item(Key={"email": email})
            if "Item" in response:
                return User(**response["Item"])
            return None
        except Exception as e:
            logger.error(f"Error getting user by email: {e}")
            return None
    
    async def update_user(
        self,
        user_id: str,
        user_update: UserUpdate
    ) -> Optional[User]:
        """
        Update user profile.
        
        Args:
            user_id: User's ID
            user_update: Update data
            
        Returns:
            Updated user if successful, None otherwise
        """
        try:
            # Get current user
            user = await self.get_user(user_id)
            if not user:
                return None
            
            # Build update expression
            update_data = user_update.dict(exclude_unset=True)
            if not update_data:
                return user
            
            update_data["updated_at"] = datetime.utcnow().isoformat()
            
            # Update user
            update_expression = "SET " + ", ".join(
                f"#{k} = :{k}" for k in update_data.keys()
            )
            expression_attribute_names = {
                f"#{k}": k for k in update_data.keys()
            }
            expression_attribute_values = {
                f":{k}": v for k, v in update_data.items()
            }
            
            response = self.users_table.update_item(
                Key={"id": user_id},
                UpdateExpression=update_expression,
                ExpressionAttributeNames=expression_attribute_names,
                ExpressionAttributeValues=expression_attribute_values,
                ReturnValues="ALL_NEW"
            )
            
            return User(**response["Attributes"])
        except Exception as e:
            logger.error(f"Error updating user: {e}")
            return None
    
    async def verify_user(self, user_id: str) -> bool:
        """
        Mark user as verified.
        
        Args:
            user_id: User's ID
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.users_table.update_item(
                Key={"id": user_id},
                UpdateExpression="SET is_verified = :verified, updated_at = :updated",
                ExpressionAttributeValues={
                    ":verified": True,
                    ":updated": datetime.utcnow().isoformat()
                }
            )
            return True
        except Exception as e:
            logger.error(f"Error verifying user: {e}")
            return False
