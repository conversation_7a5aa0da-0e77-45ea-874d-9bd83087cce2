# MCP Chat Service

Chat history management service for MyVillage AI.

## Overview

This service manages chat message storage and retrieval using DynamoDB. It provides endpoints for:
- Saving chat messages
- Retrieving chat history
- Listing conversations
- Deleting messages (soft delete)

## Architecture

- **FastAPI** application
- **DynamoDB** for data storage
- **MCP Tools** for integration with other services

## Endpoints

### POST /messages
Save a new chat message.

### POST /messages/history
Get chat history for a user.

### GET /conversations/{user_id}
Get list of conversations for a user.

### DELETE /messages/{message_id}
Soft delete a message.

### GET /.well-known/mcp-manifest.json
MCP manifest for service discovery.

## Environment Variables

- `AWS_REGION`: AWS region (default: us-east-1)
- `DYNAMODB_TABLE_NAME`: DynamoDB table name (default: ChatGPT)
- `API_HOST`: API host (default: 0.0.0.0)
- `API_PORT`: API port (default: 8000)

## Running Locally

```bash
# Install dependencies
pip install -r requirements.txt

# Run the service
python -m app.main
```

## Running with Docker

```bash
# Build image
docker build -t mcp-chat .

# Run container
docker run -p 8000:8000 \
  -e AWS_REGION=us-east-1 \
  -e DYNAMODB_TABLE_NAME=ChatGPT \
  mcp-chat
```

## DynamoDB Schema

Table: `ChatGPT`

- **Primary Key**: `id` (String)
- **Sort Key**: `createdAt` (String)
- **GSI**: `chatGPTByDate` (userId, createdAt)

Attributes:
- `id`: Message ID (UUID)
- `userId`: User ID
- `chatType`: Chat type (e.g., "GeneralWeb")
- `message`: Message content
- `role`: "user" or "assistant"
- `messageType`: "TEXT", "FILE", or "URL"
- `fileUrl`: Optional file URL
- `isDeleted`: "true" or "false"
- `createdAt`: ISO timestamp
