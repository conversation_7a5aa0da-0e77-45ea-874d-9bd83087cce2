"""Intent detection client - detects user intent from message."""

import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


class IntentClient:
    """Client for intent detection (mock implementation)."""
    
    # Keywords for intent detection
    ONBOARDING_KEYWORDS = {
        'sign up', 'signup', 'register', 'create account', 
        'new account', 'join', 'login', 'log in', 'sign in', 'authenticate',
        'need to log', 'want to log'
    }
    
    ACTIVITY_KEYWORDS = {
        'activity', 'activities', 'event', 'events', 
        'what can i do', 'things to do', 'submit', 'create activity'
    }
    
    async def detect_intent(self, text: str) -> Dict[str, Any]:
        """
        Detect user intent from message text.
        
        Args:
            text: User message text
            
        Returns:
            Dict with intent classification
        """
        logger.info(f"[IntentClient] Detecting intent for text: '{text[:50]}...'")
        
        text_lower = text.lower()
        
        # Check for onboarding keywords
        for keyword in self.ONBOARDING_KEYWORDS:
            if keyword in text_lower:
                logger.info(f"[IntentClient] Detected ONBOARDING intent (keyword: '{keyword}')")
                return {
                    "intent": "onboarding",
                    "confidence": 0.95,
                    "matched_keyword": keyword
                }
        
        # Check for activity keywords
        for keyword in self.ACTIVITY_KEYWORDS:
            if keyword in text_lower:
                logger.info(f"[IntentClient] Detected ACTIVITY intent (keyword: '{keyword}')")
                return {
                    "intent": "activity",
                    "confidence": 0.90,
                    "matched_keyword": keyword
                }
        
        # Default to general/activity
        logger.info("[IntentClient] No specific intent detected, defaulting to ACTIVITY")
        return {
            "intent": "activity",
            "confidence": 0.60,
            "matched_keyword": None
        }


# Singleton instance
intent_client = IntentClient()
