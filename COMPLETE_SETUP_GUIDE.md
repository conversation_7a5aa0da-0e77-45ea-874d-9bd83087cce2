# 🚀 MyVillage AI - Complete Setup Guide

Everything you need to run the full MyVillage AI system with chat UI.

---

## 📋 What You Have

### 1. Backend Services (myvillage-ai-monorepo/)
- **Orchestrator** - Main entry point (port 8100)
- **Onboarding MCP** - User authentication (port 8001)
- **Activities MCP** - Activities management (port 8002)
- **Rewards MCP** - Rewards system (port 8003)

### 2. Chat UI (myvillage-chat-ui/)
- ChatGPT-like interface
- Real-time chat with AI
- Intent detection display
- Service status monitoring

---

## 🎯 Quick Start (2 Commands)

### Terminal 1: Start Backend

```bash
cd myvillage-ai-monorepo
./start-all.sh
```

Wait for all ✅ health checks to pass.

### Terminal 2: Start Chat UI

```bash
cd myvillage-chat-ui
./start-ui.sh
```

### Open Browser

Visit: **http://localhost:3000**

---

## 🧪 Test the System

### 1. Activities Feature
```
User: "Show me available activities"
```
- Intent: `activities`
- Service: `activities-mcp`
- Port: 8002

### 2. Rewards Feature
```
User: "Check my rewards"
```
- Intent: `rewards`
- Service: `rewards-mcp`
- Port: 8003

### 3. Onboarding Feature
```
User: "I want to sign up"
```
- Intent: `onboarding`
- Service: `onboarding-mcp`
- Port: 8001

### 4. General Chat
```
User: "Hello, how are you?"
```
- Intent: `general`
- Service: `orchestrator`
- Port: 8100

---

## 📊 System Architecture

```
┌─────────────────────────────────────────┐
│     Chat UI (Next.js)                   │
│     http://localhost:3000               │
└──────────────┬──────────────────────────┘
               │
               ▼
┌─────────────────────────────────────────┐
│     Orchestrator (FastAPI)              │
│     http://localhost:8100               │
│     - Intent Detection (Gemini AI)      │
│     - Request Routing                   │
└──────────────┬──────────────────────────┘
               │
       ┌───────┴───────┬───────────┐
       ▼               ▼           ▼
┌──────────┐    ┌──────────┐  ┌──────────┐
│Onboarding│    │Activities│  │ Rewards  │
│   MCP    │    │   MCP    │  │   MCP    │
│  :8001   │    │  :8002   │  │  :8003   │
└──────────┘    └──────────┘  └──────────┘
```

---

## 🔧 Service Management

### Start All Services
```bash
cd myvillage-ai-monorepo
./start-all.sh
```

### Stop All Services
```bash
cd myvillage-ai-monorepo
./stop-all.sh
```

### Check Service Health
```bash
# All services
curl http://localhost:8100/health

# Individual services
curl http://localhost:8001/health  # Onboarding
curl http://localhost:8002/health  # Activities
curl http://localhost:8003/health  # Rewards
```

### View Logs
```bash
cd myvillage-ai-monorepo

# Real-time logs
tail -f logs/orchestrator.log
tail -f logs/onboarding-mcp.log
tail -f logs/activities-mcp.log
tail -f logs/rewards-mcp.log

# All logs
cat logs/*.log
```

---

## 🌐 Service URLs

| Service | URL | Purpose |
|---------|-----|---------|
| **Chat UI** | http://localhost:3000 | User interface |
| **Orchestrator** | http://localhost:8100 | Main API |
| **Onboarding MCP** | http://localhost:8001 | Auth service |
| **Activities MCP** | http://localhost:8002 | Activities |
| **Rewards MCP** | http://localhost:8003 | Rewards |

---

## 📁 Project Structure

```
myvillageai/
├── myvillage-ai-monorepo/          # Backend services
│   ├── orchestrator/               # Main orchestrator
│   ├── mcp-onboarding/            # Onboarding MCP
│   ├── mcp-activities/            # Activities MCP
│   ├── mcp-rewards/               # Rewards MCP
│   ├── common/                    # Shared code
│   ├── logs/                      # Service logs
│   ├── start-all.sh              # Start script
│   └── stop-all.sh               # Stop script
│
└── myvillage-chat-ui/             # Frontend chat UI
    ├── app/                       # Next.js app
    ├── components/                # React components
    ├── start-ui.sh               # Start script
    └── .env.local                # Configuration
```

---

## 🐛 Troubleshooting

### Services won't start

```bash
# Check if ports are in use
netstat -ano | findstr :8100
netstat -ano | findstr :8001
netstat -ano | findstr :8002
netstat -ano | findstr :8003

# Kill processes
taskkill /PID <PID> /F

# Or use stop script
./stop-all.sh
```

### Chat UI can't connect

1. Check backend is running:
   ```bash
   curl http://localhost:8100/health
   ```

2. Check CORS settings in orchestrator

3. Check `.env.local` in chat UI:
   ```env
   NEXT_PUBLIC_API_URL=http://localhost:8100
   ```

### Python module errors

```bash
cd myvillage-ai-monorepo

# Install dependencies for each service
cd mcp-onboarding && pip install -r requirements.txt
cd ../mcp-activities && pip install -r requirements.txt
cd ../mcp-rewards && pip install -r requirements.txt
cd ../orchestrator && pip install -r requirements.txt
```

### Node.js errors

```bash
cd myvillage-chat-ui

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

---

## 🔐 Environment Variables

### Backend (.env in myvillage-ai-monorepo/)

```env
DATABASE_URL=your-database-url
JWT_SECRET=your-secret-key
GEMINI_API_KEY=your-gemini-api-key
```

### Frontend (.env.local in myvillage-chat-ui/)

```env
NEXT_PUBLIC_API_URL=http://localhost:8100
```

---

## 🎨 Customization

### Change Chat UI Colors

Edit `myvillage-chat-ui/tailwind.config.ts`:
```typescript
colors: {
  primary: '#10a37f',
  secondary: '#19c37d',
}
```

### Add New Intent

1. Update orchestrator intent detection
2. Add new MCP service
3. Update routing logic
4. Test in chat UI

### Modify API Responses

Edit service files in `myvillage-ai-monorepo/*/app/routers/`

---

## 📚 Documentation

- **Backend Setup**: `myvillage-ai-monorepo/README.md`
- **Chat UI Setup**: `myvillage-chat-ui/SETUP.md`
- **API Docs**: Visit http://localhost:8100/docs (when running)

---

## 🎉 Success Checklist

- [ ] All backend services started (4 services)
- [ ] All health checks passing (✅ status 200)
- [ ] Chat UI running on port 3000
- [ ] Can send messages in chat
- [ ] Intent detection working
- [ ] MCP services responding
- [ ] Logs showing activity

---

## 🚀 Next Steps

1. **Test all features** using the chat UI
2. **Monitor logs** to see how requests flow
3. **Customize the UI** to match your brand
4. **Add new features** to MCP services
5. **Deploy to production** when ready

---

## 💡 Tips

- Keep logs open while testing: `tail -f logs/*.log`
- Use the chat UI's quick action buttons
- Check service status in the sidebar
- Watch for intent detection in messages
- Monitor which MCP service handles each request

---

## 🎯 You're Ready!

Your complete MyVillage AI system is set up and ready to use. Start chatting and testing all the features! 🚀

**Questions?** Check the logs or README files in each directory.
