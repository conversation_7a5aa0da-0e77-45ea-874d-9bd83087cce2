# MyVillage AI - Monorepo

A microservices-based architecture for MyVillage AI platform with orchestrator and MCP services.

## 🏗️ Architecture

```
Frontend → Orchestrator → MCP Services → Database
              ↓
        Intent Detection
              ↓
         Route to MCP
              ↓
      Unified Response
```

## 📁 Project Structure

```
myvillage-ai-monorepo/
├── orchestrator/          # Intent detection & routing
├── mcp-onboarding/       # User signup/login/verification
├── mcp-activities/       # Activity management & submissions
├── mcp-rewards/          # Rewards tracking & redemption
├── mcp-approval/         # Activity approval workflows
├── common/               # Shared code library
├── docker-compose.yml    # Multi-container orchestration
└── README.md            # This file
```

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- Docker & Docker Compose
- Git

### Setup

1. **Clone the repository**
   ```bash
   git clone <repo-url>
   cd myvillage-ai-monorepo
   ```

2. **Copy environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your actual values
   ```

3. **Start all services**
   ```bash
   docker-compose up -d
   ```

4. **Check service health**
   ```bash
   curl http://localhost:8100/health  # Orchestrator
   curl http://localhost:8001/health  # Onboarding MCP
   curl http://localhost:8002/health  # Activities MCP
   curl http://localhost:8003/health  # Rewards MCP
   curl http://localhost:8004/health  # Approval MCP
   ```

## 🎯 Services

| Service | Port | Description |
|---------|------|-------------|
| Orchestrator | 8100 | Intent detection and request routing |
| Onboarding MCP | 8001 | User authentication and profile management |
| Activities MCP | 8002 | Activity and submission management |
| Rewards MCP | 8003 | Rewards tracking and redemption |
| Approval MCP | 8004 | Activity approval workflows |

## 📚 Documentation

- [Architecture Guide](./ARCHITECTURE.md)
- [Deployment Guide](./DEPLOYMENT.md)
- [Migration Guide](./MONOREPO_MIGRATION_GUIDE.md)

## 🧪 Testing

```bash
# Run all tests
pytest

# Run tests for specific service
cd mcp-onboarding
pytest tests/

# Run integration tests
pytest tests/integration/
```

## 🛠️ Development

### Local Development (without Docker)

Each service can be run independently:

```bash
# Orchestrator
cd orchestrator
pip install -r requirements.txt
python -m app.main

# Onboarding MCP
cd mcp-onboarding
pip install -r requirements.txt
python -m app.main
```

### Docker Commands

```bash
# Build all services
docker-compose build

# Start services
docker-compose up -d

# View logs
docker-compose logs -f [service-name]

# Stop services
docker-compose down

# Restart a service
docker-compose restart [service-name]
```

## 📦 Deployment

See [DEPLOYMENT.md](./DEPLOYMENT.md) for detailed deployment instructions.

## 🤝 Contributing

1. Create a feature branch
2. Make your changes
3. Write tests
4. Submit a pull request

## 📄 License

MIT License

## 🙏 Acknowledgments

Built with FastAPI, Docker, and following microservices best practices.
