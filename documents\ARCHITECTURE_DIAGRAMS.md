# Architecture Diagrams & Visual Specifications
## MCP Modularization Visual Reference

---

## 1. Current Monolith Architecture

```
┌────────────────────────────────────────────────────────────────┐
│                    Next.js Frontend (Port 3000)                │
└─────────────────────────────┬────────────────────────────────┘
                              │
                       HTTP/REST (Port 80)
                              │
                              ▼
        ┌─────────────────────────────────────────────────────┐
        │           FastAPI Monolith (Port 8000)              │
        │                                                      │
        │  ┌──────────────────────────────────────────────┐   │
        │  │         routes.py - API Router               │   │
        │  │  POST /onboarding      ──┐                   │   │
        │  │  POST /gemini-chat     ──┼──→ Intent detect  │   │
        │  │  POST /auth/signup     ──┤    Auth flows     │   │
        │  │  POST /activities      ──┤    Activity CRUD  │   │
        │  │  etc.                  ──┘    Submissions    │   │
        │  └──────────────────────────────────────────────┘   │
        │                    ▲                                  │
        │                    │                                  │
        │  ┌─────────────────┴────────────────────────────┐   │
        │  │            Services Layer                     │   │
        │  ├─────────────────────────────────────────────┤   │
        │  │ • intent_service.py    (NLP - Hugging Face) │   │
        │  │ • gemini_service.py    (AI - Gemini API)    │   │
        │  │ • auth_service.py      (External Auth)      │   │
        │  │ • session_service.py   (Session Management) │   │
        │  │ • activity_service.py  (Activity CRUD)      │   │
        │  │ • submission_service.py (Submissions)       │   │
        │  │ • city_service.py      (City Data)          │   │
        │  └─────────────────────────────────────────────┘   │
        │                    ▲                                  │
        │                    │                                  │
        │  ┌─────────────────┴────────────────────────────┐   │
        │  │            External Dependencies              │   │
        │  ├─────────────────────────────────────────────┤   │
        │  │ • DynamoDB (Activities, Sessions)           │   │
        │  │ • External Auth API (Login/Signup)          │   │
        │  │ • City Service API                          │   │
        │  │ • Gemini AI API                             │   │
        │  │ • Hugging Face Models                       │   │
        │  └─────────────────────────────────────────────┘   │
        │                                                      │
        └──────────────────────────────────────────────────────┘

PROBLEMS:
❌ Single point of failure
❌ Monolithic scaling (all features scale together)
❌ Mixed concerns (auth + activities + chat + sessions)
❌ Hard to test services in isolation
❌ Deployment is all-or-nothing
❌ Performance: Intent classification blocks activity requests
```

---

## 2. Target MCP Architecture

```
┌──────────────────────────────────────────────────────────────────────┐
│                    Next.js Frontend (Port 3000)                      │
└──────────────────────────────┬──────────────────────────────────────┘
                               │
                        HTTP/REST or WebSocket
                               │
                               ▼
        ┌──────────────────────────────────────────────────────┐
        │  Conversation Orchestrator (Port 8000)               │
        │                                                       │
        │  Responsibilities:                                   │
        │  • Receive user message                              │
        │  • Detect intent from message                        │
        │  • Route to appropriate MCP                          │
        │  • Return unified response                           │
        │                                                       │
        │  Components:                                         │
        │  ├─ MessageRouter (routes based on intent)           │
        │  ├─ IntentDetector (quick intent detection)          │
        │  ├─ SessionManager (central context)                 │
        │  └─ MCPClient (HTTP client for MCPs)                 │
        │                                                       │
        └──────────────────────────────────────────────────────┘
           │              │                      │
           │              │                      │
    HTTP   │              │                      │   HTTP
    ▼      ▼              ▼                      ▼
    
┌──────────────────────┐  ┌──────────────────────┐  ┌──────────────────────┐
│  OnboardingMCP       │  │   ActivityMCP        │  │    ChatMCP           │
│  (Port 8001)         │  │   (Port 8002)        │  │   (Port 8003)        │
├──────────────────────┤  ├──────────────────────┤  ├──────────────────────┤
│ Routes:              │  │ Routes:              │  │ Routes:              │
│ • /session/init      │  │ • /activities/       │  │ • /chat/message      │
│ • /message           │  │ • /submissions/      │  │ • /classify-intent   │
│ • /session/state     │  │ • /classify          │  │ • /history           │
├──────────────────────┤  ├──────────────────────┤  ├──────────────────────┤
│ Services:            │  │ Services:            │  │ Services:            │
│ • auth_service       │  │ • activity_service   │  │ • gemini_service     │
│ • session_service    │  │ • submission_svc     │  │ • intent_classifier  │
│ • onboarding_flow    │  │ • activity_classifier│  │ • conversation_mgr   │
├──────────────────────┤  ├──────────────────────┤  ├──────────────────────┤
│ Data:                │  │ Data:                │  │ Data:                │
│ • DynamoDB (sessions)│  │ • DynamoDB           │  │ • In-memory (context)│
│ • External Auth API  │  │ • City Service API   │  │ • Gemini API         │
└──────────────────────┘  └──────────────────────┘  └──────────────────────┘

BENEFITS:
✅ Each MCP is independently deployable
✅ Services can scale independently
✅ Clear service boundaries
✅ Easy to test in isolation
✅ Different teams can own different MCPs
✅ Can use different technologies per MCP
✅ Easy to replace individual MCPs
```

---

## 3. Message Flow Diagram

```
User Input (Browser)
    │
    ▼
GET /message
  {
    "session_id": "uuid-123",
    "message": "I want to sign up"
  }

    │
    ▼

ORCHESTRATOR (/message endpoint)
    │
    ├─→ 1. Detect Intent
    │      MessageRouter.detect_intent("I want to sign up")
    │      → Returns: "signup" (confidence: 0.95)
    │
    ├─→ 2. Get Session Context
    │      SessionManager.get_session("uuid-123")
    │      → flow_type: null, flow_step: null
    │
    ├─→ 3. Determine Target MCP
    │      MessageRouter.route(intent="signup", flow_context={...})
    │      → Returns: "onboarding"
    │
    ├─→ 4. Build MCP Request
    │      POST http://localhost:8001/mcp/onboarding/message
    │      {
    │        "session_id": "uuid-123",
    │        "message": "I want to sign up",
    │        "detected_intent": "signup",
    │        "context": {
    │          "flow_type": null,
    │          "flow_step": null
    │        }
    │      }
    │
    ├─→ 5. Update Session
    │      SessionManager.update_flow("uuid-123", flow_type="signup", step="email")
    │
    ├─→ 6. Return Response
    │      {
    │        "success": true,
    │        "session_id": "uuid-123",
    │        "delegated_to": "onboarding",
    │        "intent_detected": "signup",
    │        "response": {
    │          "success": true,
    │          "type": "flow_response",
    │          "data": {
    │            "flow_type": "signup",
    │            "current_step": "email",
    │            "message": "What's your email?",
    │            "flow_state": {}
    │          }
    │        }
    │      }
    │
    ▼
Browser (Display Message to User)

---

USER PROVIDES EMAIL

    │
    ▼
POST /message
  {
    "session_id": "uuid-123",
    "message": "<EMAIL>"
  }

    │
    ▼

ORCHESTRATOR (/message endpoint)
    │
    ├─→ 1. Detect Intent
    │      → Returns: "general" (no explicit intent in message)
    │
    ├─→ 2. Get Session Context
    │      SessionManager.get_session("uuid-123")
    │      → flow_type: "signup", flow_step: "email"
    │
    ├─→ 3. Determine Target MCP
    │      MessageRouter.route(intent="general", flow_context={flow_type: "signup"})
    │      → Returns: "onboarding" (because active flow takes priority)
    │
    ├─→ 4. Send to OnboardingMCP
    │      POST http://localhost:8001/mcp/onboarding/message
    │      {...}
    │
    ├─→ 5. OnboardingMCP Processes
    │      SessionService.advance_flow("uuid-123", "email", "<EMAIL>")
    │      → Returns: "password" (next step)
    │
    ├─→ 6. Return Response
    │      {
    │        "success": true,
    │        "response": {
    │          "type": "flow_response",
    │          "data": {
    │            "current_step": "password",
    │            "message": "Create a password:"
    │          }
    │        }
    │      }
    │
    ▼
Browser (Display: "Create a password:")
```

---

## 4. Service Dependency Graph

### Current Monolith (Tight Coupling)
```
routes.py
    ├─→ intent_service
    ├─→ gemini_service
    ├─→ auth_service
    ├─→ session_service
    ├─→ activity_service
    ├─→ submission_service
    ├─→ city_service
    ├─→ DynamoDB
    ├─→ External APIs
    └─→ All services depend on each other's models

Result: Change one service → might break others
```

### Target MCP (Loose Coupling)
```
ORCHESTRATOR
    ├─→ OnboardingMCP
    │   ├─→ auth_service
    │   ├─→ session_service
    │   ├─→ External Auth API
    │   └─→ DynamoDB
    │
    ├─→ ActivityMCP
    │   ├─→ activity_service
    │   ├─→ submission_service
    │   ├─→ activity_classifier
    │   ├─→ DynamoDB
    │   └─→ City Service API
    │
    └─→ ChatMCP
        ├─→ gemini_service
        ├─→ intent_classifier
        └─→ Gemini API

Result: Services only depend on Orchestrator interface (HTTP)
```

---

## 5. Data Flow by Intent

### Signup Flow
```
User: "I want to sign up"
    │
    ├─ ORCHESTRATOR
    │  ├─ Detects: "signup"
    │  ├─ Routes to: OnboardingMCP
    │  └─ Sends: {intent: "signup", message: "I want to sign up"}
    │
    └─ ONBOARDINGMCP
       ├─ Starts signup flow
       ├─ Sets flow_step = "email"
       ├─ Returns: "What's your email?"
       │
       ├─→ User: "<EMAIL>"
       │  ├─ Validates email format
       │  ├─ Stores in collected_data
       │  ├─ Advances to flow_step = "password"
       │  ├─ Returns: "Create a password:"
       │
       ├─→ User: "SecurePass123"
       │  ├─ Stores password
       │  ├─ Flow complete
       │  ├─ Calls auth_service.signup()
       │  │  └─ External Auth API creates user
       │  └─ Returns: {token: "...", user: {...}}

Result: Signup complete, user logged in
```

### Activity Query Flow
```
User: "Show me activities"
    │
    ├─ ORCHESTRATOR
    │  ├─ Detects: "activity_query"
    │  ├─ Routes to: ActivityMCP
    │  └─ Sends: {intent: "activity_query", message: "Show me activities"}
    │
    └─ ACTIVITYMCP
       ├─ Queries DynamoDB for activities
       ├─ Returns: {activities: [...], total: 50}

Result: List of activities returned to user
```

### General Chat Flow
```
User: "What is MyVillage about?"
    │
    ├─ ORCHESTRATOR
    │  ├─ Detects: "general_inquiry"
    │  ├─ Routes to: ChatMCP
    │  └─ Sends: {intent: "general_inquiry", message: "What is MyVillage about?"}
    │
    └─ CHATMCP
       ├─ Calls gemini_service.generate_response()
       │  └─ Sends to Google Gemini API
       └─ Returns: {response: "MyVillage is..."}

Result: AI-generated response returned to user
```

---

## 6. Deployment Topology

### Local Development (docker-compose)
```
Host Machine
│
├─ Docker Network: orchestrator-network
│  ├─ Orchestrator (localhost:8000)
│  ├─ OnboardingMCP (localhost:8001)
│  ├─ ActivityMCP (localhost:8002)
│  ├─ ChatMCP (localhost:8003)
│  ├─ DynamoDB Local (localhost:8005)
│  └─ Next.js Frontend (localhost:3000)
```

### Production (Kubernetes-style)
```
Ingress
    │
    ├─→ Orchestrator Service (replicas: 2)
    │   ├─ Pod 1
    │   └─ Pod 2
    │
    ├─→ OnboardingMCP Service (replicas: 1)
    │   └─ Pod 1
    │
    ├─→ ActivityMCP Service (replicas: 2)
    │   ├─ Pod 1
    │   └─ Pod 2
    │
    └─→ ChatMCP Service (replicas: 1)
        └─ Pod 1 (with GPU if available)

Shared Resources:
    ├─ DynamoDB (AWS managed)
    ├─ Redis (for distributed sessions)
    ├─ External Auth Service
    └─ Gemini API (external)
```

---

## 7. Request/Response Lifecycle

### Complete Sequence Diagram

```
Frontend          Orchestrator      OnboardingMCP       External Auth API
   │                   │                   │                    │
   │  POST /message    │                   │                    │
   ├──────────────────→│                   │                    │
   │  {message: "..."}  │                   │                    │
   │                   │                   │                    │
   │                   │─ Detect Intent    │                    │
   │                   │─ Route Decision   │                    │
   │                   │                   │                    │
   │                   │  POST /message    │                    │
   │                   ├──────────────────→│                    │
   │                   │                   │                    │
   │                   │                   │─ Get Session        │
   │                   │                   │─ Start/Continue Flow│
   │                   │                   │                    │
   │                   │                   │ (if signup complete)│
   │                   │                   │ POST /signup       │
   │                   │                   ├───────────────────→│
   │                   │                   │                    │
   │                   │                   │ 200 OK             │
   │                   │                   │ {user: {...}}      │
   │                   │                   │←───────────────────┤
   │                   │                   │                    │
   │ Response ◄────────┼─────────────────────┤                    │
   │                   │  {flow_response}    │                    │
   │                   │←───────────────────┤                    │
   │                   │                   │                    │
   ├─ Display Message  │                   │                    │
   │                   │                   │                    │
   ├─ Wait for Input   │                   │                    │
   │                   │                   │                    │
   │  POST /message    │                   │                    │
   ├──────────────────→│                   │                    │
   │  {message: "..."}  │                   │                    │
   │                   │                   │                    │
   └─ [continues...]   └─ [continues...]   └─ [continues...]    └─ [continues...]
```

---

## 8. Configuration & Environment Variables

### Orchestrator (.env)
```
# Server
HOST=0.0.0.0
PORT=8000
DEBUG=true
LOG_LEVEL=INFO

# MCP URLs
ONBOARDING_MCP_URL=http://localhost:8001
ACTIVITY_MCP_URL=http://localhost:8002
CHAT_MCP_URL=http://localhost:8003

# Timeouts
MCP_REQUEST_TIMEOUT=30
```

### OnboardingMCP (.env)
```
# Server
HOST=0.0.0.0
PORT=8001
DEBUG=true
LOG_LEVEL=INFO

# External Auth API
AUTH_API_URL=https://auth-service.example.com/login
SIGNUP_API_URL=https://auth-service.example.com/signup
AUTH_API_TIMEOUT=10
API_BEARER_TOKEN=secret_token

# DynamoDB
DYNAMODB_REGION=us-east-1
DYNAMODB_ENDPOINT=http://localhost:8005  # for local dev
```

### ActivityMCP (.env)
```
# Server
HOST=0.0.0.0
PORT=8002
DEBUG=true
LOG_LEVEL=INFO

# DynamoDB
DYNAMODB_REGION=us-east-1
DYNAMODB_ENDPOINT=http://localhost:8005

# City Service
CITY_SERVICE_URL=https://city-service.example.com
```

### ChatMCP (.env)
```
# Server
HOST=0.0.0.0
PORT=8003
DEBUG=true
LOG_LEVEL=INFO

# Gemini API
GEMINI_API_KEY=your_key_here
GEMINI_MODEL=gemini-pro

# Hugging Face
HUGGINGFACE_API_TOKEN=your_token_here
HF_MODEL_NAME=distilbert-base-uncased-finetuned-sst-2-english
```

---

## 9. Scaling Scenarios

### Scenario 1: High Signup Traffic
```
Without MCPs:
  All requests → Single monolith instance
  Signup processing blocks activity queries

With MCPs:
  Signup requests → OnboardingMCP (scale to 3 replicas)
  Activity requests → ActivityMCP (1 replica, unaffected)
  Result: ✅ Independent scaling
```

### Scenario 2: Gemini API Rate Limiting
```
Without MCPs:
  Chat requests block all other operations
  Need to scale entire monolith

With MCPs:
  ChatMCP hits rate limit
  OnboardingMCP and ActivityMCP continue normally
  Result: ✅ Isolated impact
```

### Scenario 3: Database Connection Pool Exhaustion
```
Without MCPs:
  One connection pool shared by all services
  Activity queries can exhaust pool for auth operations

With MCPs:
  Each MCP has separate connection pool
  Activity service exhaustion doesn't affect auth
  Result: ✅ Independent resource allocation
```

---

## 10. Monitoring & Observability

### Metrics to Track

**Per MCP:**
- Request count
- Error rate
- Latency (p50, p95, p99)
- CPU/Memory usage
- External API call duration

**Orchestrator:**
- Request routing distribution
- Intent detection accuracy
- Message latency end-to-end
- MCP health status

**Example Dashboard:**
```
┌──────────────────────────────────────────────────────────┐
│                    Orchestrator Metrics                  │
├──────────────────────────────────────────────────────────┤
│                                                          │
│ Message Throughput: 1.2K req/s                          │
│ P95 Latency: 245ms                                      │
│ Error Rate: 0.1%                                        │
│                                                          │
│ MCP Status:                                             │
│ ├─ OnboardingMCP: 👍 Healthy (3 replicas, 150 req/s)  │
│ ├─ ActivityMCP: 👍 Healthy (2 replicas, 500 req/s)    │
│ └─ ChatMCP: ⚠️  Slow (1 replica, 550ms avg latency)   │
│                                                          │
│ Top Intents:                                            │
│ ├─ activity_query (45%)                                │
│ ├─ general_inquiry (30%)                               │
│ ├─ signup (20%)                                         │
│ └─ login (5%)                                           │
│                                                          │
└──────────────────────────────────────────────────────────┘
```

---

## Summary: Architecture Benefits

| Aspect | Before (Monolith) | After (MCP) |
|--------|-------------------|------------|
| **Scaling** | All-or-nothing | Independent per MCP |
| **Deployment** | Full app rebuild | Single MCP update |
| **Team Ownership** | Shared codebase | Clear boundaries |
| **Failure Isolation** | Cascading failures | Isolated failures |
| **Tech Stack** | Single | Multiple per MCP |
| **Testing** | Integration-heavy | Unit + isolated integration |
| **Development Speed** | Slower (merged PRs) | Faster (parallel development) |
| **Latency** | Variable | Predictable per MCP |

---

**For implementation details:** See `CODE_EXTRACTION_GUIDE.md` and `IMPLEMENTATION_QUICK_START.md`
