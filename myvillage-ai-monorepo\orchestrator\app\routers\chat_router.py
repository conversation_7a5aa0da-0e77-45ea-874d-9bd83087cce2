"""Chat router for orchestrator."""
from fastapi import APIRouter, HTTPException
import httpx
import logging

from ..schemas.messages import ChatRequest, ChatResponse
from ..core.config import settings
from ..services.intent_detector import IntentDetector
from ..services.onboarding_client import OnboardingClient
from ..services.activities_client import Activities<PERSON><PERSON>
from ..services.rewards_client import RewardsClient
from ..services.chat_client import ChatClient
from ..services.conversation_manager import conversation_manager
from ..services.city_service import get_city_service

logger = logging.getLogger(__name__)
router = APIRouter()

# Initialize services
intent_detector = IntentDetector()
onboarding_client = OnboardingClient()
activities_client = ActivitiesClient()
rewards_client = RewardsClient()
chat_client = ChatClient(base_url=settings.chat_mcp_url)
city_service = get_city_service()


@router.get("/conversations/{user_id}")
async def get_conversations(user_id: str):
    """Get conversation history for a user."""
    try:
        logger.info(f"Fetching conversations for user: {user_id}")
        response = await chat_client.get_conversations(user_id)
        
        if response.get("success"):
            return {
                "success": True,
                "data": response.get("data", [])
            }
        else:
            return {
                "success": False,
                "message": response.get("message", "Failed to fetch conversations"),
                "data": []
            }
    except Exception as e:
        logger.error(f"Error fetching conversations: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch conversations: {str(e)}"
        )


@router.get("/cities")
async def get_cities():
    """Get list of all available cities."""
    try:
        logger.info("Fetching cities list")
        cities_response = await activities_client.list_cities(limit=100)
        
        if cities_response.get("success"):
            return {
                "success": True,
                "data": cities_response.get("data", {})
            }
        else:
            return {
                "success": False,
                "message": "Failed to fetch cities",
                "data": {"cities": [], "count": 0}
            }
    except Exception as e:
        logger.error(f"Error fetching cities: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch cities: {str(e)}"
        )


@router.post("/chat", response_model=ChatResponse)
async def process_chat(request: ChatRequest):
    """
    Process chat message with conversational flow support.
    
    Flow:
    1. Check if user has active conversation
    2. If yes, process input in conversation context
    3. If no, detect intent and start new flow or respond directly
    """
    logger.info(f"Processing chat for user: {request.user_id}")
    logger.info(f"Message: {request.text}")
    
    try:
        user_input = request.text.strip()
        chat_type = request.chat_type or "GeneralWeb"
        
        async def save_and_return(response: ChatResponse) -> ChatResponse:
            try:
                await chat_client.save_message(
                    user_id=request.user_id,
                    chat_type=chat_type,
                    message=response.message,
                    role="assistant",
                    message_type="TEXT"
                )
            except Exception as e:
                logger.error(f"Failed to save assistant message: {e}")
            return response
        
        # Save user message
        try:
            await chat_client.save_message(
                user_id=request.user_id,
                chat_type=chat_type,
                message=user_input,
                role="user",
                message_type="TEXT"
            )
        except Exception as e:
            logger.error(f"Failed to save user message: {e}")
        
        # Check for global "my submissions" command - INTERRUPT any active flow
        if "my submissions" in user_input.lower() or "show my submissions" in user_input.lower():
            logger.info(f"Global command detected: my submissions (User: {request.user_id})")
            
            # Cancel any active conversation first
            if conversation_manager.has_active_conversation(request.user_id):
                conversation_manager.cancel_flow(request.user_id)
            
            # Start conversation flow to ask for city
            result = conversation_manager.start_flow(request.user_id, "filter_my_submissions_by_city")
            return await save_and_return(ChatResponse(
                success=True,
                message=result["message"],
                intent="submission_list",
                routed_to="orchestrator",
                data={
                    "in_progress": True,
                    "needs_city_list": result.get("needs_city_list", False)
                }
            )

        # Check for cancel command
        if user_input.lower() in ["cancel", "stop", "exit", "quit"]:
            result = conversation_manager.cancel_flow(request.user_id)
            return await save_and_return(ChatResponse(
                success=True,
                message=result["message"],
                intent="cancel",
                routed_to="orchestrator",
                data={}
            ))
        
        # Check if user has active conversation
        if conversation_manager.has_active_conversation(request.user_id):
            logger.info(f"User {request.user_id} has active conversation")
            result = conversation_manager.process_input(request.user_id, user_input)
            
            # If conversation completed, execute the action
            if result.get("completed"):
                flow = result["flow"]
                data = result["data"]
                
                if flow == "signup":
                    # Create user
                    try:
                        user_response = await onboarding_client.create_user(
                            name=data["name"],
                            email=data["email"],
                            password=data["password"]
                        )
                        
                        if user_response.get("success"):
                            return await save_and_return(ChatResponse(
                                success=True,
                                message=f"🎉 Welcome {data['name']}! Your account has been created successfully. You can now log in with your email and password.",
                                intent="signup",
                                routed_to="onboarding",
                                data=user_response.get("data", {})
                            )
                        else:
                            return await save_and_return(ChatResponse(
                                success=False,
                                message=f"❌ Signup failed: {user_response.get('message', 'Unknown error')}",
                                intent="signup",
                                routed_to="onboarding",
                                data={}
                            )
                    except httpx.HTTPStatusError as e:
                        error_detail = "Unknown error"
                        try:
                            error_data = e.response.json()
                            error_detail = error_data.get("detail", str(e))
                        except:
                            error_detail = str(e)
                        
                        return await save_and_return(ChatResponse(
                            success=False,
                            message=f"❌ Signup failed: {error_detail}",
                            intent="signup",
                            routed_to="onboarding",
                            data={}
                        )
                    except Exception as e:
                        logger.error(f"Signup error: {str(e)}", exc_info=True)
                        return await save_and_return(ChatResponse(
                            success=False,
                            message=f"❌ An error occurred during signup. Please try again.",
                            intent="signup",
                            routed_to="onboarding",
                            data={}
                        )
                
                elif flow == "login":
                    # Login user
                    try:
                        login_response = await onboarding_client.login_user(
                            email=data["email"],
                            password=data["password"]
                        )
                        
                        if login_response.get("success"):
                            user_data = login_response.get("data", {})
                            user_name = user_data.get("user", {}).get("name", "")
                            return await save_and_return(ChatResponse(
                                success=True,
                                message=f"✅ Welcome back{', ' + user_name if user_name else ''}! You're now logged in.",
                                intent="login",
                                routed_to="onboarding",
                                data=user_data
                            )
                        else:
                            return await save_and_return(ChatResponse(
                                success=False,
                                message="❌ Login failed. Please check your email and password.",
                                intent="login",
                                routed_to="onboarding",
                                data={}
                            )
                    except httpx.HTTPStatusError as e:
                        if e.response.status_code == 401:
                            return await save_and_return(ChatResponse(
                                success=False,
                                message="❌ Invalid email or password. Please try again or sign up if you don't have an account.",
                                intent="login",
                                routed_to="onboarding",
                                data={}
                            )
                        else:
                            return await save_and_return(ChatResponse(
                                success=False,
                                message=f"❌ Login service error. Please try again later.",
                                intent="login",
                                routed_to="onboarding",
                                data={}
                            )
                    except Exception as e:
                        logger.error(f"Login error: {str(e)}", exc_info=True)
                        return await save_and_return(ChatResponse(
                            success=False,
                            message="❌ An error occurred during login. Please try again.",
                            intent="login",
                            routed_to="onboarding",
                            data={}
                        )
                
                elif flow == "create_activity":
                    # Create activity
                    try:
                        activity_response = await activities_client.create_activity(
                            title=data["title"],
                            description=data["description"],
                            activity_type=data["activity_type"],
                            created_by=request.user_id
                        )
                        return await save_and_return(ChatResponse(
                            success=True,
                            message=f"✅ Activity '{data['title']}' created successfully!",
                            intent="activity_create",
                            routed_to="activities",
                            data=activity_response.get("data", {})
                        )
                    except Exception as e:
                        return await save_and_return(ChatResponse(
                            success=False,
                            message=f"Sorry, there was an error creating the activity: {str(e)}",
                            intent="activity_create",
                            routed_to="activities",
                            data={}
                        )
                
                elif flow == "list_activities":
                    # List activities by city
                    city_name = data.get("city_name")
                    
                    # Get city ID from city name
                    city_id = city_service.get_city_id(city_name)
                    
                    if not city_id:
                        return await save_and_return(ChatResponse(
                            success=False,
                            message=f"❌ Sorry, I couldn't find a city named '{city_name}'. Please check the spelling and try again.",
                            intent="activity_list",
                            routed_to="orchestrator",
                            data={}
                        )
                    
                    # Fetch activities for this city
                    try:
                        activities = await activities_client.list_activities(
                            city_id=city_id,
                            limit=10
                        )
                        
                        activities_data = activities.get('data', {})
                        activities_list = activities_data.get('activities', [])
                        
                        if activities_list:
                            return await save_and_return(ChatResponse(
                                success=True,
                                message=f"📍 Here are the activities in {city_name}:",
                                intent="activity_list",
                                routed_to="activities",
                                data=activities_data
                            )
                        else:
                            return await save_and_return(ChatResponse(
                                success=True,
                                message=f"📍 No activities found in {city_name} at the moment.",
                                intent="activity_list",
                                routed_to="activities",
                                data={"activities": [], "count": 0}
                            )
                    except Exception as e:
                        logger.error(f"Error fetching activities: {str(e)}", exc_info=True)
                        return await save_and_return(ChatResponse(
                            success=False,
                            message=f"❌ Sorry, there was an error fetching activities. Please try again.",
                            intent="activity_list",
                            routed_to="activities",
                            data={}
                        )
                
                elif flow == "list_submissions":
                    # List submissions based on filter type
                    filter_type = data.get("filter_type", "").lower()
                    
                    # Map user input to filter type
                    if filter_type in ["1", "activity"]:
                        # Start activity filter flow
                        result = conversation_manager.start_flow(request.user_id, "filter_submissions_by_activity")
                        return await save_and_return(ChatResponse(
                            success=True,
                            message=result["message"],
                            intent="submission_list",
                            routed_to="orchestrator",
                            data={"in_progress": True}
                        )
                    elif filter_type in ["2", "user"]:
                        # Start user filter flow
                        result = conversation_manager.start_flow(request.user_id, "filter_submissions_by_user")
                        return await save_and_return(ChatResponse(
                            success=True,
                            message=result["message"],
                            intent="submission_list",
                            routed_to="orchestrator",
                            data={"in_progress": True}
                        )
                    elif filter_type in ["3", "status"]:
                        # Start status filter flow
                        result = conversation_manager.start_flow(request.user_id, "filter_submissions_by_status")
                        return await save_and_return(ChatResponse(
                            success=True,
                            message=result["message"],
                            intent="submission_list",
                            routed_to="orchestrator",
                            data={"in_progress": True}
                        )
                    elif filter_type in ["4", "all"]:
                        # Fetch all submissions
                        try:
                            submissions = await activities_client.list_submissions(limit=20)
                            
                            submissions_data = submissions.get('data', {})
                            submissions_list = submissions_data.get('submissions', [])
                            
                            if submissions_list:
                                return await save_and_return(ChatResponse(
                                    success=True,
                                    message=f"📝 Here are all submissions (showing {len(submissions_list)}):",
                                    intent="submission_list",
                                    routed_to="activities",
                                    data=submissions_data
                                )
                            else:
                                return await save_and_return(ChatResponse(
                                    success=True,
                                    message="📝 No submissions found at the moment.",
                                    intent="submission_list",
                                    routed_to="activities",
                                    data={"submissions": [], "count": 0}
                                )
                        except Exception as e:
                            logger.error(f"Error fetching submissions: {str(e)}", exc_info=True)
                            return await save_and_return(ChatResponse(
                                success=False,
                                message=f"❌ Sorry, there was an error fetching submissions. Please try again.",
                                intent="submission_list",
                                routed_to="activities",
                                data={}
                            )
                    elif filter_type in ["5", "city"]:
                        # Start city filter flow
                        result = conversation_manager.start_flow(request.user_id, "filter_submissions_by_city")
                        return await save_and_return(ChatResponse(
                            success=True,
                            message=result["message"],
                            intent="submission_list",
                            routed_to="orchestrator",
                            data={
                                "in_progress": True,
                                "needs_city_list": result.get("needs_city_list", False)
                            }
                        )
                
                elif flow == "filter_submissions_by_status":
                    status = data.get("status")
                    try:
                        submissions = await activities_client.list_submissions(status=status, limit=20)
                        submissions_data = submissions.get('data', {})
                        submissions_list = submissions_data.get('submissions', [])
                        
                        if submissions_list:
                            return await save_and_return(ChatResponse(
                                success=True,
                                message=f"📝 Here are submissions with status '{status}':",
                                intent="submission_list",
                                routed_to="activities",
                                data=submissions_data
                            )
                        else:
                            return await save_and_return(ChatResponse(
                                success=True,
                                message=f"📝 No submissions found with status '{status}'.",
                                intent="submission_list",
                                routed_to="activities",
                                data={"submissions": [], "count": 0}
                            )
                    except Exception as e:
                        logger.error(f"Error fetching submissions by status: {str(e)}", exc_info=True)
                        return await save_and_return(ChatResponse(
                            success=False,
                            message=f"❌ Error fetching submissions. Please try again.",
                            intent="submission_list",
                            routed_to="activities",
                            data={}
                        )

                elif flow == "filter_submissions_by_activity":
                    activity_id = data.get("activity_id")
                    try:
                        submissions = await activities_client.list_submissions(activity_id=activity_id, limit=20)
                        submissions_data = submissions.get('data', {})
                        submissions_list = submissions_data.get('submissions', [])
                        
                        if submissions_list:
                            return await save_and_return(ChatResponse(
                                success=True,
                                message=f"📝 Here are submissions for activity '{activity_id}':",
                                intent="submission_list",
                                routed_to="activities",
                                data=submissions_data
                            )
                        else:
                            return await save_and_return(ChatResponse(
                                success=True,
                                message=f"📝 No submissions found for activity '{activity_id}'.",
                                intent="submission_list",
                                routed_to="activities",
                                data={"submissions": [], "count": 0}
                            )
                    except Exception as e:
                        logger.error(f"Error fetching submissions by activity: {str(e)}", exc_info=True)
                        return await save_and_return(ChatResponse(
                            success=False,
                            message=f"❌ Error fetching submissions. Please try again.",
                            intent="submission_list",
                            routed_to="activities",
                            data={}
                        )

                elif flow == "filter_submissions_by_user":
                    user_id = data.get("user_id")
                    try:
                        submissions = await activities_client.list_submissions(user_id=user_id, limit=20)
                        submissions_data = submissions.get('data', {})
                        submissions_list = submissions_data.get('submissions', [])
                        
                        if submissions_list:
                            return await save_and_return(ChatResponse(
                                success=True,
                                message=f"📝 Here are submissions by user '{user_id}':",
                                intent="submission_list",
                                routed_to="activities",
                                data=submissions_data
                            )
                        else:
                            return await save_and_return(ChatResponse(
                                success=True,
                                message=f"📝 No submissions found by user '{user_id}'.",
                                intent="submission_list",
                                routed_to="activities",
                                data={"submissions": [], "count": 0}
                            )
                    except Exception as e:
                        logger.error(f"Error fetching submissions by user: {str(e)}", exc_info=True)
                        return await save_and_return(ChatResponse(
                            success=False,
                            message=f"❌ Error fetching submissions. Please try again.",
                            intent="submission_list",
                            routed_to="activities",
                            data={}
                        )

                elif flow == "filter_submissions_by_city":
                    city_name = data.get("city_name")
                    
                    # Get city ID from city name
                    city_id = city_service.get_city_id(city_name)
                    
                    if not city_id:
                        return await save_and_return(ChatResponse(
                            success=False,
                            message=f"❌ Sorry, I couldn't find a city named '{city_name}'. Please check the spelling and try again.",
                            intent="submission_list",
                            routed_to="orchestrator",
                            data={}
                        )
                    
                    try:
                        submissions = await activities_client.list_submissions(city_id=city_id, limit=20)
                        submissions_data = submissions.get('data', {})
                        submissions_list = submissions_data.get('submissions', [])
                        
                        if submissions_list:
                            return await save_and_return(ChatResponse(
                                success=True,
                                message=f"📝 Here are submissions in {city_name}:",
                                intent="submission_list",
                                routed_to="activities",
                                data=submissions_data
                            )
                        else:
                            return await save_and_return(ChatResponse(
                                success=True,
                                message=f"📝 No submissions found in {city_name}.",
                                intent="submission_list",
                                routed_to="activities",
                                data={"submissions": [], "count": 0}
                            )
                    except Exception as e:
                        logger.error(f"Error fetching submissions by city: {str(e)}", exc_info=True)
                        return await save_and_return(ChatResponse(
                            success=False,
                            message=f"❌ Error fetching submissions. Please try again.",
                            intent="submission_list",
                            routed_to="activities",
                            data={}
                        )

                elif flow == "filter_my_submissions_by_city":
                    city_name = data.get("city_name")
                    
                    # Get city ID from city name
                    city_id = city_service.get_city_id(city_name)
                    
                    if not city_id:
                        return await save_and_return(ChatResponse(
                            success=False,
                            message=f"❌ Sorry, I couldn't find a city named '{city_name}'. Please check the spelling and try again.",
                            intent="submission_list",
                            routed_to="orchestrator",
                            data={}
                        )
                    
                    try:
                        # Fetch submissions for THIS user in THIS city
                        submissions = await activities_client.list_submissions(
                            user_id=request.user_id,
                            city_id=city_id,
                            limit=20
                        )
                        submissions_data = submissions.get('data', {})
                        submissions_list = submissions_data.get('submissions', [])
                        
                        if submissions_list:
                            return await save_and_return(ChatResponse(
                                success=True,
                                message=f"📝 Here are your submissions in {city_name}:",
                                intent="submission_list",
                                routed_to="activities",
                                data=submissions_data
                            )
                        else:
                            return await save_and_return(ChatResponse(
                                success=True,
                                message=f"📝 You haven't made any submissions in {city_name}.",
                                intent="submission_list",
                                routed_to="activities",
                                data={"submissions": [], "count": 0}
                            )
                    except Exception as e:
                        logger.error(f"Error fetching user submissions by city: {str(e)}", exc_info=True)
                        return await save_and_return(ChatResponse(
                            success=False,
                            message=f"❌ Error fetching submissions. Please try again.",
                            intent="submission_list",
                            routed_to="activities",
                            data={}
                        )
            
            # Return conversation step response
            return await save_and_return(ChatResponse(
                success=result["success"],
                message=result["message"],
                intent=result.get("flow", "conversation"),
                routed_to="orchestrator",
                data={
                    "in_progress": result.get("in_progress", False),
                    "needs_city_list": result.get("needs_city_list", False)
                }
            )
        
        # No active conversation - detect intent
        intent = intent_detector.detect(user_input)
        mcp_service = intent_detector.get_mcp_service(intent)
        
        logger.info(f"Detected intent: {intent}, routing to: {mcp_service}")
        
        # Handle different intents
        if intent == "signup":
            result = conversation_manager.start_flow(request.user_id, "signup")
            return await save_and_return(ChatResponse(
                success=True,
                message=result["message"],
                intent=intent,
                routed_to=mcp_service,
                data={"in_progress": True}
            )
        
        elif intent == "login":
            result = conversation_manager.start_flow(request.user_id, "login")
            return await save_and_return(ChatResponse(
                success=True,
                message=result["message"],
                intent=intent,
                routed_to=mcp_service,
                data={"in_progress": True}
            )
        
        elif intent == "activity_list":
            # Start conversation flow to ask for city name
            result = conversation_manager.start_flow(request.user_id, "list_activities")
            return await save_and_return(ChatResponse(
                success=True,
                message=result["message"],
                intent=intent,
                routed_to=mcp_service,
                data={
                    "in_progress": True,
                    "needs_city_list": result.get("needs_city_list", False)
                }
            )
        
        elif intent == "activity_create":
            result = conversation_manager.start_flow(request.user_id, "create_activity")
            return await save_and_return(ChatResponse(
                success=True,
                message=result["message"],
                intent=intent,
                routed_to=mcp_service,
                data={"in_progress": True}
            )
        
        elif intent == "submission_list":
            # Start conversation flow to ask for filter type
            result = conversation_manager.start_flow(request.user_id, "list_submissions")
            return await save_and_return(ChatResponse(
                success=True,
                message=result["message"],
                intent=intent,
                routed_to=mcp_service,
                data={"in_progress": True}
            )
        
        elif intent == "rewards_get":
            rewards = await rewards_client.get_rewards(request.user_id)
            points = rewards.get('data', {}).get('available_points', 0)
            return await save_and_return(ChatResponse(
                success=True,
                message=f"🎁 You have {points} reward points available!",
                intent=intent,
                routed_to=mcp_service,
                data=rewards.get('data', {})
            )
        
        # Default response for general queries
        # Default response for general queries
        response_message = "I'm here to help! You can:\n• Sign up or log in\n• View activities\n• Check your rewards\n• Create activities\n\nWhat would you like to do?"
        
        # Save assistant response
        try:
            await chat_client.save_message(
                user_id=request.user_id,
                chat_type=chat_type,
                message=response_message,
                role="assistant",
                message_type="TEXT"
            )
        except Exception as e:
            logger.error(f"Failed to save assistant message: {e}")

        return await save_and_return(ChatResponse(
            success=True,
            message=response_message,
            intent=intent,
            routed_to="orchestrator",
            data={}
        )
    
    except Exception as e:
        logger.error(f"Error processing chat: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process chat: {str(e)}"
        )
