from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Query
from typing import List, Optional, Dict, Any

from ...models.activity import Activity, ActivityCreate, ActivityUpdate, ActivityListResponse
from ...services.activity_service import get_activity_service
from ...core.security import get_current_user

router = APIRouter()

@router.post("/", response_model=Activity, status_code=status.HTTP_201_CREATED)
async def create_activity(
    activity: ActivityCreate,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Create a new activity.
    """
    # Log the incoming request payload
    logger.info(
        "[ACTIVITY_CREATE] Creating new activity. "
        f"User: {current_user.get('user_id')}, "
        f"Request data: {activity.dict(exclude={'content'})}"
    )
    
    activity_service = get_activity_service()
    try:
        # Set the creator to the current user
        activity_data = activity.dict()
        activity_data["created_by"] = current_user["user_id"]
        created_activity = await activity_service.create_activity(ActivityCreate(**activity_data))
        
        # Log the created activity (excluding sensitive data)
        logger.info(
            f"[ACTIVITY_CREATE] Successfully created activity ID: {created_activity.id}, "
            f"Type: {getattr(created_activity, 'type', 'N/A')}"
        )
        return created_activity
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.get("/{activity_id}", response_model=Activity)
async def get_activity(
    activity_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get an activity by ID.
    """
    logger.info(
        f"[ACTIVITY_GET] Fetching activity. "
        f"User: {current_user.get('user_id')}, "
        f"Activity ID: {activity_id}"
    )
    
    activity_service = get_activity_service()
    try:
        activity = await activity_service.get_activity(activity_id)
        
        # Log successful fetch
        logger.info(
            f"[ACTIVITY_GET] Retrieved activity. "
            f"ID: {activity.id}, "
            f"Type: {getattr(activity, 'type', 'N/A')}, "
            f"Status: {getattr(activity, 'status', 'N/A')}"
        )
        return activity
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve activity"
        )

@router.put("/{activity_id}", response_model=Activity)
async def update_activity(
    activity_id: str,
    activity: ActivityUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Update an activity.
    """
    # Log the update request
    logger.info(
        f"[ACTIVITY_UPDATE] Updating activity. "
        f"User: {current_user.get('user_id')}, "
        f"Activity ID: {activity_id}, "
        f"Update data: {activity.dict(exclude_unset=True, exclude={'content'})}"
    )
    
    activity_service = get_activity_service()
    try:
        # Check if activity exists and user has permission
        existing_activity = await activity_service.get_activity(activity_id)
        
        # Only allow creator to update
        if str(existing_activity.created_by) != current_user["user_id"]:
            logger.warning(
                f"[ACTIVITY_UPDATE] Unauthorized update attempt. "
                f"User {current_user.get('user_id')} tried to update activity {activity_id} "
                f"owned by {existing_activity.created_by}"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to update this activity"
            )
            
        updated_activity = await activity_service.update_activity(activity_id, activity)
        
        # Log successful update
        logger.info(
            f"[ACTIVITY_UPDATE] Successfully updated activity {activity_id}. "
            f"New status: {getattr(updated_activity, 'status', 'N/A')}"
        )
        return updated_activity
    except HTTPException:
        raise
    except Exception as e:
        if isinstance(e, HTTPException):
            raise
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.delete("/{activity_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_activity(
    activity_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Delete an activity.
    """
    logger.info(
        f"[ACTIVITY_DELETE] Deleting activity. "
        f"User: {current_user.get('user_id')}, "
        f"Activity ID: {activity_id}"
    )
    
    activity_service = get_activity_service()
    try:
        # Check if activity exists and user has permission
        existing_activity = await activity_service.get_activity(activity_id)
        
        # Only allow creator to delete
        if str(existing_activity.created_by) != current_user["user_id"]:
            logger.warning(
                f"[ACTIVITY_DELETE] Unauthorized delete attempt. "
                f"User {current_user.get('user_id')} tried to delete activity {activity_id} "
                f"owned by {existing_activity.created_by}"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to delete this activity"
            )
            
        await activity_service.delete_activity(activity_id)
        logger.info(f"[ACTIVITY_DELETE] Successfully deleted activity {activity_id}")
        return None
    except HTTPException:
        raise
    except Exception as e:
        if isinstance(e, HTTPException):
            raise
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.get("/user/{user_id}", response_model=ActivityListResponse)
async def get_user_activities(
    user_id: str,
    status: Optional[str] = Query(None, description="Filter by status"),
    limit: int = Query(10, ge=1, le=100, description="Number of items per page"),
    offset: int = Query(0, ge=0, description="Page number"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get activities assigned to a user.
    """
    # Log the request
    logger.info(
        f"[USER_ACTIVITIES_GET] Fetching activities for user. "
        f"Requested by: {current_user.get('user_id')}, "
        f"Target user: {user_id}, "
        f"Status filter: {status}, "
        f"Limit: {limit}, Offset: {offset}"
    )
    
    # Only allow users to view their own activities
    if user_id != current_user["user_id"]:
        logger.warning(
            f"[USER_ACTIVITIES_GET] Unauthorized access attempt. "
            f"User {current_user.get('user_id')} tried to access activities for user {user_id}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to view these activities"
        )
    
    activity_service = get_activity_service()
    try:
        result = await activity_service.list_activities(
            skip=offset,
            limit=limit,
            status=status,
            created_by=user_id
        )
        
        # Log the response summary
        logger.info(
            f"[USER_ACTIVITIES_GET] Successfully retrieved {len(result.activities)} activities. "
            f"Total: {result.total}"
        )
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve activities"
        )

@router.post("/{activity_id}/assign", status_code=status.HTTP_200_OK)
async def assign_activity(
    activity_id: str,
    user_ids: List[str],
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Assign an activity to users.
    """
    # Log the assignment request
    logger.info(
        f"[ACTIVITY_ASSIGN] Assigning activity. "
        f"User: {current_user.get('user_id')}, "
        f"Activity ID: {activity_id}, "
        f"Assigned to user IDs: {user_ids}"
    )
    
    activity_service = get_activity_service()
    try:
        # Check if activity exists and user has permission
        activity = await activity_service.get_activity(activity_id)
        
        # Only allow creator to assign
        if str(activity.created_by) != current_user["user_id"]:
            logger.warning(
                f"[ACTIVITY_ASSIGN] Unauthorized assignment attempt. "
                f"User {current_user.get('user_id')} tried to assign activity {activity_id} "
                f"owned by {activity.created_by}"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to assign this activity"
            )
            
        # TODO: Implement assignment logic
        result = {"message": f"Activity assigned to {len(user_ids)} users"}
        
        # Log successful assignment
        logger.info(
            f"[ACTIVITY_ASSIGN] Successfully assigned activity {activity_id} to {len(user_ids)} users"
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        if isinstance(e, HTTPException):
            raise
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
