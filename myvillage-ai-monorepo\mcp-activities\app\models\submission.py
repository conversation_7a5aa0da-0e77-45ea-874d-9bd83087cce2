"""Submission models."""
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum


class SubmissionStatus(str, Enum):
    """Submission status enumeration."""
    DRAFT = "draft"
    SUBMITTED = "submitted"
    GRADED = "graded"
    RETURNED = "returned"


class SubmissionBase(BaseModel):
    """Base submission model."""
    activity_id: str
    user_id: str
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    status: SubmissionStatus = SubmissionStatus.DRAFT


class SubmissionCreate(BaseModel):
    """Submission creation model."""
    activity_id: str
    user_id: str
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    content: Optional[str] = None
    is_public: bool = True


class SubmissionUpdate(BaseModel):
    """Submission update model."""
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    content: Optional[str] = None
    status: Optional[SubmissionStatus] = None
    is_public: Optional[bool] = None


class Submission(SubmissionBase):
    """Submission model."""
    id: str
    content: Optional[str] = None
    is_public: bool = True
    created_at: datetime
    updated_at: datetime
    submitted_at: Optional[datetime] = None
    grade: Optional[float] = None
    feedback: Optional[str] = None
    graded_by: Optional[str] = None
    graded_at: Optional[datetime] = None
    likes_count: int = 0

    class Config:
        from_attributes = True
