'use client';

import { useState, useEffect } from 'react';
import { ChevronDown, Circle, Check, X } from 'lucide-react';

type MCPTool = {
  name: string;
  description: string;
  status: 'online' | 'offline';
  lastUsed?: string;
};

export default function MCPDropdown() {
  const [isOpen, setIsOpen] = useState(false);
  const [mcpTools, setMcpTools] = useState<MCPTool[]>([]);

  useEffect(() => {
    // Only include Onboarding and Activity servers
    const fetchMcpTools = async () => {
      const tools: MCPTool[] = [
        {
          name: 'Onboarding',
          description: 'User onboarding and profile management',
          status: 'online',
          lastUsed: 'Just now'
        },
        {
          name: 'Activity',
          description: 'User activity tracking and analytics',
          status: 'online',
          lastUsed: '2 mins ago'
        }
      ];
      setMcpTools(tools);
    };

    fetchMcpTools();
  }, []);

  const onlineCount = mcpTools.filter(tool => tool.status === 'online').length;
  const totalCount = mcpTools.length;

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-foreground hover:bg-muted rounded-md transition-colors border border-border"
      >
        <div className="flex items-center space-x-2">
          <div className="relative">
            <div className={`w-2 h-2 rounded-full ${onlineCount > 0 ? 'bg-success' : 'bg-destructive'}`}></div>
          </div>
          <span className="font-medium">MCP</span>
          <span className="text-xs text-muted-foreground">{onlineCount}/{totalCount}</span>
        </div>
        <ChevronDown className={`w-4 h-4 text-muted-foreground transition-transform ${isOpen ? 'transform rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-72 rounded-md shadow-lg bg-card ring-1 ring-border z-50">
          <div className="py-1">
            <div className="px-4 py-2 border-b border-border">
              <p className="text-sm font-medium text-foreground">MCP Servers</p>
              <p className="text-xs text-muted-foreground">{onlineCount} of {totalCount} servers available</p>
            </div>
            
            <div className="max-h-60 overflow-y-auto">
              {mcpTools.map((tool, index) => (
                <div key={index} className="px-4 py-2 hover:bg-muted">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`w-2 h-2 rounded-full mr-2 ${tool.status === 'online' ? 'bg-success' : 'bg-destructive'}`}></div>
                      <div>
                        <p className="text-sm font-medium text-foreground">{tool.name}</p>
                        <p className="text-xs text-muted-foreground">{tool.description}</p>
                      </div>
                    </div>
                    {tool.status === 'online' ? (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-success-light text-success">
                        <Check className="w-3 h-3 mr-1" /> Online
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-destructive-light text-destructive">
                        <X className="w-3 h-3 mr-1" /> Offline
                      </span>
                    )}
                  </div>
                  {tool.lastUsed && (
                    <p className="text-xs text-muted-foreground mt-1 text-right">Last used: {tool.lastUsed}</p>
                  )}
                </div>
              ))}
            </div>
            
            <div className="px-4 py-2 border-t border-border bg-muted">
              <button className="text-xs text-primary hover:text-primary-dark font-medium">
                View all MCP services
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
