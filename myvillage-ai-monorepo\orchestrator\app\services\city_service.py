"""City Service for orchestrator."""
import os
import boto3
from typing import Optional, Dict
from botocore.exceptions import ClientError
import logging

logger = logging.getLogger(__name__)


class CityService:
    """Service to handle city lookup operations."""
    
    def __init__(self):
        """Initialize the DynamoDB resource and table."""
        self.table_name = os.getenv('DYNAMODB_CITIES_TABLE', 'cities')
        self.region = os.getenv('AWS_REGION', 'us-east-1')
        
        logger.info(f"Initializing CityService with table: {self.table_name}")
        
        # Initialize DynamoDB client
        self.dynamodb = boto3.resource(
            'dynamodb',
            region_name=self.region,
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
            aws_session_token=os.getenv('AWS_SESSION_TOKEN')
        )
        
        self.table = self.dynamodb.Table(self.table_name)
    
    def find_city_by_name(self, city_name: str) -> Optional[Dict]:
        """
        Find a city by name (case-insensitive).
        
        Args:
            city_name: The name of the city to find
            
        Returns:
            City data if found, None otherwise
        """
        if not city_name:
            return None
            
        logger.info(f"Searching for city: {city_name}")
        search_name = city_name.strip().lower()
        
        try:
            # Scan with filter to exclude deleted cities
            scan_params = {
                'FilterExpression': '#isDeleted <> :true',
                'ExpressionAttributeNames': {
                    '#isDeleted': 'isDeleted'
                },
                'ExpressionAttributeValues': {
                    ':true': 'true'
                },
                'Limit': 100
            }
            
            while True:
                response = self.table.scan(**scan_params)
                
                # Process items from this batch
                for item in response.get('Items', []):
                    # Check for exact match (case-insensitive)
                    if item.get('name', '').lower() == search_name:
                        logger.info(f"Found city: {item.get('name')} (ID: {item.get('id')})")
                        return item
                
                # Check if there are more items to scan
                if 'LastEvaluatedKey' not in response:
                    break
                    
                scan_params['ExclusiveStartKey'] = response['LastEvaluatedKey']
            
            logger.info(f"City not found: {city_name}")
            return None
            
        except ClientError as e:
            logger.error(f"DynamoDB error finding city '{city_name}': {e}")
            return None
    
    def get_city_id(self, city_name: str) -> Optional[str]:
        """
        Get city ID from city name.
        
        Args:
            city_name: The name of the city
            
        Returns:
            City ID if found, None otherwise
        """
        city_data = self.find_city_by_name(city_name)
        if city_data:
            return city_data.get('id') or city_data.get('cityId')
        return None


# Singleton instance
_city_service_instance = None


def get_city_service() -> CityService:
    """Get the singleton instance of CityService."""
    global _city_service_instance
    if _city_service_instance is None:
        _city_service_instance = CityService()
    return _city_service_instance

