"""Rewards service."""
from datetime import datetime
from typing import Optional, List
import logging
import uuid

from ..models.reward import Reward, RewardTransaction
from ..core.database import get_db

logger = logging.getLogger(__name__)


class RewardsService:
    """Rewards management service."""
    
    def __init__(self):
        from ..core.config import settings
        self.db = get_db()
        self.points_table = self.db.get_table(settings.points_table)
    
    async def get_user_rewards(self, user_id: str) -> Optional[Reward]:
        """Get user's reward balance."""
        try:
            from boto3.dynamodb.conditions import Attr
            
            # Scan Points table filtering by memberId (not userId)
            response = self.points_table.scan(
                FilterExpression=Attr('memberId').eq(user_id)
            )
            
            logger.info(f"Points scan response: {response.get('Count', 0)} items found")
            
            if response.get("Items"):
                # Sum up all MVPTokens values for the user
                total_points = 0
                for item in response["Items"]:
                    mvp_tokens = item.get('MVPTokens', 0)
                    try:
                        total_points += int(mvp_tokens) if mvp_tokens else 0
                    except (ValueError, TypeError):
                        logger.warning(f"Invalid MVPTokens value: {mvp_tokens}")
                        continue
                
                logger.info(f"Total points calculated: {total_points}")
                
                return Reward(
                    id=user_id,
                    user_id=user_id,
                    total_points=total_points,
                    available_points=total_points,
                    redeemed_points=0,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
            
            # Return empty reward if no points found
            logger.info(f"No points found for memberId: {user_id}")
            return Reward(
                id=user_id,
                user_id=user_id,
                total_points=0,
                available_points=0,
                redeemed_points=0,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
        except Exception as e:
            logger.error(f"Error getting rewards: {e}")
            raise
    

    
    async def get_transaction_history(
        self,
        user_id: str,
        limit: int = 10
    ) -> List[RewardTransaction]:
        """Get user's transaction history."""
        try:
            from boto3.dynamodb.conditions import Attr
            
            # Scan Points table filtering by memberId (not userId)
            response = self.points_table.scan(
                FilterExpression=Attr('memberId').eq(user_id),
                Limit=limit
            )
            
            transactions = []
            for item in response.get("Items", []):
                try:
                    created_at_str = item.get('createdAt', item.get('_lastChangedAt'))
                    if isinstance(created_at_str, (int, float)):
                        # Convert timestamp to datetime
                        created_at = datetime.fromtimestamp(created_at_str / 1000)
                    else:
                        created_at = datetime.fromisoformat(str(created_at_str)) if created_at_str else datetime.utcnow()
                except:
                    created_at = datetime.utcnow()
                
                # Get points from MVPTokens field
                mvp_tokens = item.get('MVPTokens', 0)
                try:
                    points_value = int(mvp_tokens) if mvp_tokens else 0
                except (ValueError, TypeError):
                    points_value = 0
                
                transactions.append(RewardTransaction(
                    id=item.get('id', ''),
                    user_id=user_id,
                    activity_id=item.get('activityId', ''),
                    points=points_value,
                    transaction_type='earn',
                    description=item.get('description', 'Points earned'),
                    created_at=created_at
                ))
            
            # Sort by created_at descending
            transactions.sort(key=lambda x: x.created_at, reverse=True)
            return transactions[:limit]
        except Exception as e:
            logger.error(f"Error getting transaction history: {e}")
            return []
