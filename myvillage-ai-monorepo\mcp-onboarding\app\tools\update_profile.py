"""Tool: Update User Profile."""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "common"))

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import Dict, Any
import logging

from common.models.base import BaseResponse
from ..services.user_service import UserService
from ..models.user import UserUpdate

logger = logging.getLogger(__name__)
router = APIRouter()
user_service = UserService()


class UpdateProfileRequest(BaseModel):
    """Update profile request model."""
    user_id: str = Field(..., description="User's unique identifier")
    data: Dict[str, Any] = Field(..., description="Profile data to update")


@router.post("/update_profile", response_model=BaseResponse)
async def update_profile(request: UpdateProfileRequest):
    """
    Update user profile information.
    
    This tool updates a user's profile with the provided data.
    Only the fields present in the data dictionary will be updated.
    
    Args:
        request: Profile update request
        
    Returns:
        BaseResponse with updated user data
        
    Raises:
        HTTPException: If update fails
    """
    try:
        logger.info(f"Updating profile for user: {request.user_id}")
        
        # Create UserUpdate from data
        user_update = UserUpdate(**request.data)
        
        # Update user
        updated_user = await user_service.update_user(
            user_id=request.user_id,
            user_update=user_update
        )
        
        if not updated_user:
            raise HTTPException(status_code=404, detail="User not found")
        
        logger.info(f"Profile updated successfully for: {request.user_id}")
        
        return BaseResponse(
            success=True,
            message="Profile updated successfully",
            data={
                "user_id": updated_user.id,
                "email": updated_user.email,
                "name": updated_user.name,
                "phone_number": updated_user.phone_number,
                "updated_at": updated_user.updated_at.isoformat()
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating profile: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Profile update failed: {str(e)}"
        )
