"""Database configuration and utilities."""
import boto3
import logging
from .config import settings

logger = logging.getLogger(__name__)


class Database:
    """Database connection manager."""
    
    def __init__(self):
        self._resource = None
        self._client = None
    
    @property
    def resource(self):
        if self._resource is None:
            self._resource = boto3.resource('dynamodb', region_name=settings.aws_region)
        return self._resource
    
    @property
    def client(self):
        if self._client is None:
            self._client = boto3.client('dynamodb', region_name=settings.aws_region)
        return self._client
    
    def get_table(self, table_name: str):
        return self.resource.Table(table_name)


db = Database()


def get_db() -> Database:
    return db
