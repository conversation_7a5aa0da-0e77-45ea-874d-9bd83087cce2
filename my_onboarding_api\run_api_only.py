"""
Run only the API server (without MCP server).

This script is useful when you want to use the external orchestrator
instead of the internal MCP server.
"""

import uvicorn
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.config import settings
from app.core.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)

if __name__ == "__main__":
    logger.info("=" * 80)
    logger.info("🚀 Starting API Server (Orchestrator Mode)")
    logger.info("=" * 80)
    logger.info(f"Host: {settings.host}")
    logger.info(f"Port: {settings.port}")
    logger.info(f"Debug: {settings.debug}")
    logger.info(f"Orchestrator URL: {settings.orchestrator_url}")
    logger.info(f"Use Orchestrator: {settings.use_orchestrator}")
    logger.info("=" * 80)
    
    try:
        uvicorn.run(
            "app.main:app",
            host=settings.host,
            port=settings.port,
            reload=settings.debug,
            log_level=settings.log_level.lower()
        )
    except KeyboardInterrupt:
        logger.info("\n" + "=" * 80)
        logger.info("🛑 API Server stopped by user")
        logger.info("=" * 80)
    except Exception as e:
        logger.error(f"❌ Failed to start API server: {str(e)}", exc_info=True)
        sys.exit(1)
