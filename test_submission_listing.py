"""Test script for submission listing functionality."""
import requests
import json

# Configuration
ORCHESTRATOR_URL = "http://localhost:8100"
ACTIVITIES_MCP_URL = "http://localhost:8002"
TEST_USER_ID = "test-user-123"


def test_orchestrator_health():
    """Test orchestrator health check."""
    print("\n🔍 Testing Orchestrator Health...")
    try:
        response = requests.get(f"{ORCHESTRATOR_URL}/health")
        print(f"✅ Orchestrator: {response.json()}")
        return True
    except Exception as e:
        print(f"❌ Orchestrator Error: {e}")
        return False


def test_activities_mcp_health():
    """Test activities MCP health check."""
    print("\n🔍 Testing Activities MCP Health...")
    try:
        response = requests.get(f"{ACTIVITIES_MCP_URL}/health")
        print(f"✅ Activities MCP: {response.json()}")
        return True
    except Exception as e:
        print(f"❌ Activities MCP Error: {e}")
        return False


def test_direct_list_submissions():
    """Test direct MCP call to list submissions."""
    print("\n🔍 Testing Direct MCP Call: List All Submissions...")
    try:
        response = requests.get(f"{ACTIVITIES_MCP_URL}/tools/list_submissions?limit=5")
        data = response.json()
        print(f"✅ Response: {json.dumps(data, indent=2)}")
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_direct_list_submissions_by_status():
    """Test direct MCP call with status filter."""
    print("\n🔍 Testing Direct MCP Call: List Submissions by Status...")
    try:
        response = requests.get(
            f"{ACTIVITIES_MCP_URL}/tools/list_submissions?status=submitted&limit=5"
        )
        data = response.json()
        print(f"✅ Response: {json.dumps(data, indent=2)}")
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_conversational_flow_start():
    """Test starting submission listing conversation."""
    print("\n🔍 Testing Conversational Flow: Start...")
    try:
        response = requests.post(
            f"{ORCHESTRATOR_URL}/chat",
            json={
                "user_id": TEST_USER_ID,
                "text": "Show me submissions"
            }
        )
        data = response.json()
        print(f"✅ Bot Response: {data.get('message')}")
        print(f"   Intent: {data.get('intent')}")
        print(f"   In Progress: {data.get('data', {}).get('in_progress')}")
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_conversational_flow_all():
    """Test selecting 'All' filter option."""
    print("\n🔍 Testing Conversational Flow: Select 'All'...")
    try:
        # Start flow
        requests.post(
            f"{ORCHESTRATOR_URL}/chat",
            json={
                "user_id": TEST_USER_ID,
                "text": "List submissions"
            }
        )
        
        # Select 'All' option
        response = requests.post(
            f"{ORCHESTRATOR_URL}/chat",
            json={
                "user_id": TEST_USER_ID,
                "text": "4"
            }
        )
        data = response.json()
        print(f"✅ Bot Response: {data.get('message')}")
        submissions = data.get('data', {}).get('submissions', [])
        print(f"   Found {len(submissions)} submission(s)")
        if submissions:
            print(f"   First submission: {submissions[0].get('title')}")
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_intent_detection():
    """Test intent detection for submission listing."""
    print("\n🔍 Testing Intent Detection...")
    test_phrases = [
        "show submissions",
        "list submissions",
        "view submissions",
        "my submissions",
        "all submissions"
    ]
    
    for phrase in test_phrases:
        try:
            response = requests.post(
                f"{ORCHESTRATOR_URL}/chat",
                json={
                    "user_id": f"test-{phrase.replace(' ', '-')}",
                    "text": phrase
                }
            )
            data = response.json()
            intent = data.get('intent')
            print(f"✅ '{phrase}' → Intent: {intent}")
        except Exception as e:
            print(f"❌ '{phrase}' → Error: {e}")
    
    return True


def run_all_tests():
    """Run all tests."""
    print("=" * 60)
    print("🧪 SUBMISSION LISTING TESTS")
    print("=" * 60)
    
    results = []
    
    # Health checks
    results.append(("Orchestrator Health", test_orchestrator_health()))
    results.append(("Activities MCP Health", test_activities_mcp_health()))
    
    # Direct MCP calls
    results.append(("Direct List All", test_direct_list_submissions()))
    results.append(("Direct List by Status", test_direct_list_submissions_by_status()))
    
    # Conversational flow
    results.append(("Conversation Start", test_conversational_flow_start()))
    results.append(("Conversation All Filter", test_conversational_flow_all()))
    
    # Intent detection
    results.append(("Intent Detection", test_intent_detection()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    print("=" * 60)


if __name__ == "__main__":
    run_all_tests()
