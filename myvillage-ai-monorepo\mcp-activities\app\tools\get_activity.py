"""Tool: Get Activity."""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "common"))

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
import logging

from common.models.base import BaseResponse
from ..services.activity_service import ActivityService

logger = logging.getLogger(__name__)
router = APIRouter()
activity_service = ActivityService()


class GetActivityRequest(BaseModel):
    """Get activity request model."""
    activity_id: str = Field(..., description="Activity ID")


@router.post("/get_activity", response_model=BaseResponse)
async def get_activity(request: GetActivityRequest):
    """
    Get detailed information about a specific activity.
    
    This tool retrieves complete details for a single activity by its ID.
    
    Args:
        request: Get activity request
        
    Returns:
        BaseResponse with activity details
        
    Raises:
        HTTPException: If activity not found
    """
    try:
        logger.info(f"Getting activity: {request.activity_id}")
        
        activity = await activity_service.get_activity(request.activity_id)
        
        if not activity:
            raise HTTPException(status_code=404, detail="Activity not found")
        
        activity_data = {
            "id": activity.id,
            "title": activity.title,
            "description": activity.description,
            "activity_type": activity.activity_type.value,
            "status": activity.status.value,
            "created_by": activity.created_by,
            "created_at": activity.created_at.isoformat(),
            "updated_at": activity.updated_at.isoformat(),
            "due_date": activity.due_date.isoformat() if activity.due_date else None,
            "points": activity.points,
            "tags": activity.tags,
            "submission_count": activity.submission_count
        }
        
        logger.info(f"Activity found: {activity.title}")
        
        return BaseResponse(
            success=True,
            message="Activity retrieved successfully",
            data=activity_data
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting activity: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get activity: {str(e)}"
        )
