import React from 'react';
import { colors, typography, shadows, shape } from '@/styles/design-system';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'text' | 'danger';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  isLoading?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      children,
      variant = 'primary',
      size = 'medium',
      fullWidth = false,
      startIcon,
      endIcon,
      isLoading = false,
      disabled = false,
      className = '',
      ...props
    },
    ref
  ) => {
    const baseStyles = {
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: typography.fontFamily,
      fontWeight: typography.button.fontWeight,
      lineHeight: typography.button.lineHeight,
      letterSpacing: typography.button.letterSpacing,
      textTransform: typography.button.textTransform as any,
      border: 'none',
      borderRadius: shape.borderRadius,
      cursor: 'pointer',
      transition: 'all 0.2s ease-in-out',
      '&:focus': {
        outline: 'none',
        boxShadow: `0 0 0 3px ${colors.primary[100]}`,
      },
      '&:disabled': {
        opacity: 0.6,
        cursor: 'not-allowed',
      },
    };

    const sizeStyles = {
      small: {
        fontSize: '0.75rem',
        padding: '0.25rem 0.75rem',
      },
      medium: {
        fontSize: '0.875rem',
        padding: '0.5rem 1rem',
      },
      large: {
        fontSize: '1rem',
        padding: '0.75rem 1.5rem',
      },
    };

    const variantStyles = {
      primary: {
        backgroundColor: colors.primary[500],
        color: 'white',
        '&:hover:not(:disabled)': {
          backgroundColor: colors.primary[600],
        },
        '&:active:not(:disabled)': {
          backgroundColor: colors.primary[700],
        },
      },
      secondary: {
        backgroundColor: colors.secondary[500],
        color: 'white',
        '&:hover:not(:disabled)': {
          backgroundColor: colors.secondary[600],
        },
        '&:active:not(:disabled)': {
          backgroundColor: colors.secondary[700],
        },
      },
      outline: {
        backgroundColor: 'transparent',
        color: colors.primary[500],
        border: `1px solid ${colors.primary[500]}`,
        '&:hover:not(:disabled)': {
          backgroundColor: `${colors.primary[50]}80`,
        },
        '&:active:not(:disabled)': {
          backgroundColor: `${colors.primary[100]}80`,
        },
      },
      text: {
        backgroundColor: 'transparent',
        color: colors.primary[500],
        '&:hover:not(:disabled)': {
          backgroundColor: `${colors.neutral[100]}80`,
        },
        '&:active:not(:disabled)': {
          backgroundColor: `${colors.neutral[200]}80`,
        },
      },
      danger: {
        backgroundColor: colors.error.main,
        color: 'white',
        '&:hover:not(:disabled)': {
          backgroundColor: colors.error.dark,
        },
        '&:active:not(:disabled)': {
          backgroundColor: colors.error.dark,
          opacity: 0.9,
        },
      },
    };

    const buttonStyles = {
      ...baseStyles,
      ...sizeStyles[size],
      ...variantStyles[variant],
      width: fullWidth ? '100%' : 'auto',
      opacity: isLoading ? 0.7 : 1,
    };

    return (
      <button
        ref={ref}
        style={buttonStyles as React.CSSProperties}
        disabled={disabled || isLoading}
        className={`button ${className}`}
        {...props}
      >
        {isLoading && (
          <span style={{ marginRight: '0.5rem' }}>
            <Spinner size={16} color="currentColor" />
          </span>
        )}
        {startIcon && !isLoading && (
          <span style={{ marginRight: '0.5rem' }}>{startIcon}</span>
        )}
        {children}
        {endIcon && <span style={{ marginLeft: '0.5rem' }}>{endIcon}</span>}
      </button>
    );
  }
);

// Simple Spinner component for loading state
const Spinner = ({ size = 16, color = 'currentColor' }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 38 38"
    xmlns="http://www.w3.org/2000/svg"
    stroke={color}
  >
    <g fill="none" fillRule="evenodd">
      <g transform="translate(1 1)" strokeWidth="2">
        <circle strokeOpacity=".5" cx="18" cy="18" r="18" />
        <path d="M36 18c0-9.94-8.06-18-18-18">
          <animateTransform
            attributeName="transform"
            type="rotate"
            from="0 18 18"
            to="360 18 18"
            dur="1s"
            repeatCount="indefinite"
          />
        </path>
      </g>
    </g>
  </svg>
);

export { Button };
