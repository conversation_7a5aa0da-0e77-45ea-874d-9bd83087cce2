{"name": "onboarding-mcp", "version": "1.0.0", "description": "User onboarding, authentication, and profile management", "author": "MyVillage AI Team", "tools": [{"name": "create_user", "description": "Create a new user account with name, email, and password", "parameters": {"type": "object", "properties": {"name": {"type": "string", "description": "Full name of the user", "minLength": 1, "maxLength": 100}, "email": {"type": "string", "format": "email", "description": "Email address of the user"}, "password": {"type": "string", "description": "Password for the account (minimum 8 characters)", "minLength": 8}, "phone_number": {"type": "string", "description": "Optional phone number"}}, "required": ["name", "email", "password"]}}, {"name": "login_user", "description": "Authenticate user and return access token", "parameters": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "description": "User's email address"}, "password": {"type": "string", "description": "User's password"}}, "required": ["email", "password"]}}, {"name": "verify_otp", "description": "Verify OTP code sent to user's email or phone", "parameters": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "description": "User's email address"}, "otp": {"type": "string", "description": "6-digit OTP code", "minLength": 6, "maxLength": 6, "pattern": "^[0-9]{6}$"}}, "required": ["email", "otp"]}}, {"name": "update_profile", "description": "Update user profile information", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "User's unique identifier"}, "data": {"type": "object", "description": "Profile data to update", "properties": {"name": {"type": "string", "description": "Updated name"}, "phone_number": {"type": "string", "description": "Updated phone number"}}}}, "required": ["user_id", "data"]}}]}