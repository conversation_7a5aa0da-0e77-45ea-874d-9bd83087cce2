from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
import os
import uuid
import boto3
from botocore.exceptions import Client<PERSON>rror
from fastapi import HTTPException, status, UploadFile

from ..models.submission import (
    Submission as SubmissionModel,
    SubmissionCreate,
    SubmissionUpdate,
    SubmissionListResponse,
    SubmissionStatus,
    MediaItem,
    MediaType
)
from ..core.database import get_db
from ..core.logging import get_logger
from ..core.config import settings

logger = get_logger(__name__)

class SubmissionService:
    def __init__(self):
        self.db = get_db()
        self.table_name = os.getenv('DYNAMODB_SUBMISSIONS_TABLE', 'submissions')
        self.table = self.db.get_table(self.table_name)
        self.media_base_path = settings.MEDIA_BASE_PATH if hasattr(settings, 'MEDIA_BASE_PATH') else "/var/www/media"
        
        # Create media directory if it doesn't exist
        os.makedirs(self.media_base_path, exist_ok=True)

    async def _save_media_file(self, file: UploadFile, submission_id: str, media_type: MediaType) -> Dict[str, str]:
        """
        Save uploaded media file and return file metadata.
        
        In a production environment, you might want to upload to S3 instead of local storage.
        """
        try:
            # Create submission directory if it doesn't exist
            submission_dir = os.path.join(self.media_base_path, "submissions", submission_id)
            os.makedirs(submission_dir, exist_ok=True)
            
            # Generate unique filename
            file_ext = os.path.splitext(file.filename)[1] if file.filename else '.bin'
            filename = f"{uuid.uuid4()}{file_ext}"
            file_id = str(uuid.uuid4())
            file_path = os.path.join(submission_dir, f"{file_id}{file_ext}")
            
            # Save file
            with open(file_path, "wb") as f:
                content = await file.read()
                f.write(content)
            
            # In a real app, you'd store the file in S3 and return the URL
            return {
                "id": file_id,
                "url": f"/media/submissions/{submission_id}/{file_id}{file_ext}",
                "type": media_type.value,
                "file_name": file.filename or f"file{file_ext}",
                "size": len(content),
                "created_at": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error saving media file: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to save media file: {str(e)}"
            )

    async def create_submission(
        self, 
        submission: SubmissionCreate,
        files: List[UploadFile] = None
    ) -> SubmissionModel:
        """Create a new submission."""
        try:
            submission_id = str(uuid.uuid4())
            now = datetime.now(timezone.utc).isoformat()
            
            # Handle file uploads if any
            media_items = []
            if files:
                for file in files:
                    # Determine media type from file extension or content type
                    content_type = file.content_type or ""
                    if any(ext in content_type for ext in ["image/", ".jpg", ".jpeg", ".png", ".gif"]):
                        media_type = MediaType.IMAGE
                    elif any(ext in content_type for ext in ["video/", ".mp4", ".mov", ".avi"]):
                        media_type = MediaType.VIDEO
                    elif any(ext in content_type for ext in ["audio/", ".mp3", ".wav"]):
                        media_type = MediaType.AUDIO
                    else:
                        media_type = MediaType.DOCUMENT
                    
                    media_item = await self._save_media_file(
                        file=file,
                        submission_id=submission_id,
                        media_type=media_type
                    )
                    media_items.append(media_item)
            
            # Create submission item
            submission_data = submission.dict(exclude={"media_files"})
            submission_data.update({
                "id": submission_id,
                "status": SubmissionStatus.SUBMITTED.value,
                "media_files": media_items,
                "created_at": now,
                "updated_at": now,
                "likes": [],
                "like_count": 0
            })
            
            # Store in DynamoDB
            self.table.put_item(Item=submission_data)
            
            return SubmissionModel(**submission_data)
            
        except Exception as e:
            logger.error(f"Error creating submission: {str(e)}")
            if isinstance(e, HTTPException):
                raise
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create submission: {str(e)}"
            )

    async def get_submission(self, submission_id: str) -> Optional[SubmissionModel]:
        """Get a submission by ID."""
        try:
            response = self.table.get_item(Key={"id": submission_id})
            if 'Item' not in response:
                return None
                
            return SubmissionModel(**response['Item'])
            
        except ClientError as e:
            logger.error(f"DynamoDB error getting submission {submission_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve submission"
            )
        except Exception as e:
            logger.error(f"Error getting submission {submission_id}: {str(e)}")
            if isinstance(e, HTTPException):
                raise
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve submission"
            )

    async def update_submission(
        self,
        submission_id: str,
        submission_update: SubmissionUpdate,
        current_user_id: str
    ) -> SubmissionModel:
        """Update a submission."""
        try:
            submission = await self.get_submission(submission_id)
            if not submission:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Submission not found"
                )
            
            # Check if user has permission to update
            if str(submission.submitted_by) != current_user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Not authorized to update this submission"
                )
            
            update_data = submission_update.dict(exclude_unset=True)
            
            # Handle status changes
            if "status" in update_data and update_data["status"] != submission.status:
                # Additional validation for status transitions can be added here
                pass
            
            # Update fields
            for field, value in update_data.items():
                setattr(submission, field, value)
            
            submission.updated_at = datetime.now(timezone.utc).isoformat()
            
            # Store in DynamoDB
            self.table.update_item(
                Key={"id": submission_id},
                UpdateExpression="set #status = :status, updated_at = :updated_at",
                ExpressionAttributeNames={
                    "#status": "status"
                },
                ExpressionAttributeValues={
                    ":status": submission.status,
                    ":updated_at": submission.updated_at
                }
            )
            
            return SubmissionModel(**submission.__dict__)
            
        except Exception as e:
            logger.error(f"Error updating submission: {str(e)}")
            if isinstance(e, HTTPException):
                raise
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update submission"
            )

    async def delete_submission(self, submission_id: str) -> None:
        """Delete a submission."""
        try:
            # Get submission first to handle media files
            submission = await self.get_submission(submission_id)
            if not submission:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Submission not found"
                )
            
            # Delete associated media files
            if submission.media_files:
                for media in submission.media_files:
                    try:
                        file_path = os.path.join(
                            self.media_base_path, 
                            "submissions", 
                            str(submission_id),
                            os.path.basename(media['url'])
                        )
                        if os.path.exists(file_path):
                            os.remove(file_path)
                    except Exception as e:
                        logger.warning(f"Failed to delete media file {media.get('url')}: {str(e)}")
            
            # Delete the submission
            self.table.delete_item(Key={"id": submission_id})
            
        except ClientError as e:
            logger.error(f"DynamoDB error deleting submission {submission_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete submission"
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting submission {submission_id}: {str(e)}")
            if isinstance(e, HTTPException):
                raise
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete submission"
            )

    async def list_submissions(
        self,
        created_by: Optional[str] = None,
        activity_id: Optional[str] = None,
        status: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> SubmissionListResponse:
        """List submissions with optional filtering."""
        try:
            # Note: For production, you should use a GSI for these queries
            # This is a simple implementation that scans the table
            scan_params = {}
            filter_expressions = []
            
            if created_by:
                filter_expressions.append("created_by = :created_by")
                scan_params['ExpressionAttributeValues'] = scan_params.get('ExpressionAttributeValues', {})
                scan_params['ExpressionAttributeValues'][":created_by"] = created_by
                
            if activity_id:
                filter_expressions.append("activity_id = :activity_id")
                scan_params['ExpressionAttributeValues'] = scan_params.get('ExpressionAttributeValues', {})
                scan_params['ExpressionAttributeValues'][":activity_id"] = activity_id
                
            if status:
                filter_expressions.append("#status = :status")
                scan_params['ExpressionAttributeNames'] = scan_params.get('ExpressionAttributeNames', {})
                scan_params['ExpressionAttributeNames']["#status"] = "status"
                scan_params['ExpressionAttributeValues'] = scan_params.get('ExpressionAttributeValues', {})
                scan_params['ExpressionAttributeValues'][":status"] = status
            
            if filter_expressions:
                scan_params['FilterExpression'] = " AND ".join(filter_expressions)
            
            # Scan the table with filters
            response = self.table.scan(**scan_params)
            items = response.get('Items', [])
            
            # Apply sorting (newest first) and pagination
            items.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            
            total = len(items)
            paginated_items = items[skip:skip + limit]
            
            return SubmissionListResponse(
                submissions=[SubmissionModel(**item) for item in paginated_items],
                total=total,
                limit=limit,
                offset=skip
            )
            
        except ClientError as e:
            logger.error(f"DynamoDB error listing submissions: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve submissions"
            )
        except Exception as e:
            logger.error(f"Error listing submissions: {str(e)}")
            if isinstance(e, HTTPException):
                raise
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve submissions"
            )

    async def grade_submission(
        self,
        submission_id: str,
        grade: float,
        feedback: str,
        grader_id: str
    ) -> SubmissionModel:
        """Grade a submission."""
        try:
            submission = await self.get_submission(submission_id)
            if not submission:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Submission not found"
                )
            
            # Check if user has permission to grade
            # TODO: Add proper permission check based on user role
            
            submission.grade = grade
            submission.feedback = feedback
            submission.graded_by = grader_id
            submission.graded_at = datetime.now(timezone.utc).isoformat()
            submission.status = SubmissionStatus.APPROVED if grade >= 60 else SubmissionStatus.REJECTED
            submission.updated_at = datetime.now(timezone.utc).isoformat()
            
            # Store in DynamoDB
            self.table.update_item(
                Key={"id": submission_id},
                UpdateExpression="set grade = :grade, feedback = :feedback, graded_by = :graded_by, graded_at = :graded_at, status = :status, updated_at = :updated_at",
                ExpressionAttributeValues={
                    ":grade": submission.grade,
                    ":feedback": submission.feedback,
                    ":graded_by": submission.graded_by,
                    ":graded_at": submission.graded_at,
                    ":status": submission.status,
                    ":updated_at": submission.updated_at
                }
            )
            
            return SubmissionModel(**submission.__dict__)
            
        except Exception as e:
            logger.error(f"Error grading submission: {str(e)}")
            if isinstance(e, HTTPException):
                raise
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to grade submission"
            )

    def _get_media_type(self, content_type: str) -> str:
        """Determine media type from content type."""
        if content_type.startswith('image/'):
            return MediaType.IMAGE
        elif content_type.startswith('video/'):
            return MediaType.VIDEO
        elif content_type.startswith('audio/'):
            return MediaType.AUDIO
        elif content_type in ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
            return MediaType.DOCUMENT
        else:
            return MediaType.OTHER

# Singleton instance of SubmissionService
_submission_service = None

def get_submission_service() -> SubmissionService:
    """
    Get a singleton instance of SubmissionService.
    
    Returns:
        SubmissionService: The singleton instance
    """
    global _submission_service
    if _submission_service is None:
        _submission_service = SubmissionService()
    return _submission_service
