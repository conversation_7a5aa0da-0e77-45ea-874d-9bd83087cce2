'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import {
  PlusCircle,
  Menu,
  X,
  Activity,
  Server,
  CheckCircle2,
  XCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { authUtils } from '@/lib/auth'
import { chatApi, Conversation } from '@/lib/api'
import { formatDistanceToNow } from 'date-fns'

interface SidebarProps {
  onClearChat: () => void
  onSelectConversation?: (conversation: Conversation) => void
  currentConversationId?: string | null
}

export default function Sidebar({ onClearChat, onSelectConversation, currentConversationId }: SidebarProps) {
  const [isOpen, setIsOpen] = useState(true)
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [user, setUser] = useState<ReturnType<typeof authUtils.getUser> | null>(null)
  const [isMounted, setIsMounted] = useState(false)

  console.log("conversations", conversations)
  const mcps = [
    { name: "Orchestrator", port: "8100", status: "online" },
    { name: "Onboarding", port: "8001", status: "online" },
    { name: "Activities", port: "8002", status: "online" },
    { name: "Rewards", port: "8003", status: "online" },
    { name: "Chat", port: "8005", status: "online" },
  ]

  // Set mounted state
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Load conversations on mount
  useEffect(() => {
    if (!isMounted) return

    const loadConversations = async () => {
      const currentUser = authUtils.getUser()
      setUser(currentUser)

      if (currentUser) {
        setIsLoading(true)
        try {
          const data = await chatApi.getConversations(currentUser.id)
          setConversations(data)
        } catch (error) {
          console.error('Failed to load conversations', error)
          // Silently fail - empty conversation list is fine
        } finally {
          setIsLoading(false)
        }
      }
    }

    loadConversations()

    // Refresh conversations every 30 seconds
    const interval = setInterval(loadConversations, 30000)
    return () => clearInterval(interval)
  }, [isMounted])

  // Group conversations by date
  const groupedConversations = conversations.reduce((groups, conversation) => {
    const date = new Date(conversation.lastMessageTime)
    const now = new Date()
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))

    let groupName = 'Older'
    if (diffDays === 0) groupName = 'Today'
    else if (diffDays === 1) groupName = 'Yesterday'
    else if (diffDays < 7) groupName = 'Previous 7 Days'
    else if (diffDays < 30) groupName = 'Previous 30 Days'

    if (!groups[groupName]) groups[groupName] = []
    groups[groupName].push(conversation)
    return groups
  }, {} as Record<string, Conversation[]>)

  const groupOrder = ['Today', 'Yesterday', 'Previous 7 Days', 'Previous 30 Days', 'Older']

  return (
    <>
      {/* Mobile toggle */}
      <Button
        onClick={() => setIsOpen(!isOpen)}
        variant="outline"
        size="icon"
        className="lg:hidden fixed top-4 left-4 z-50"
      >
        {isOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
      </Button>

      {/* Overlay for mobile */}
      {isOpen && (
        <div
          onClick={() => setIsOpen(false)}
          className="lg:hidden fixed inset-0 bg-background/80 backdrop-blur-sm z-40"
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed lg:relative z-40 w-64 h-screen transition-transform duration-300 flex flex-col",
          "bg-card border-r",
          isOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
        )}
      >
        {/* Header */}
        <div className="p-4 border-b">
          <Button
            onClick={onClearChat}
            className="w-full"
            variant="default"
          >
            <PlusCircle className="w-5 h-5 mr-2" />
            New Chat
          </Button>
        </div>

        {/* Chat History */}
        <ScrollArea className="flex-1 p-4">
          {!isMounted || isLoading ? (
            <div className="text-center text-muted-foreground text-sm py-8">
              Loading...
            </div>
          ) : !user ? (
            <div className="text-center text-muted-foreground text-sm py-8">
              Please log in to view chat history
            </div>
          ) : conversations.length === 0 ? (
            <div className="text-center text-muted-foreground text-sm py-8">
              No chat history yet
            </div>
          ) : (
            <div className="space-y-6">
              {groupOrder.map((groupName) => {
                const groupConversations = groupedConversations[groupName]
                if (!groupConversations || groupConversations.length === 0) return null

                return (
                  <div key={groupName}>
                    <h3 className="text-xs font-semibold text-muted-foreground uppercase mb-3 px-2">
                      {groupName}
                    </h3>
                    <div className="space-y-1">
                      {groupConversations.map((conv) => (
                        <Button
                          key={conv.conversationId}
                          variant={currentConversationId === conv.conversationId ? "secondary" : "ghost"}
                          className="w-full justify-start text-left h-auto py-2 px-2 font-normal truncate"
                          onClick={() => onSelectConversation?.(conv)}
                        >
                          <div className="flex flex-col w-full overflow-hidden">
                            <span className="truncate text-sm">{conv.title}</span>
                            <span className="text-[10px] text-muted-foreground truncate">
                              {formatDistanceToNow(new Date(conv.lastMessageTime), { addSuffix: true })}
                            </span>
                          </div>
                        </Button>
                      ))}
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </ScrollArea>

        {/* Footer - Service Status */}
        <div className="p-4 border-t">
          <Accordion type="single" collapsible>
            <AccordionItem value="mcps" className="border-none">
              <AccordionTrigger className="py-2 hover:no-underline">
                <div className="flex items-center gap-2">
                  <Server className="w-4 h-4 text-muted-foreground" />
                  <h3 className="text-xs font-semibold text-muted-foreground uppercase">
                    MCPs
                  </h3>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="space-y-2 pt-2">
                  {mcps.map((mcp) => (
                    <Card key={mcp.port} className="p-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Activity className="w-3 h-3 text-muted-foreground" />
                          <span className="text-xs font-medium">{mcp.name}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          {mcp.status === "online" ? (
                            <CheckCircle2 className="w-3 h-3 text-primary" />
                          ) : (
                            <XCircle className="w-3 h-3 text-destructive" />
                          )}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </aside>
    </>
  )
}
