"""
DynamoDB database module.
Handles database connections and operations using boto3.
"""
import os
import boto3
from botocore.exceptions import Client<PERSON>rror
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class Database:
    """Database class for DynamoDB operations."""
    
    def __init__(self):
        """Initialize the database connection."""
        self._resource = None
        self._tables = {}
        
    @property
    def resource(self):
        """Get the DynamoDB resource."""
        if self._resource is None:
            try:
                self._resource = boto3.resource(
                    'dynamodb',
                    region_name=os.getenv('AWS_REGION', 'us-east-1'),
                    aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
                    aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
                    aws_session_token=os.getenv('AWS_SESSION_TOKEN')
                )
                logger.info("DynamoDB resource initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize DynamoDB resource: {str(e)}")
                raise
        return self._resource
    
    def get_table(self, table_name: str):
        """Get a DynamoDB table."""
        if table_name not in self._tables:
            try:
                table = self.resource.Table(table_name)
                # Verify the table exists by calling describe_table
                table.load()
                self._tables[table_name] = table
                logger.info(f"Table {table_name} loaded successfully")
            except self.resource.meta.client.exceptions.ResourceNotFoundException:
                logger.error(f"Table {table_name} not found")
                raise
            except ClientError as e:
                logger.error(f"Error accessing table {table_name}: {str(e)}")
                raise
        return self._tables[table_name]
    
    def create_table(self, table_name: str, key_schema: list, attribute_definitions: list, **kwargs) -> bool:
        """
        Create a new DynamoDB table.
        
        Args:
            table_name: Name of the table to create
            key_schema: List of key schema definitions
            attribute_definitions: List of attribute definitions
            **kwargs: Additional parameters for table creation
            
        Returns:
            bool: True if table was created successfully, False otherwise
        """
        try:
            table = self.resource.create_table(
                TableName=table_name,
                KeySchema=key_schema,
                AttributeDefinitions=attribute_definitions,
                **kwargs
            )
            # Wait until the table exists
            table.wait_until_exists()
            self._tables[table_name] = table
            logger.info(f"Table {table_name} created successfully")
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceInUseException':
                logger.warning(f"Table {table_name} already exists")
                return True
            logger.error(f"Error creating table {table_name}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error creating table {table_name}: {str(e)}")
            return False

# Singleton instance
_db_instance = None

def get_db() -> Database:
    """
    Get the database instance (singleton).
    
    Returns:
        Database: The database instance
    """
    global _db_instance
    if _db_instance is None:
        _db_instance = Database()
    return _db_instance

def get_table(table_name: str):
    """
    Get a DynamoDB table.
    
    Args:
        table_name: Name of the table to get
        
    Returns:
        Table: The DynamoDB table
    """
    return get_db().get_table(table_name)

def create_table(table_name: str, key_schema: list, attribute_definitions: list, **kwargs) -> bool:
    """
    Create a new DynamoDB table.
    
    Args:
        table_name: Name of the table to create
        key_schema: List of key schema definitions
        attribute_definitions: List of attribute definitions
        **kwargs: Additional parameters for table creation
        
    Returns:
        bool: True if table was created successfully, False otherwise
    """
    return get_db().create_table(table_name, key_schema, attribute_definitions, **kwargs)

# Initialize required tables on import
if os.getenv('INIT_TABLES', 'false').lower() == 'true':
    try:
        # Example table creation (modify according to your needs)
        create_table(
            table_name=os.getenv('DYNAMODB_ACTIVITIES_TABLE', 'activities'),
            key_schema=[
                {'AttributeName': 'id', 'KeyType': 'HASH'},  # Partition key
            ],
            attribute_definitions=[
                {'AttributeName': 'id', 'AttributeType': 'S'},
            ],
            ProvisionedThroughput={
                'ReadCapacityUnits': 5,
                'WriteCapacityUnits': 5
            }
        )
        
        create_table(
            table_name=os.getenv('DYNAMODB_SUBMISSIONS_TABLE', 'submissions'),
            key_schema=[
                {'AttributeName': 'id', 'KeyType': 'HASH'},  # Partition key
            ],
            attribute_definitions=[
                {'AttributeName': 'id', 'AttributeType': 'S'},
            ],
            ProvisionedThroughput={
                'ReadCapacityUnits': 5,
                'WriteCapacityUnits': 5
            }
        )
        
        logger.info("Database tables initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database tables: {str(e)}")
        raise
