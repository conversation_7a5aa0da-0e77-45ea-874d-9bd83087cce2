"""Intent detection service with NLU integration."""
import logging
from typing import Dict, Optional, Any
from .nlu_service import nlu_service

logger = logging.getLogger(__name__)


class IntentDetector:
    """Detect user intent from message with advanced NLU capabilities."""
    
    # Intent keywords mapping (kept for backward compatibility)
    INTENT_KEYWORDS = {
        "signup": ["sign up", "signup", "register", "create account", "new account", "join"],
        "login": ["log in", "login", "sign in", "signin", "authenticate"],
        "activity_list": ["activities", "what can i do", "show activities", "list activities", "events"],
        "activity_create": ["create activity", "new activity", "add activity"],
        "activity_submit": ["submit", "submit assignment", "turn in", "hand in"],
        "submission_list": ["submissions", "show submissions", "list submissions", "view submissions", "my submissions", "all submissions"],
        "rewards_get": ["rewards", "points", "my rewards", "check rewards", "balance"],
        "rewards_redeem": ["redeem", "use points", "spend points"],
        "approval_pending": ["pending", "approvals", "need approval", "waiting approval"]
    }
    
    def __init__(self):
        """Initialize intent detector with NLU service."""
        self.nlu = nlu_service
    
    def detect(self, message: str) -> str:
        """
        Detect intent from user message (synchronous, backward compatible).
        
        Args:
            message: User message
            
        Returns:
            Intent label
        """
        message_lower = message.lower()
        
        # Check each intent's keywords
        for intent, keywords in self.INTENT_KEYWORDS.items():
            for keyword in keywords:
                if keyword in message_lower:
                    logger.info(f"Detected intent: {intent} (keyword: {keyword})")
                    return intent
        
        # Default to general query
        logger.info("No specific intent detected, defaulting to 'general'")
        return "general"
    
    async def detect_advanced(self, message: str, context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Detect intent using advanced NLU with entity extraction.
        
        Args:
            message: User message
            context: Optional context (user info, conversation state, etc.)
            
        Returns:
            Dictionary containing:
            - intent: Primary detected intent
            - entities: Extracted entities
            - confidence: Confidence score
            - sentiment: Detected sentiment
            - full_analysis: Complete NLU analysis
        """
        # Perform full NLU analysis
        analysis = await self.nlu.analyze_intent(message, context)
        
        # Map NLU intent to our intent system
        intent = self._map_nlu_intent(analysis["primary_intent"], analysis["entities"])
        
        result = {
            "intent": intent,
            "entities": analysis["entities"],
            "confidence": analysis["confidence"],
            "sentiment": analysis["sentiment"],
            "action": analysis.get("action"),
            "secondary_intents": analysis.get("secondary_intents", []),
            "full_analysis": analysis
        }
        
        logger.info(f"Advanced intent detection: {intent} (confidence: {analysis['confidence']:.2f})")
        
        return result
    
    def _map_nlu_intent(self, nlu_intent: str, entities: Dict) -> str:
        """
        Map NLU intent to system intent, considering entities.
        
        Args:
            nlu_intent: Intent from NLU service
            entities: Extracted entities
            
        Returns:
            Mapped intent
        """
        # Direct mapping for most intents
        intent_mapping = {
            "signup": "signup",
            "login": "login",
            "activity_list": "activity_list",
            "activity_create": "activity_create",
            "activity_submit": "activity_submit",
            "submission_list": "submission_list",
            "rewards_get": "rewards_get",
            "rewards_redeem": "rewards_redeem",
            "approval_pending": "approval_pending"
        }
        
        mapped_intent = intent_mapping.get(nlu_intent, "general")
        
        # Entity-based refinement
        # If we have a status entity with submission intent, it's likely a filtered list
        if mapped_intent == "submission_list" and "status" in entities:
            logger.info(f"Refined intent: submission_list with status filter")
        
        # If we have activity_type entity, it might refine the intent
        if "activity_type" in entities:
            activity_type = entities["activity_type"]
            logger.info(f"Intent involves {activity_type} activity type")
        
        return mapped_intent
    
    def get_mcp_service(self, intent: str) -> str:
        """
        Map intent to MCP service.
        
        Args:
            intent: Detected intent
            
        Returns:
            MCP service name
        """
        intent_to_service = {
            "signup": "onboarding",
            "login": "onboarding",
            "activity_list": "activities",
            "activity_create": "activities",
            "activity_submit": "activities",
            "submission_list": "activities",
            "rewards_get": "rewards",
            "rewards_redeem": "rewards",
            "approval_pending": "approval",
            "general": "activities"  # Default
        }
        
        return intent_to_service.get(intent, "activities")
    
    def extract_filters_from_entities(self, entities: Dict) -> Dict[str, Any]:
        """
        Convert extracted entities into filter parameters.
        
        Args:
            entities: Extracted entities from NLU
            
        Returns:
            Dictionary of filter parameters
        """
        filters = {}
        
        # Map entity types to filter parameters
        if "activity_type" in entities:
            filters["activity_type"] = entities["activity_type"]
        
        if "status" in entities:
            filters["status"] = entities["status"]
        
        if "city" in entities:
            filters["city"] = entities["city"]
        
        if "date" in entities:
            date_info = entities["date"]
            if isinstance(date_info, dict) and "value" in date_info:
                filters["date"] = date_info["value"]
        
        if "date_range" in entities:
            range_info = entities["date_range"]
            if isinstance(range_info, dict):
                filters["start_date"] = range_info.get("start")
                filters["end_date"] = range_info.get("end")
        
        if "activity_id" in entities:
            filters["activity_id"] = entities["activity_id"]
        
        if "user_id" in entities:
            filters["user_id"] = entities["user_id"]
        
        if "subject" in entities:
            filters["subject"] = entities["subject"]
        
        logger.info(f"Extracted filters: {filters}")
        
        return filters
