import sys
from pathlib import Path
import asyncio
import logging

# Add common to path
# Assuming this script is in mcp-activities/mcp_server.py
# common is in ../common
sys.path.insert(0, str(Path(__file__).parent.parent / "common"))

from mcp.server.fastmcp import FastMCP
from app.services.activity_service import ActivityService
from app.services.submission_service import SubmissionService
from app.models.activity import ActivityCreate, ActivityType
from app.models.submission import SubmissionCreate

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize MCP Server
mcp = FastMCP("Activities")

# Initialize Services
activity_service = ActivityService()
submission_service = SubmissionService()

@mcp.tool()
async def list_activities(
    activity_type: str = None,
    status: str = None,
    city_id: str = None,
    limit: int = 10
) -> dict:
    """
    List available activities with optional filters.
    
    Args:
        activity_type: Filter by type (assignment, event, survey, discussion, volunteer)
        status: Filter by status (draft, published, archived, completed)
        city_id: Filter by city ID
        limit: Maximum results (1-100)
    """
    try:
        logger.info(f"Listing activities with filters: type={activity_type}, status={status}, city_id={city_id}, limit={limit}")
        
        activities = await activity_service.list_activities(
            activity_type=activity_type,
            status=status,
            city_id=city_id,
            limit=limit
        )
        
        logger.info(f"Found {len(activities)} activities")
        
        # Serialize results
        activities_data = [
            {
                "id": activity.id,
                "title": activity.title,
                "description": activity.description,
                "activity_type": activity.activity_type.value,
                "status": activity.status.value,
                "created_by": activity.created_by,
                "created_at": activity.created_at.isoformat(),
                "due_date": activity.due_date.isoformat() if activity.due_date else None,
                "points": activity.points,
                "city_id": activity.city_id
            }
            for activity in activities
        ]
        
        return {"activities": activities_data, "count": len(activities_data)}
    except Exception as e:
        logger.error(f"Error listing activities: {str(e)}", exc_info=True)
        return {"activities": [], "count": 0, "error": str(e)}

@mcp.tool()
async def get_activity(activity_id: str) -> dict:
    """
    Get detailed information about a specific activity.
    
    Args:
        activity_id: Activity ID
    """
    activity = await activity_service.get_activity(activity_id)
    if not activity:
        return {"error": "Activity not found"}
        
    return {
        "id": activity.id,
        "title": activity.title,
        "description": activity.description,
        "activity_type": activity.activity_type.value,
        "status": activity.status.value,
        "created_by": activity.created_by,
        "created_at": activity.created_at.isoformat(),
        "due_date": activity.due_date.isoformat() if activity.due_date else None,
        "points": activity.points,
        "city_id": activity.city_id
    }

@mcp.tool()
async def create_activity(
    title: str,
    description: str,
    activity_type: str,
    created_by: str,
    city_id: str = None,
    points: int = 0
) -> dict:
    """
    Create a new activity.
    
    Args:
        title: Activity title
        description: Description
        activity_type: Type (assignment, event, survey, discussion, volunteer)
        created_by: Creator user ID
        city_id: Optional city ID
        points: Points for completion
    """
    activity_data = ActivityCreate(
        title=title,
        description=description,
        activity_type=activity_type,
        created_by=created_by,
        city_id=city_id,
        points=points
    )
    
    activity = await activity_service.create_activity(activity_data)
    
    return {
        "id": activity.id,
        "message": "Activity created successfully"
    }

@mcp.tool()
async def submit_assignment(
    activity_id: str,
    user_id: str,
    title: str,
    description: str = None,
    content: str = None,
    is_public: bool = True
) -> dict:
    """
    Submit an assignment for an activity.
    
    Args:
        activity_id: Activity ID
        user_id: User ID
        title: Submission title
        description: Submission description
        content: Submission content
        is_public: Whether submission is public
    """
    submission_data = SubmissionCreate(
        activity_id=activity_id,
        user_id=user_id,
        title=title,
        description=description,
        content=content,
        is_public=is_public
    )
    
    submission = await submission_service.create_submission(submission_data)
    
    return {
        "id": submission.id,
        "message": "Assignment submitted successfully"
    }

@mcp.tool()
async def grade_submission(
    submission_id: str,
    grade: float,
    feedback: str,
    grader_id: str
) -> dict:
    """
    Grade a submission.
    
    Args:
        submission_id: Submission ID
        grade: Grade (0-100)
        feedback: Feedback text
        grader_id: Grader ID
    """
    submission = await submission_service.grade_submission(
        submission_id=submission_id,
        grade=grade,
        feedback=feedback,
        grader_id=grader_id
    )
    
    if not submission:
        return {"error": "Submission not found or grading failed"}
        
    return {
        "id": submission.id,
        "grade": submission.grade,
        "status": submission.status.value,
        "message": "Submission graded successfully"
    }

@mcp.tool()
async def list_submissions(
    activity_id: str = None,
    user_id: str = None,
    status: str = None,
    city_id: str = None,
    limit: int = 10
) -> dict:
    """
    List submissions with optional filters.
    
    Args:
        activity_id: Filter by activity ID
        user_id: Filter by user ID
        status: Filter by status
        city_id: Filter by city ID
        limit: Maximum results
    """
    submissions = await submission_service.list_submissions(
        activity_id=activity_id,
        user_id=user_id,
        status=status,
        city_id=city_id,
        limit=limit
    )
    
    submissions_data = [
        {
            "id": s.id,
            "activity_id": s.activity_id,
            "user_id": s.user_id,
            "title": s.title,
            "status": s.status.value,
            "grade": s.grade,
            "submitted_at": s.submitted_at.isoformat() if s.submitted_at else None
        }
        for s in submissions
    ]
    
    return {"submissions": submissions_data, "count": len(submissions_data)}

if __name__ == "__main__":
    mcp.run()
