"""Tool: List Cities."""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "common"))

from fastapi import APIRouter, HTTPException, Query
from typing import List
import logging
import os
import boto3
from botocore.exceptions import ClientError

from common.models.base import BaseResponse

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/list_cities", response_model=BaseResponse)
async def list_cities(
    limit: int = Query(100, ge=1, le=500, description="Maximum number of results")
):
    """
    List all available cities.
    
    This tool retrieves a list of all active cities from DynamoDB.
    
    Args:
        limit: Maximum number of results (1-500, default: 100)
        
    Returns:
        BaseResponse with list of cities
    """
    try:
        logger.info(f"Listing cities: limit={limit}")
        
        # Get DynamoDB configuration
        table_name = os.getenv('DYNAMODB_CITIES_TABLE', 'cities')
        region = os.getenv('AWS_REGION', 'us-east-1')
        
        # Initialize DynamoDB client
        dynamodb = boto3.resource(
            'dynamodb',
            region_name=region,
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
            aws_session_token=os.getenv('AWS_SESSION_TOKEN')
        )
        
        table = dynamodb.Table(table_name)
        
        # Scan with filter to exclude deleted cities
        cities = []
        scan_params = {
            'FilterExpression': '#isDeleted <> :true',
            'ExpressionAttributeNames': {
                '#isDeleted': 'isDeleted'
            },
            'ExpressionAttributeValues': {
                ':true': 'true'
            },
            'Limit': limit
        }
        
        while True:
            response = table.scan(**scan_params)
            cities.extend(response.get('Items', []))
            
            # Check if we have enough cities or no more to scan
            if len(cities) >= limit or 'LastEvaluatedKey' not in response:
                break
                
            scan_params['ExclusiveStartKey'] = response['LastEvaluatedKey']
        
        # Sort by name
        cities.sort(key=lambda x: x.get('name', '').lower())
        
        # Limit results
        cities = cities[:limit]
        
        # Format city data
        cities_data = [
            {
                "id": city.get("id") or city.get("cityId"),
                "name": city.get("name"),
                "state": city.get("state"),
                "country": city.get("country"),
                "population": city.get("population"),
                "is_active": city.get("isActive", True)
            }
            for city in cities
        ]
        
        logger.info(f"Found {len(cities_data)} cities")
        
        return BaseResponse(
            success=True,
            message=f"Found {len(cities_data)} cities",
            data={"cities": cities_data, "count": len(cities_data)}
        )
    except ClientError as e:
        logger.error(f"DynamoDB error listing cities: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Database error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error listing cities: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list cities: {str(e)}"
        )
