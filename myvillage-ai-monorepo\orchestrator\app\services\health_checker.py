"""Health checker for all MCP services."""
import logging
from typing import Dict
from .onboarding_client import OnboardingClient
from .activities_client import ActivitiesClient
from .rewards_client import RewardsClient

logger = logging.getLogger(__name__)


async def check_all_services() -> Dict[str, Dict[str, any]]:
    """
    Check health of all MCP services.
    
    Returns:
        Dictionary with service health status
    """
    onboarding = OnboardingClient()
    activities = ActivitiesClient()
    rewards = RewardsClient()
    
    return {
        "onboarding": {
            "healthy": await onboarding.health_check(),
            "url": onboarding.base_url
        },
        "activities": {
            "healthy": await activities.health_check(),
            "url": activities.base_url
        },
        "rewards": {
            "healthy": await rewards.health_check(),
            "url": rewards.base_url
        }
    }
