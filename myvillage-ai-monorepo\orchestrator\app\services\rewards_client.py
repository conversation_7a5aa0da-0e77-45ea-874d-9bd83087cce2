"""Real HTTP client for Rewards MCP."""
import httpx
from typing import Dict, Any
import logging
from ..core.config import settings

logger = logging.getLogger(__name__)


class RewardsClient:
    """Client for calling Rewards MCP service."""
    
    def __init__(self):
        self.base_url = settings.rewards_mcp_url
        self.timeout = settings.mcp_timeout
    
    async def get_rewards(self, user_id: str) -> Dict[str, Any]:
        """Call get_rewards tool on Rewards MCP."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                logger.info(f"Calling rewards MCP: get_rewards")
                response = await client.post(
                    f"{self.base_url}/tools/get_rewards",
                    json={"user_id": user_id}
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"Error calling rewards MCP: {e}")
                raise
    
    async def calculate_points(
        self,
        activity_id: str,
        user_id: str,
        points: int = 50
    ) -> Dict[str, Any]:
        """Call calculate_points tool on Rewards MCP."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                logger.info(f"Calling rewards MCP: calculate_points")
                response = await client.post(
                    f"{self.base_url}/tools/calculate_points",
                    json={
                        "activity_id": activity_id,
                        "user_id": user_id,
                        "points": points
                    }
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"Error calling rewards MCP: {e}")
                raise
    
    async def health_check(self) -> bool:
        """Check if rewards MCP is healthy."""
        async with httpx.AsyncClient(timeout=5) as client:
            try:
                response = await client.get(f"{self.base_url}/health")
                return response.status_code == 200
            except:
                return False
