# 🏗️ MyVillage AI - Monorepo Migration & Architecture Guide

## 📋 Table of Contents

1. [Executive Summary](#executive-summary)
2. [Current State Analysis](#current-state-analysis)
3. [Target Architecture](#target-architecture)
4. [Monorepo Structure](#monorepo-structure)
5. [Migration Strategy](#migration-strategy)
6. [Step-by-Step Implementation](#step-by-step-implementation)
7. [Service Breakdown](#service-breakdown)
8. [Deployment Guide](#deployment-guide)
9. [Testing Strategy](#testing-strategy)
10. [Timeline & Resources](#timeline--resources)

---

## 📊 Executive Summary

### Current Situation
You have **two separate repositories**:
- `my_onboarding_api/` - Monolithic FastAPI app with all functionality
- `mvp-conversation-orchestrator/` - Basic orchestrator with mock clients

### Target Goal
Create a **unified monorepo** following the architecture pattern:
```
Custom Chatbot → Orchestrator → MCP Services → Database
                      ↓
                Intent Detection
                      ↓
                 Route to MCP
                      ↓
              Unified Response
```

### Key Benefits
✅ **Code Reusability** - Both ChatGPT and custom chatbots use same backends  
✅ **Clean Separation** - Each service has single responsibility  
✅ **Independent Scaling** - Services scale based on demand  
✅ **Easy Maintenance** - Clear boundaries, easier debugging  
✅ **Future-Proof** - Add new services without affecting existing ones

---

## 🔍 Current State Analysis

### Repository 1: `my_onboarding_api/`

**What it contains:**

```
my_onboarding_api/
├── app/
│   ├── api/
│   │   ├── auth.py                  # Authentication endpoints
│   │   └── endpoints/
│   │       ├── activities.py        # Activity management
│   │       └── submissions.py       # Submission handling
│   ├── services/
│   │   ├── auth_service.py          # Auth business logic
│   │   ├── activity_service.py      # Activity business logic
│   │   ├── submission_service.py    # Submission business logic
│   │   ├── intent_service.py        # Intent classification
│   │   ├── gemini_service.py        # Gemini AI integration
│   │   └── session_service.py       # Session management
│   ├── models/                      # Pydantic models
│   ├── core/                        # Config, logging, exceptions
│   └── middleware/                  # Security, logging middleware
├── mcp_server.py                    # MCP server (STDIO)
└── requirements.txt
```

**Problems:**
- ❌ Everything in one service (monolithic)
- ❌ Mixed responsibilities (onboarding + activities + submissions)
- ❌ No clear service boundaries
- ❌ Difficult to scale independently
- ❌ No MCP manifests for ChatGPT integration

### Repository 2: `mvp-conversation-orchestrator/`

**What it contains:**
```
mvp-conversation-orchestrator/
├── app/
│   ├── main.py                      # FastAPI app
│   ├── routers/
│   │   └── chat_router.py           # /chat endpoint
│   ├── services/
│   │   ├── intent_client.py         # Intent detection
│   │   ├── onboarding_client.py     # Mock onboarding client
│   │   └── activity_client.py       # Mock activity client
│   ├── schemas/
│   │   └── message_schema.py        # Request/response models
│   └── core/
│       └── config.py                # Configuration
└── requirements.txt
```

**Problems:**
- ❌ Mock clients (not calling real services)
- ❌ No actual MCP services to route to
- ❌ Incomplete intent detection
- ❌ Not integrated with my_onboarding_api

---

## 🎯 Target Architecture

### High-Level Overview

```
┌─────────────────────────────────────────────────────────────┐
│                     Frontend / Clients                       │
│  (Custom Chatbot UI, Mobile App, ChatGPT Desktop)           │
└────────────────────┬────────────────────────────────────────┘
                     │
                     ▼
        ┌────────────────────────┐
        │    Orchestrator        │
        │  (Intent Detection &   │
        │   Request Routing)     │
        └────────┬───────────────┘
                 │
    ┌────────────┼────────────┬──────────────┐
    ▼            ▼            ▼              ▼
┌─────────┐ ┌──────────┐ ┌─────────┐ ┌──────────┐
│Onboard  │ │Activities│ │ Rewards │ │ Approval │
│  MCP    │ │   MCP    │ │   MCP   │ │   MCP    │
└────┬────┘ └────┬─────┘ └────┬────┘ └────┬─────┘
     │           │            │           │
     └───────────┴────────────┴───────────┘
                     │
                     ▼
            ┌────────────────┐
            │   Database     │
            │  (DynamoDB)    │
            └────────────────┘
```

### Communication Patterns

**Pattern 1: Custom Chatbot (Orchestrator-Mediated)**
```
User Message → Orchestrator → Detect Intent → Route to MCP → Process → Return Response
```

**Pattern 2: ChatGPT (Direct MCP)**
```
User Message → ChatGPT reads manifest → Calls MCP directly → Process → Return Response
```

---

## 📁 Monorepo Structure

### Recommended Directory Layout

```
myvillage-ai-monorepo/
│
├── orchestrator/                           # Intent detection & routing
│   ├── app/
│   │   ├── main.py                        # FastAPI application
│   │   ├── routers/
│   │   │   └── chat_router.py             # /chat endpoint
│   │   ├── services/
│   │   │   ├── intent_detector.py         # Intent classification
│   │   │   ├── onboarding_client.py       # HTTP client for onboarding MCP
│   │   │   ├── activities_client.py       # HTTP client for activities MCP
│   │   │   ├── rewards_client.py          # HTTP client for rewards MCP
│   │   │   └── approval_client.py         # HTTP client for approval MCP
│   │   ├── schemas/
│   │   │   └── messages.py                # Request/response schemas
│   │   └── core/
│   │       ├── config.py                  # Configuration
│   │       └── logging.py                 # Logging setup
│   ├── tests/
│   ├── requirements.txt
│   ├── Dockerfile
│   └── README.md
│
├── mcp-onboarding/                        # User signup/login/verification
│   ├── app/
│   │   ├── main.py                        # FastAPI application
│   │   ├── tools/
│   │   │   ├── create_user.py             # Tool: Create user account
│   │   │   ├── login_user.py              # Tool: User login
│   │   │   ├── verify_otp.py              # Tool: OTP verification
│   │   │   └── update_profile.py          # Tool: Update user profile
│   │   ├── services/
│   │   │   ├── auth_service.py            # Authentication logic
│   │   │   └── user_service.py            # User management logic
│   │   ├── models/
│   │   │   └── user.py                    # User models
│   │   └── core/
│   │       ├── config.py
│   │       ├── database.py                # Database connection
│   │       └── security.py                # Security utilities
│   ├── mcp-manifest.json                  # MCP tool definitions
│   ├── tests/
│   ├── requirements.txt
│   ├── Dockerfile
│   └── README.md
│
├── mcp-activities/                        # Activity management & submissions
│   ├── app/
│   │   ├── main.py                        # FastAPI application
│   │   ├── tools/
│   │   │   ├── list_activities.py         # Tool: List activities
│   │   │   ├── get_activity.py            # Tool: Get activity details
│   │   │   ├── create_activity.py         # Tool: Create activity
│   │   │   ├── submit_assignment.py       # Tool: Submit assignment
│   │   │   ├── grade_submission.py        # Tool: Grade submission
│   │   │   └── like_submission.py         # Tool: Like submission
│   │   ├── services/
│   │   │   ├── activity_service.py        # Activity business logic
│   │   │   └── submission_service.py      # Submission business logic
│   │   ├── models/
│   │   │   ├── activity.py                # Activity models
│   │   │   └── submission.py              # Submission models
│   │   └── core/
│   │       ├── config.py
│   │       └── database.py
│   ├── mcp-manifest.json                  # MCP tool definitions
│   ├── tests/
│   ├── requirements.txt
│   ├── Dockerfile
│   └── README.md
│
├── mcp-rewards/                           # Rewards tracking & redemption
│   ├── app/
│   │   ├── main.py                        # FastAPI application
│   │   ├── tools/
│   │   │   ├── get_rewards.py             # Tool: Get user rewards
│   │   │   ├── calculate_points.py        # Tool: Calculate points
│   │   │   └── redeem_reward.py           # Tool: Redeem reward
│   │   ├── services/
│   │   │   └── rewards_service.py         # Rewards business logic
│   │   ├── models/
│   │   │   └── reward.py                  # Reward models
│   │   └── core/
│   │       ├── config.py
│   │       └── database.py
│   ├── mcp-manifest.json                  # MCP tool definitions
│   ├── tests/
│   ├── requirements.txt
│   ├── Dockerfile
│   └── README.md
│
├── mcp-approval/                          # Activity approval workflows
│   ├── app/
│   │   ├── main.py                        # FastAPI application
│   │   ├── tools/
│   │   │   ├── approve_activity.py        # Tool: Approve activity
│   │   │   ├── reject_activity.py         # Tool: Reject activity
│   │   │   └── get_pending.py             # Tool: Get pending approvals
│   │   ├── services/
│   │   │   └── approval_service.py        # Approval business logic
│   │   ├── models/
│   │   │   └── approval.py                # Approval models
│   │   └── core/
│   │       ├── config.py
│   │       └── database.py
│   ├── mcp-manifest.json                  # MCP tool definitions
│   ├── tests/
│   ├── requirements.txt
│   ├── Dockerfile
│   └── README.md
│
├── common/                                # Shared code across services
│   ├── models/
│   │   ├── base.py                        # Base models
│   │   ├── user.py                        # Shared user models
│   │   ├── activity.py                    # Shared activity models
│   │   └── response.py                    # Standard response models
│   ├── utils/
│   │   ├── validation.py                  # Input validation
│   │   ├── security.py                    # Security utilities
│   │   ├── database.py                    # Database utilities
│   │   └── logging.py                     # Logging utilities
│   ├── config/
│   │   └── settings.py                    # Shared configuration
│   ├── constants.py                       # Application constants
│   └── __init__.py
│
├── docker-compose.yml                     # Multi-container orchestration
├── docker-compose.dev.yml                 # Development environment
├── .env.example                           # Environment variables template
├── .gitignore
├── README.md                              # Main project documentation
├── ARCHITECTURE.md                        # Architecture documentation
├── DEPLOYMENT.md                          # Deployment guide
└── .github/
    └── workflows/
        ├── orchestrator-deploy.yml        # CI/CD for orchestrator
        ├── mcp-onboarding-deploy.yml      # CI/CD for onboarding MCP
        ├── mcp-activities-deploy.yml      # CI/CD for activities MCP
        ├── mcp-rewards-deploy.yml         # CI/CD for rewards MCP
        └── mcp-approval-deploy.yml        # CI/CD for approval MCP
```

---

## 🚀 Migration Strategy

### Phase-Based Approach (Recommended)

We'll use the **Strangler Pattern** to gradually migrate from two repos to monorepo:

```
Current State → Transition State → Final State
(2 repos)       (Monorepo + old)    (Monorepo only)
```

### Why Strangler Pattern?

✅ **Low Risk** - Incremental changes, easy to rollback  
✅ **No Downtime** - Old system runs while new is built  
✅ **Testable** - Validate each service before switching  
✅ **Reversible** - Can revert if issues arise

---

## 📝 Step-by-Step Implementation

### PHASE 1: Setup Monorepo Foundation (Week 1)

#### Step 1.1: Create Monorepo Structure

```bash
# Create new monorepo directory
mkdir myvillage-ai-monorepo
cd myvillage-ai-monorepo

# Initialize git
git init

# Create main directories
mkdir -p orchestrator mcp-onboarding mcp-activities mcp-rewards mcp-approval common

# Create subdirectories
for service in orchestrator mcp-onboarding mcp-activities mcp-rewards mcp-approval; do
    mkdir -p $service/{app,tests}
    touch $service/README.md
    touch $service/requirements.txt
    touch $service/Dockerfile
done

# Create common library structure
mkdir -p common/{models,utils,config}
touch common/__init__.py
```

#### Step 1.2: Create Shared Common Library

**File: `common/models/base.py`**

```python
"""Base models shared across all services."""
from pydantic import BaseModel, Field
from typing import Optional, Any, Dict
from datetime import datetime

class BaseResponse(BaseModel):
    """Standard response format for all services."""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class HealthResponse(BaseModel):
    """Health check response."""
    status: str
    service: str
    version: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
```

**File: `common/utils/validation.py`**
```python
"""Shared validation utilities."""
import re
from typing import Optional

def validate_email(email: str) -> bool:
    """Validate email format."""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def validate_phone(phone: str) -> bool:
    """Validate phone number format."""
    pattern = r'^\+?1?\d{9,15}$'
    return bool(re.match(pattern, phone))

def sanitize_input(text: str, max_length: Optional[int] = None) -> str:
    """Sanitize user input."""
    text = text.strip()
    if max_length:
        text = text[:max_length]
    return text
```

**File: `common/config/settings.py`**
```python
"""Shared configuration settings."""
from pydantic_settings import BaseSettings
from typing import Optional

class CommonSettings(BaseSettings):
    """Common settings for all services."""
    
    # Database
    database_url: str
    
    # Logging
    log_level: str = "INFO"
    
    # Security
    jwt_secret: str
    jwt_algorithm: str = "HS256"
    jwt_expiration: int = 3600
    
    # CORS
    cors_origins: list = ["*"]
    
    class Config:
        env_file = ".env"
```

#### Step 1.3: Create Docker Compose Configuration

**File: `docker-compose.yml`**
```yaml
version: '3.8'

services:
  orchestrator:
    build: ./orchestrator
    container_name: myvillage-orchestrator
    ports:
      - "8100:8100"
    environment:
      - ONBOARDING_MCP_URL=http://mcp-onboarding:8001
      - ACTIVITIES_MCP_URL=http://mcp-activities:8002
      - REWARDS_MCP_URL=http://mcp-rewards:8003
      - APPROVAL_MCP_URL=http://mcp-approval:8004
      - LOG_LEVEL=INFO
    depends_on:
      - mcp-onboarding
      - mcp-activities
      - mcp-rewards
      - mcp-approval
    networks:
      - myvillage-network
    restart: unless-stopped

  mcp-onboarding:
    build: ./mcp-onboarding
    container_name: myvillage-mcp-onboarding
    ports:
      - "8001:8001"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - PORT=8001
      - LOG_LEVEL=INFO
    networks:
      - myvillage-network
    restart: unless-stopped

  mcp-activities:
    build: ./mcp-activities
    container_name: myvillage-mcp-activities
    ports:
      - "8002:8002"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - PORT=8002
      - LOG_LEVEL=INFO
    networks:
      - myvillage-network
    restart: unless-stopped

  mcp-rewards:
    build: ./mcp-rewards
    container_name: myvillage-mcp-rewards
    ports:
      - "8003:8003"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - PORT=8003
      - LOG_LEVEL=INFO
    networks:
      - myvillage-network
    restart: unless-stopped

  mcp-approval:
    build: ./mcp-approval
    container_name: myvillage-mcp-approval
    ports:
      - "8004:8004"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - PORT=8004
      - LOG_LEVEL=INFO
    networks:
      - myvillage-network
    restart: unless-stopped

networks:
  myvillage-network:
    driver: bridge

volumes:
  postgres-data:
```

**File: `.env.example`**
```env
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/myvillage

# Security
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# External APIs
GEMINI_API_KEY=your-gemini-api-key
HF_TOKEN=your-huggingface-token

# Service URLs (for local development)
ONBOARDING_MCP_URL=http://localhost:8001
ACTIVITIES_MCP_URL=http://localhost:8002
REWARDS_MCP_URL=http://localhost:8003
APPROVAL_MCP_URL=http://localhost:8004

# Logging
LOG_LEVEL=INFO
```

---

### PHASE 2: Extract Onboarding MCP (Week 2)

#### Step 2.1: Create MCP Onboarding Service

**File: `mcp-onboarding/app/main.py`**
```python
"""Onboarding MCP Service - User signup, login, verification."""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import sys
from pathlib import Path

# Add common to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "common"))

from common.models.base import HealthResponse

app = FastAPI(
    title="Onboarding MCP",
    version="1.0.0",
    description="MCP service for user onboarding, authentication, and profile management"
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import routers
from .tools import create_user, login_user, verify_otp, update_profile

# Include tool routers
app.include_router(create_user.router, prefix="/tools", tags=["tools"])
app.include_router(login_user.router, prefix="/tools", tags=["tools"])
app.include_router(verify_otp.router, prefix="/tools", tags=["tools"])
app.include_router(update_profile.router, prefix="/tools", tags=["tools"])

@app.get("/", response_model=HealthResponse)
async def root():
    """Root endpoint."""
    return HealthResponse(
        status="healthy",
        service="Onboarding MCP",
        version="1.0.0"
    )

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        service="Onboarding MCP",
        version="1.0.0"
    )

@app.get("/manifest")
async def get_manifest():
    """Return MCP manifest for ChatGPT integration."""
    return {
        "name": "onboarding-mcp",
        "version": "1.0.0",
        "description": "User onboarding, authentication, and profile management",
        "tools": [
            {
                "name": "create_user",
                "description": "Create a new user account",
                "parameters": {
                    "name": {"type": "string", "required": True},
                    "email": {"type": "string", "required": True},
                    "password": {"type": "string", "required": True}
                }
            },
            {
                "name": "login_user",
                "description": "Authenticate user and return access token",
                "parameters": {
                    "email": {"type": "string", "required": True},
                    "password": {"type": "string", "required": True}
                }
            },
            {
                "name": "verify_otp",
                "description": "Verify OTP code for user",
                "parameters": {
                    "email": {"type": "string", "required": True},
                    "otp": {"type": "string", "required": True}
                }
            },
            {
                "name": "update_profile",
                "description": "Update user profile information",
                "parameters": {
                    "user_id": {"type": "string", "required": True},
                    "data": {"type": "object", "required": True}
                }
            }
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
```

**File: `mcp-onboarding/app/tools/create_user.py`**
```python
"""Tool: Create User Account."""
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, EmailStr
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "common"))

from common.models.base import BaseResponse
from ..services.auth_service import AuthService

router = APIRouter()
auth_service = AuthService()

class CreateUserRequest(BaseModel):
    name: str
    email: EmailStr
    password: str
    phone_number: str | None = None

@router.post("/create_user", response_model=BaseResponse)
async def create_user(request: CreateUserRequest):
    """
    Create a new user account.
    
    This tool creates a new user in the system with the provided information.
    """
    try:
        user = await auth_service.create_user(
            name=request.name,
            email=request.email,
            password=request.password,
            phone_number=request.phone_number
        )
        
        return BaseResponse(
            success=True,
            message="User created successfully",
            data={"user_id": user.id, "email": user.email}
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create user: {str(e)}")
```

**File: `mcp-onboarding/mcp-manifest.json`**
```json
{
  "name": "onboarding-mcp",
  "version": "1.0.0",
  "description": "User onboarding, authentication, and profile management",
  "author": "MyVillage AI Team",
  "tools": [
    {
      "name": "create_user",
      "description": "Create a new user account with name, email, and password",
      "parameters": {
        "type": "object",
        "properties": {
          "name": {
            "type": "string",
            "description": "Full name of the user"
          },
          "email": {
            "type": "string",
            "format": "email",
            "description": "Email address of the user"
          },
          "password": {
            "type": "string",
            "description": "Password for the account (min 8 characters)"
          },
          "phone_number": {
            "type": "string",
            "description": "Optional phone number"
          }
        },
        "required": ["name", "email", "password"]
      }
    },
    {
      "name": "login_user",
      "description": "Authenticate user and return access token",
      "parameters": {
        "type": "object",
        "properties": {
          "email": {
            "type": "string",
            "format": "email",
            "description": "User's email address"
          },
          "password": {
            "type": "string",
            "description": "User's password"
          }
        },
        "required": ["email", "password"]
      }
    },
    {
      "name": "verify_otp",
      "description": "Verify OTP code sent to user's email or phone",
      "parameters": {
        "type": "object",
        "properties": {
          "email": {
            "type": "string",
            "format": "email",
            "description": "User's email address"
          },
          "otp": {
            "type": "string",
            "description": "6-digit OTP code"
          }
        },
        "required": ["email", "otp"]
      }
    },
    {
      "name": "update_profile",
      "description": "Update user profile information",
      "parameters": {
        "type": "object",
        "properties": {
          "user_id": {
            "type": "string",
            "description": "User's unique identifier"
          },
          "data": {
            "type": "object",
            "description": "Profile data to update"
          }
        },
        "required": ["user_id", "data"]
      }
    }
  ]
}
```

#### Step 2.2: Copy Code from my_onboarding_api

**Files to copy:**
```bash
# From my_onboarding_api to mcp-onboarding
cp my_onboarding_api/app/services/auth_service.py mcp-onboarding/app/services/
cp my_onboarding_api/app/services/session_service.py mcp-onboarding/app/services/
cp my_onboarding_api/app/models/user.py mcp-onboarding/app/models/
cp my_onboarding_api/app/db/user.py mcp-onboarding/app/services/user_repository.py
cp my_onboarding_api/app/core/security.py mcp-onboarding/app/core/
cp my_onboarding_api/app/core/database.py mcp-onboarding/app/core/
```

**Refactor imports** to use common library where applicable.

---

### PHASE 3: Extract Activities MCP (Week 3)

#### Step 3.1: Create MCP Activities Service

**File: `mcp-activities/app/main.py`**
```python
"""Activities MCP Service - Activity management and submissions."""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent.parent / "common"))

from common.models.base import HealthResponse

app = FastAPI(
    title="Activities MCP",
    version="1.0.0",
    description="MCP service for activity management and submissions"
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import routers
from .tools import (
    list_activities,
    get_activity,
    create_activity,
    submit_assignment,
    grade_submission
)

# Include tool routers
app.include_router(list_activities.router, prefix="/tools", tags=["tools"])
app.include_router(get_activity.router, prefix="/tools", tags=["tools"])
app.include_router(create_activity.router, prefix="/tools", tags=["tools"])
app.include_router(submit_assignment.router, prefix="/tools", tags=["tools"])
app.include_router(grade_submission.router, prefix="/tools", tags=["tools"])

@app.get("/", response_model=HealthResponse)
async def root():
    return HealthResponse(
        status="healthy",
        service="Activities MCP",
        version="1.0.0"
    )

@app.get("/health", response_model=HealthResponse)
async def health_check():
    return HealthResponse(
        status="healthy",
        service="Activities MCP",
        version="1.0.0"
    )

@app.get("/manifest")
async def get_manifest():
    """Return MCP manifest for ChatGPT integration."""
    return {
        "name": "activities-mcp",
        "version": "1.0.0",
        "description": "Activity management and submission handling",
        "tools": [
            {
                "name": "list_activities",
                "description": "List available activities with optional filters",
                "parameters": {
                    "activity_type": {"type": "string", "required": False},
                    "status": {"type": "string", "required": False},
                    "limit": {"type": "integer", "required": False}
                }
            },
            {
                "name": "get_activity",
                "description": "Get detailed information about a specific activity",
                "parameters": {
                    "activity_id": {"type": "string", "required": True}
                }
            },
            {
                "name": "create_activity",
                "description": "Create a new activity",
                "parameters": {
                    "title": {"type": "string", "required": True},
                    "description": {"type": "string", "required": True},
                    "activity_type": {"type": "string", "required": True}
                }
            },
            {
                "name": "submit_assignment",
                "description": "Submit an assignment for an activity",
                "parameters": {
                    "activity_id": {"type": "string", "required": True},
                    "user_id": {"type": "string", "required": True},
                    "content": {"type": "string", "required": True}
                }
            }
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
```

#### Step 3.2: Copy Code from my_onboarding_api

```bash
# From my_onboarding_api to mcp-activities
cp my_onboarding_api/app/api/endpoints/activities.py mcp-activities/app/tools/
cp my_onboarding_api/app/api/endpoints/submissions.py mcp-activities/app/tools/
cp my_onboarding_api/app/services/activity_service.py mcp-activities/app/services/
cp my_onboarding_api/app/services/submission_service.py mcp-activities/app/services/
cp my_onboarding_api/app/models/activity.py mcp-activities/app/models/
cp my_onboarding_api/app/models/submission.py mcp-activities/app/models/
```

---

### PHASE 4: Create New Services (Week 4)

#### Step 4.1: Create MCP Rewards Service

**File: `mcp-rewards/app/main.py`**

```python
"""Rewards MCP Service - Rewards tracking and redemption."""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent.parent / "common"))

from common.models.base import HealthResponse

app = FastAPI(
    title="Rewards MCP",
    version="1.0.0",
    description="MCP service for rewards tracking and redemption"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

from .tools import get_rewards, calculate_points, redeem_reward

app.include_router(get_rewards.router, prefix="/tools", tags=["tools"])
app.include_router(calculate_points.router, prefix="/tools", tags=["tools"])
app.include_router(redeem_reward.router, prefix="/tools", tags=["tools"])

@app.get("/", response_model=HealthResponse)
async def root():
    return HealthResponse(status="healthy", service="Rewards MCP", version="1.0.0")

@app.get("/health", response_model=HealthResponse)
async def health_check():
    return HealthResponse(status="healthy", service="Rewards MCP", version="1.0.0")

@app.get("/manifest")
async def get_manifest():
    return {
        "name": "rewards-mcp",
        "version": "1.0.0",
        "description": "Rewards tracking and redemption",
        "tools": [
            {
                "name": "get_rewards",
                "description": "Get user's reward points and history",
                "parameters": {"user_id": {"type": "string", "required": True}}
            },
            {
                "name": "calculate_points",
                "description": "Calculate points for an activity",
                "parameters": {
                    "activity_id": {"type": "string", "required": True},
                    "user_id": {"type": "string", "required": True}
                }
            },
            {
                "name": "redeem_reward",
                "description": "Redeem points for a reward",
                "parameters": {
                    "user_id": {"type": "string", "required": True},
                    "reward_id": {"type": "string", "required": True}
                }
            }
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8003)
```

#### Step 4.2: Create MCP Approval Service

**File: `mcp-approval/app/main.py`**
```python
"""Approval MCP Service - Activity approval workflows."""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent.parent / "common"))

from common.models.base import HealthResponse

app = FastAPI(
    title="Approval MCP",
    version="1.0.0",
    description="MCP service for activity approval workflows"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

from .tools import approve_activity, reject_activity, get_pending

app.include_router(approve_activity.router, prefix="/tools", tags=["tools"])
app.include_router(reject_activity.router, prefix="/tools", tags=["tools"])
app.include_router(get_pending.router, prefix="/tools", tags=["tools"])

@app.get("/", response_model=HealthResponse)
async def root():
    return HealthResponse(status="healthy", service="Approval MCP", version="1.0.0")

@app.get("/health", response_model=HealthResponse)
async def health_check():
    return HealthResponse(status="healthy", service="Approval MCP", version="1.0.0")

@app.get("/manifest")
async def get_manifest():
    return {
        "name": "approval-mcp",
        "version": "1.0.0",
        "description": "Activity approval workflows",
        "tools": [
            {
                "name": "approve_activity",
                "description": "Approve a pending activity",
                "parameters": {
                    "activity_id": {"type": "string", "required": True},
                    "approver_id": {"type": "string", "required": True}
                }
            },
            {
                "name": "reject_activity",
                "description": "Reject a pending activity",
                "parameters": {
                    "activity_id": {"type": "string", "required": True},
                    "approver_id": {"type": "string", "required": True},
                    "reason": {"type": "string", "required": True}
                }
            },
            {
                "name": "get_pending",
                "description": "Get list of pending approvals",
                "parameters": {
                    "approver_id": {"type": "string", "required": True}
                }
            }
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8004)
```

---

### PHASE 5: Refactor Orchestrator (Week 5)

#### Step 5.1: Update Orchestrator with Real Clients

**File: `orchestrator/app/main.py`**
```python
"""Orchestrator Service - Intent detection and request routing."""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent / "common"))

from common.models.base import HealthResponse
from .routers import chat_router

app = FastAPI(
    title="MyVillage AI Orchestrator",
    version="1.0.0",
    description="Orchestrator for routing requests to appropriate MCP services"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(chat_router.router, tags=["chat"])

@app.get("/", response_model=HealthResponse)
async def root():
    return HealthResponse(
        status="healthy",
        service="MyVillage AI Orchestrator",
        version="1.0.0"
    )

@app.get("/health", response_model=HealthResponse)
async def health_check():
    # Check if all MCP services are reachable
    from .services.health_checker import check_all_services
    
    services_status = await check_all_services()
    all_healthy = all(status["healthy"] for status in services_status.values())
    
    return {
        "status": "healthy" if all_healthy else "degraded",
        "service": "MyVillage AI Orchestrator",
        "version": "1.0.0",
        "mcp_services": services_status
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8100)
```

**File: `orchestrator/app/services/onboarding_client.py`**
```python
"""Real HTTP client for Onboarding MCP."""
import httpx
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)

class OnboardingClient:
    """Client for calling Onboarding MCP service."""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.timeout = 30.0
    
    async def create_user(self, name: str, email: str, password: str) -> Dict[str, Any]:
        """Call create_user tool on Onboarding MCP."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                response = await client.post(
                    f"{self.base_url}/tools/create_user",
                    json={"name": name, "email": email, "password": password}
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"Error calling onboarding MCP: {e}")
                raise
    
    async def login_user(self, email: str, password: str) -> Dict[str, Any]:
        """Call login_user tool on Onboarding MCP."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                response = await client.post(
                    f"{self.base_url}/tools/login_user",
                    json={"email": email, "password": password}
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"Error calling onboarding MCP: {e}")
                raise
    
    async def verify_otp(self, email: str, otp: str) -> Dict[str, Any]:
        """Call verify_otp tool on Onboarding MCP."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                response = await client.post(
                    f"{self.base_url}/tools/verify_otp",
                    json={"email": email, "otp": otp}
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"Error calling onboarding MCP: {e}")
                raise
```

**File: `orchestrator/app/services/intent_detector.py`**
```python
"""Intent detection service."""
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)

class IntentDetector:
    """Detect user intent from message."""
    
    # Intent keywords mapping
    INTENT_KEYWORDS = {
        "signup": ["sign up", "signup", "register", "create account", "new account", "join"],
        "login": ["log in", "login", "sign in", "signin", "authenticate"],
        "activity_list": ["activities", "what can i do", "show activities", "list activities"],
        "activity_create": ["create activity", "new activity", "add activity"],
        "activity_submit": ["submit", "submit assignment", "turn in"],
        "rewards_get": ["rewards", "points", "my rewards", "check rewards"],
        "approval_pending": ["pending", "approvals", "need approval", "waiting approval"]
    }
    
    def detect(self, message: str) -> str:
        """
        Detect intent from user message.
        
        Returns:
            Intent label (e.g., "signup", "login", "activity_list")
        """
        message_lower = message.lower()
        
        # Check each intent's keywords
        for intent, keywords in self.INTENT_KEYWORDS.items():
            for keyword in keywords:
                if keyword in message_lower:
                    logger.info(f"Detected intent: {intent} (keyword: {keyword})")
                    return intent
        
        # Default to general query
        logger.info("No specific intent detected, defaulting to 'general'")
        return "general"
    
    def get_mcp_service(self, intent: str) -> str:
        """
        Map intent to MCP service.
        
        Returns:
            MCP service name (e.g., "onboarding", "activities")
        """
        intent_to_service = {
            "signup": "onboarding",
            "login": "onboarding",
            "activity_list": "activities",
            "activity_create": "activities",
            "activity_submit": "activities",
            "rewards_get": "rewards",
            "approval_pending": "approval",
            "general": "activities"  # Default to activities for general queries
        }
        
        return intent_to_service.get(intent, "activities")
```

**File: `orchestrator/app/routers/chat_router.py`**
```python
"""Chat router for orchestrator."""
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import logging
import os

from ..services.intent_detector import IntentDetector
from ..services.onboarding_client import OnboardingClient
from ..services.activities_client import ActivitiesClient
from ..services.rewards_client import RewardsClient
from ..services.approval_client import ApprovalClient

logger = logging.getLogger(__name__)
router = APIRouter()

# Initialize services
intent_detector = IntentDetector()
onboarding_client = OnboardingClient(os.getenv("ONBOARDING_MCP_URL", "http://localhost:8001"))
activities_client = ActivitiesClient(os.getenv("ACTIVITIES_MCP_URL", "http://localhost:8002"))
rewards_client = RewardsClient(os.getenv("REWARDS_MCP_URL", "http://localhost:8003"))
approval_client = ApprovalClient(os.getenv("APPROVAL_MCP_URL", "http://localhost:8004"))

class ChatRequest(BaseModel):
    user_id: str
    text: str
    session_id: str | None = None

class ChatResponse(BaseModel):
    success: bool
    message: str
    intent: str
    routed_to: str
    data: dict | None = None

@router.post("/chat", response_model=ChatResponse)
async def process_chat(request: ChatRequest):
    """
    Process chat message with intent detection and routing.
    
    Flow:
    1. Detect intent from user message
    2. Route to appropriate MCP service
    3. Return unified response
    """
    logger.info(f"Processing chat for user: {request.user_id}")
    logger.info(f"Message: {request.text}")
    
    try:
        # Step 1: Detect intent
        intent = intent_detector.detect(request.text)
        mcp_service = intent_detector.get_mcp_service(intent)
        
        logger.info(f"Detected intent: {intent}, routing to: {mcp_service}")
        
        # Step 2: Route to appropriate MCP
        if mcp_service == "onboarding":
            # Handle onboarding intents
            if intent == "signup":
                # For signup, we need to collect more info
                # This is a simplified example
                return ChatResponse(
                    success=True,
                    message="Let's get you signed up! Please provide your name, email, and password.",
                    intent=intent,
                    routed_to=mcp_service,
                    data={"flow": "signup", "step": "collect_info"}
                )
            elif intent == "login":
                return ChatResponse(
                    success=True,
                    message="Please provide your email and password to log in.",
                    intent=intent,
                    routed_to=mcp_service,
                    data={"flow": "login", "step": "collect_credentials"}
                )
        
        elif mcp_service == "activities":
            # Handle activity intents
            if intent == "activity_list":
                activities = await activities_client.list_activities()
                return ChatResponse(
                    success=True,
                    message=f"Found {len(activities.get('data', []))} activities",
                    intent=intent,
                    routed_to=mcp_service,
                    data=activities
                )
        
        elif mcp_service == "rewards":
            # Handle rewards intents
            if intent == "rewards_get":
                rewards = await rewards_client.get_rewards(request.user_id)
                return ChatResponse(
                    success=True,
                    message=f"You have {rewards.get('data', {}).get('points', 0)} points",
                    intent=intent,
                    routed_to=mcp_service,
                    data=rewards
                )
        
        elif mcp_service == "approval":
            # Handle approval intents
            if intent == "approval_pending":
                pending = await approval_client.get_pending(request.user_id)
                return ChatResponse(
                    success=True,
                    message=f"You have {len(pending.get('data', []))} pending approvals",
                    intent=intent,
                    routed_to=mcp_service,
                    data=pending
                )
        
        # Default response for general queries
        return ChatResponse(
            success=True,
            message="I'm here to help! You can ask about activities, rewards, or your account.",
            intent=intent,
            routed_to=mcp_service,
            data={}
        )
    
    except Exception as e:
        logger.error(f"Error processing chat: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process chat: {str(e)}"
        )
```

---

### PHASE 6: Testing & Deployment (Week 6)

#### Step 6.1: Create Integration Tests

**File: `tests/integration/test_end_to_end.py`**
```python
"""End-to-end integration tests."""
import pytest
import httpx

ORCHESTRATOR_URL = "http://localhost:8100"
ONBOARDING_URL = "http://localhost:8001"
ACTIVITIES_URL = "http://localhost:8002"

@pytest.mark.asyncio
async def test_signup_flow():
    """Test complete signup flow through orchestrator."""
    async with httpx.AsyncClient() as client:
        # Step 1: Send signup intent
        response = await client.post(
            f"{ORCHESTRATOR_URL}/chat",
            json={
                "user_id": "test-user-123",
                "text": "I want to sign up"
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert data["intent"] == "signup"
        assert data["routed_to"] == "onboarding"

@pytest.mark.asyncio
async def test_activity_list_flow():
    """Test activity listing through orchestrator."""
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{ORCHESTRATOR_URL}/chat",
            json={
                "user_id": "test-user-123",
                "text": "Show me activities"
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert data["intent"] == "activity_list"
        assert data["routed_to"] == "activities"

@pytest.mark.asyncio
async def test_mcp_health_checks():
    """Test all MCP services are healthy."""
    services = [
        ("Orchestrator", ORCHESTRATOR_URL),
        ("Onboarding", ONBOARDING_URL),
        ("Activities", ACTIVITIES_URL)
    ]
    
    async with httpx.AsyncClient() as client:
        for name, url in services:
            response = await client.get(f"{url}/health")
            assert response.status_code == 200, f"{name} health check failed"
            data = response.json()
            assert data["status"] == "healthy"
```

#### Step 6.2: Create Deployment Scripts

**File: `scripts/deploy.sh`**
```bash
#!/bin/bash

# Deployment script for all services

set -e

echo "🚀 Starting MyVillage AI deployment..."

# Build all Docker images
echo "📦 Building Docker images..."
docker-compose build

# Start all services
echo "🏃 Starting services..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Health checks
echo "🏥 Running health checks..."
services=("8100" "8001" "8002" "8003" "8004")
for port in "${services[@]}"; do
    if curl -f http://localhost:$port/health > /dev/null 2>&1; then
        echo "✅ Service on port $port is healthy"
    else
        echo "❌ Service on port $port is not responding"
        exit 1
    fi
done

echo "✨ Deployment complete!"
echo "📊 Services running:"
echo "  - Orchestrator: http://localhost:8100"
echo "  - Onboarding MCP: http://localhost:8001"
echo "  - Activities MCP: http://localhost:8002"
echo "  - Rewards MCP: http://localhost:8003"
echo "  - Approval MCP: http://localhost:8004"
```

---

## 🎯 Service Breakdown

### Service Responsibilities

| Service | Port | Responsibilities | Dependencies |
|---------|------|------------------|--------------|
| **Orchestrator** | 8100 | Intent detection, Request routing, Response aggregation | All MCPs |
| **Onboarding MCP** | 8001 | User signup, Login, OTP verification, Profile management | Database |
| **Activities MCP** | 8002 | Activity CRUD, Submissions, Grading, Likes | Database |
| **Rewards MCP** | 8003 | Points calculation, Rewards tracking, Redemption | Database |
| **Approval MCP** | 8004 | Activity approvals, Rejection workflows, Pending list | Database |

### API Endpoints Summary

**Orchestrator:**
- `POST /chat` - Main chat endpoint
- `GET /health` - Health check with MCP status

**Each MCP:**
- `GET /` - Root endpoint
- `GET /health` - Health check
- `GET /manifest` - MCP manifest for ChatGPT
- `POST /tools/{tool_name}` - Execute specific tool

---

## 📦 Deployment Guide

### Local Development

```bash
# 1. Clone monorepo
git clone <monorepo-url>
cd myvillage-ai-monorepo

# 2. Copy environment variables
cp .env.example .env
# Edit .env with your values

# 3. Start all services
docker-compose up -d

# 4. View logs
docker-compose logs -f

# 5. Stop services
docker-compose down
```

### Production Deployment (Render)

**Option 1: Deploy as separate services**


Each service gets its own Render web service:
- `myvillage-orchestrator` (from `/orchestrator`)
- `myvillage-mcp-onboarding` (from `/mcp-onboarding`)
- `myvillage-mcp-activities` (from `/mcp-activities`)
- `myvillage-mcp-rewards` (from `/mcp-rewards`)
- `myvillage-mcp-approval` (from `/mcp-approval`)

**Option 2: Deploy as Docker containers**

Use Render's Docker deployment with `docker-compose.yml`.

---

## 🧪 Testing Strategy

### Unit Tests
```bash
# Test individual services
cd mcp-onboarding
pytest tests/unit/

cd mcp-activities
pytest tests/unit/
```

### Integration Tests
```bash
# Test service communication
pytest tests/integration/
```

### End-to-End Tests
```bash
# Test complete user flows
pytest tests/e2e/
```

### Load Testing
```bash
# Use locust or k6
locust -f tests/load/locustfile.py
```

---

## 📅 Timeline & Resources

### 6-Week Implementation Plan

| Week | Phase | Tasks | Team Size | Deliverables |
|------|-------|-------|-----------|--------------|
| **1** | Foundation | Setup monorepo, common library, Docker config | 2 devs | Monorepo structure, shared code |
| **2** | Onboarding MCP | Extract auth code, create MCP service, manifest | 2 devs | Working onboarding MCP |
| **3** | Activities MCP | Extract activity code, create MCP service, manifest | 2 devs | Working activities MCP |
| **4** | New Services | Create rewards & approval MCPs | 2 devs | Rewards & approval MCPs |
| **5** | Orchestrator | Refactor with real clients, routing logic | 2 devs | Working orchestrator |
| **6** | Testing & Deploy | Integration tests, deployment, documentation | 2 devs | Production-ready system |

### Resource Requirements

**Development Team:**
- 2 Backend Developers (Python/FastAPI)
- 1 DevOps Engineer (Docker/CI/CD)
- 1 QA Engineer (Testing)

**Infrastructure:**
- Development environment (local Docker)
- Staging environment (Render/AWS)
- Production environment (Render/AWS)
- Database (PostgreSQL or DynamoDB)

---

## 🔄 Migration Checklist

### Pre-Migration
- [ ] Review architecture document
- [ ] Identify all code to migrate
- [ ] Set up monorepo structure
- [ ] Create common library
- [ ] Set up Docker environment

### During Migration
- [ ] Extract onboarding code → mcp-onboarding
- [ ] Extract activities code → mcp-activities
- [ ] Create rewards service
- [ ] Create approval service
- [ ] Refactor orchestrator
- [ ] Create MCP manifests
- [ ] Write integration tests

### Post-Migration
- [ ] Deploy to staging
- [ ] Run integration tests
- [ ] Performance testing
- [ ] Security audit
- [ ] Deploy to production
- [ ] Monitor and optimize
- [ ] Decommission old repos

---

## 🚨 Common Pitfalls & Solutions

### Pitfall 1: Import Path Issues
**Problem:** Services can't import from common library

**Solution:**
```python
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "common"))
```

### Pitfall 2: Database Connection Sharing
**Problem:** Multiple services trying to use same database connection

**Solution:** Each service should have its own database connection pool

### Pitfall 3: Circular Dependencies
**Problem:** Services calling each other in loops

**Solution:** Use orchestrator as single entry point, MCPs should not call each other

### Pitfall 4: Environment Variables
**Problem:** Different env vars for each service

**Solution:** Use docker-compose environment section or .env files per service

### Pitfall 5: Port Conflicts
**Problem:** Services trying to use same ports

**Solution:** Assign unique ports (8100, 8001, 8002, 8003, 8004)

---

## 📊 Success Metrics

### Technical Metrics
- ✅ All services deployable independently
- ✅ < 200ms latency for orchestrator routing
- ✅ < 500ms latency for MCP tool execution
- ✅ 99.9% uptime for each service
- ✅ Zero data loss during migration
- ✅ 100% test coverage for critical paths

### Business Metrics
- ✅ No user-facing disruption during migration
- ✅ Same or better response times
- ✅ Improved developer productivity (faster feature deployment)
- ✅ Reduced deployment time (from hours to minutes)
- ✅ Better scalability (services scale independently)

---

## 🎓 Learning Resources

### FastAPI
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [FastAPI Best Practices](https://github.com/zhanymkanov/fastapi-best-practices)

### Microservices
- [Microservices Patterns](https://microservices.io/patterns/index.html)
- [Building Microservices (Book)](https://www.oreilly.com/library/view/building-microservices-2nd/*************/)

### Docker
- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose](https://docs.docker.com/compose/)

### MCP (Model Context Protocol)
- [MCP Specification](https://modelcontextprotocol.io/)
- [FastMCP Library](https://github.com/jlowin/fastmcp)

---

## 🤝 Team Collaboration

### Git Workflow

**Branch Strategy:**
```
main (production)
  ├── develop (staging)
  │   ├── feature/onboarding-mcp
  │   ├── feature/activities-mcp
  │   ├── feature/orchestrator-refactor
  │   └── feature/rewards-mcp
```

**Commit Convention:**
```
feat: Add create_user tool to onboarding MCP
fix: Fix intent detection for activity queries
docs: Update deployment guide
test: Add integration tests for orchestrator
refactor: Extract common validation logic
```

### Code Review Process
1. Create feature branch
2. Implement changes
3. Write tests
4. Create pull request
5. Code review (2 approvals required)
6. Merge to develop
7. Deploy to staging
8. Test on staging
9. Merge to main
10. Deploy to production

---

## 📞 Support & Troubleshooting

### Common Issues

**Issue: Service won't start**
```bash
# Check logs
docker-compose logs <service-name>

# Check if port is already in use
lsof -i :<port>

# Restart service
docker-compose restart <service-name>
```

**Issue: Services can't communicate**
```bash
# Check network
docker network ls
docker network inspect myvillage-network

# Check service URLs in environment
docker-compose exec orchestrator env | grep MCP_URL
```

**Issue: Database connection fails**
```bash
# Check database is running
docker-compose ps

# Check connection string
echo $DATABASE_URL

# Test connection
docker-compose exec mcp-onboarding python -c "from app.core.database import db; print(db.test_connection())"
```

---

## 🎯 Next Steps

### Immediate Actions (This Week)

1. **Review this document** with your team
2. **Create monorepo** directory structure
3. **Set up common library** with shared code
4. **Create Docker Compose** configuration
5. **Start Phase 1** (Foundation work)

### Short-term Goals (Next Month)

1. **Complete Phases 1-3** (Foundation, Onboarding, Activities)
2. **Deploy to staging** environment
3. **Run integration tests**
4. **Get team feedback**

### Long-term Goals (Next Quarter)

1. **Complete all 6 phases**
2. **Deploy to production**
3. **Monitor and optimize**
4. **Add new features** (using new architecture)
5. **Decommission old repositories**

---

## 📋 Appendix

### A. File Migration Map

**From `my_onboarding_api` to `mcp-onboarding`:**
```
app/services/auth_service.py → mcp-onboarding/app/services/auth_service.py
app/services/session_service.py → mcp-onboarding/app/services/session_service.py
app/models/user.py → mcp-onboarding/app/models/user.py
app/api/auth.py → mcp-onboarding/app/tools/login_user.py
app/core/security.py → common/utils/security.py
app/core/database.py → common/utils/database.py
```

**From `my_onboarding_api` to `mcp-activities`:**
```
app/api/endpoints/activities.py → mcp-activities/app/tools/
app/api/endpoints/submissions.py → mcp-activities/app/tools/
app/services/activity_service.py → mcp-activities/app/services/activity_service.py
app/services/submission_service.py → mcp-activities/app/services/submission_service.py
app/models/activity.py → mcp-activities/app/models/activity.py
app/models/submission.py → mcp-activities/app/models/submission.py
```

**From `mvp-conversation-orchestrator` to `orchestrator`:**
```
app/main.py → orchestrator/app/main.py
app/routers/chat_router.py → orchestrator/app/routers/chat_router.py
app/services/intent_client.py → orchestrator/app/services/intent_detector.py
app/schemas/message_schema.py → orchestrator/app/schemas/messages.py
```

### B. Environment Variables Reference

**Orchestrator (.env):**
```env
# Service URLs
ONBOARDING_MCP_URL=http://mcp-onboarding:8001
ACTIVITIES_MCP_URL=http://mcp-activities:8002
REWARDS_MCP_URL=http://mcp-rewards:8003
APPROVAL_MCP_URL=http://mcp-approval:8004

# Server
HOST=0.0.0.0
PORT=8100
DEBUG=false
LOG_LEVEL=INFO

# CORS
CORS_ORIGINS=["*"]
```

**MCP Services (.env):**
```env
# Database
DATABASE_URL=**********************************/myvillage

# Security
JWT_SECRET=your-secret-key
JWT_ALGORITHM=HS256
JWT_EXPIRATION=3600

# Server
HOST=0.0.0.0
PORT=800X  # Different for each service
DEBUG=false
LOG_LEVEL=INFO

# External APIs (if needed)
GEMINI_API_KEY=your-gemini-key
HF_TOKEN=your-huggingface-token
```

### C. Docker Commands Reference

```bash
# Build all services
docker-compose build

# Start all services
docker-compose up -d

# View logs
docker-compose logs -f [service-name]

# Stop all services
docker-compose down

# Restart a service
docker-compose restart [service-name]

# Execute command in service
docker-compose exec [service-name] [command]

# View running containers
docker-compose ps

# Remove all containers and volumes
docker-compose down -v

# Rebuild and restart
docker-compose up -d --build
```

### D. API Testing Examples

**Test Orchestrator:**
```bash
# Health check
curl http://localhost:8100/health

# Chat endpoint
curl -X POST http://localhost:8100/chat \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "test-123",
    "text": "I want to sign up"
  }'
```

**Test Onboarding MCP:**
```bash
# Health check
curl http://localhost:8001/health

# Get manifest
curl http://localhost:8001/manifest

# Create user
curl -X POST http://localhost:8001/tools/create_user \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "SecurePass123"
  }'
```

**Test Activities MCP:**
```bash
# Health check
curl http://localhost:8002/health

# List activities
curl -X POST http://localhost:8002/tools/list_activities \
  -H "Content-Type: application/json" \
  -d '{
    "limit": 10
  }'
```

---

## 📝 Conclusion

This migration guide provides a comprehensive roadmap for transforming your two separate repositories into a unified monorepo following the microservices architecture pattern. The key benefits include:

✅ **Clean Architecture** - Each service has a single, well-defined responsibility  
✅ **Scalability** - Services can be scaled independently based on demand  
✅ **Maintainability** - Clear boundaries make debugging and updates easier  
✅ **Flexibility** - Support both ChatGPT (direct MCP) and custom chatbots (orchestrator)  
✅ **Code Reusability** - Shared common library eliminates duplication  
✅ **Future-Proof** - Easy to add new services without affecting existing ones

### Success Factors

1. **Follow the phases** - Don't skip steps, each builds on the previous
2. **Test thoroughly** - Integration tests are critical for microservices
3. **Monitor closely** - Watch logs and metrics during migration
4. **Communicate often** - Keep team aligned on progress and issues
5. **Document everything** - Update docs as you go, not after

### Final Checklist

Before considering migration complete:

- [ ] All services running independently
- [ ] All integration tests passing
- [ ] Performance meets requirements
- [ ] Security audit completed
- [ ] Documentation updated
- [ ] Team trained on new architecture
- [ ] Monitoring and alerting configured
- [ ] Rollback plan tested
- [ ] Production deployment successful
- [ ] Old repositories archived

---

**Document Version:** 1.0  
**Last Updated:** 2025-11-18  
**Authors: <AUTHORS>
**Status:** Ready for Implementation

---

## 🙏 Acknowledgments

This architecture follows industry best practices from:
- Martin Fowler's Microservices patterns
- The Twelve-Factor App methodology
- FastAPI best practices
- Docker and container orchestration patterns
- Model Context Protocol specifications

---

**Questions or need help?** Contact the architecture team or create an issue in the monorepo.

**Ready to start?** Begin with Phase 1 and follow the step-by-step guide above!

🚀 **Happy Building!**
