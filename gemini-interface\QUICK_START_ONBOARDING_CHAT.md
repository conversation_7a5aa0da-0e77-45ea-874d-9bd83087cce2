# Quick Start: Onboarding Chat Page

## 🚀 5-Minute Setup

### Step 1: Verify Environment Variables

Check your `.env` file has:

```env
NEXT_PUBLIC_API_URL=http://localhost:8000
```

### Step 2: Start Required Services

**Terminal 1 - Start Orchestrator:**
```bash
cd mvp-conversation-orchestrator
python -m app.main
```

Verify: `curl http://localhost:8100/health`

**Terminal 2 - Start Backend:**
```bash
cd my_onboarding_api
python main.py
```

Verify: `curl http://localhost:8000/api/v1/health`

**Terminal 3 - Start Frontend:**
```bash
cd gemini-interface
npm run dev
```

### Step 3: Open the Chat Page

Navigate to: **http://localhost:3000/onboarding-chat**

### Step 4: Test It Out!

Try these messages:

1. **"I want to sign up"** → Should start signup flow
2. **"I need to log in"** → Should start login flow
3. **"Show me activities"** → Should list activities
4. **"What can you help me with?"** → Should show help

## 🎯 Quick Test Commands

### Test All Services

```bash
# Test Orchestrator
curl http://localhost:8100/health

# Test Backend
curl http://localhost:8000/api/v1/health

# Test Integration
curl -X POST http://localhost:8000/api/v1/chat-orchestrated \
  -H "Content-Type: application/json" \
  -H "session_id: test-123" \
  -d '{"message": "I want to sign up"}'
```

## 📱 Using the Chat Interface

### Quick Actions (Click to Auto-fill)

- **Sign up** → "I want to sign up"
- **Log in** → "I need to log in"
- **View activities** → "Show me activities"
- **Help** → "What can you help me with?"

### Keyboard Shortcuts

- **Enter** → Send message
- **Shift + Enter** → New line (not implemented yet)

## 🎨 Visual Indicators

### Message Colors

- **Blue** → Your messages
- **White/Gray** → Assistant responses
- **Green** → Flow messages (signup/login)
- **Red** → Error messages

### Flow Indicators

When in a signup/login flow, you'll see:
```
Flow: signup • Step: name
```

## 🐛 Troubleshooting

### "Orchestrator unavailable"

**Problem:** Orchestrator service not running

**Fix:**
```bash
cd mvp-conversation-orchestrator
python -m app.main
```

### "Failed to send message"

**Problem:** Backend not running or can't reach orchestrator

**Fix:**
1. Start backend: `cd my_onboarding_api && python main.py`
2. Check orchestrator is running
3. Verify `NEXT_PUBLIC_API_URL` in `.env`

### CORS Errors

**Problem:** Browser blocking requests

**Fix:**
1. Check backend CORS settings in `my_onboarding_api/app/core/config.py`
2. Ensure `http://localhost:3000` is in `cors_origins`

### Page Not Found

**Problem:** Wrong URL

**Fix:** Make sure you're going to `/onboarding-chat` (not `/chat` or `/onboarding`)

## 📊 What You'll See

### Successful Signup Flow

```
You: I want to sign up
Assistant: Great! Let's get you signed up. What's your name?
[Flow: signup • Step: name]

You: John Doe
Assistant: Nice to meet you, John! What's your email address?
[Flow: signup • Step: email]
```

### Activity Query

```
You: Show me activities
Assistant: Here are some activities available in your area:
- Community Yoga Class (sports) - 2025-11-15
- Art Workshop (arts) - 2025-11-16
- Coding Bootcamp (education) - 2025-11-17
```

## 🔧 Configuration

### Change Backend URL

Edit `.env`:
```env
NEXT_PUBLIC_API_URL=http://your-backend-url:8000
```

### Change Port

Edit `package.json`:
```json
{
  "scripts": {
    "dev": "next dev -p 3001"
  }
}
```

## 📚 More Information

- **Full Documentation:** `ONBOARDING_CHAT_PAGE.md`
- **Backend Integration:** `../my_onboarding_api/ORCHESTRATOR_INTEGRATION.md`
- **Orchestrator Docs:** `../mvp-conversation-orchestrator/README.md`

---

**Ready to chat!** 💬
