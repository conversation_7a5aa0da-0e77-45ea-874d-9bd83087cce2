"""
Example usage and test cases for the NLU Service.

This demonstrates the advanced capabilities of the NLU service including:
- Entity extraction (dates, locations, activity types, status)
- Sentiment analysis
- Context-aware disambiguation
- Multi-intent handling
"""

import asyncio
from app.services.nlu_service import nlu_service
from app.services.intent_detector import IntentDetector


async def test_nlu_examples():
    """Test various NLU scenarios."""
    
    print("=" * 80)
    print("NLU SERVICE - EXAMPLE USAGE AND TEST CASES")
    print("=" * 80)
    
    # Initialize intent detector
    detector = IntentDetector()
    
    # Test cases with expected results
    test_cases = [
        {
            "text": "Show me quizzes in Mumbai due this week",
            "description": "Complex query with activity type, city, and date range",
            "expected_entities": ["activity_type", "city", "date_range"]
        },
        {
            "text": "I want to submit my homework for the math assignment",
            "description": "Submit action with activity type and subject",
            "expected_entities": ["activity_type", "subject", "action"]
        },
        {
            "text": "Show all submitted assignments in Bangalore",
            "description": "Filter by status and city",
            "expected_entities": ["activity_type", "status", "city"]
        },
        {
            "text": "List pending approvals for today",
            "description": "Approval intent with date",
            "expected_entities": ["status", "date"]
        },
        {
            "text": "I love this! Show me my rewards and points",
            "description": "Positive sentiment with rewards query",
            "expected_sentiment": "positive"
        },
        {
            "text": "This is confusing. How do I create a new quiz?",
            "description": "Negative sentiment with create intent",
            "expected_sentiment": "negative"
        },
        {
            "text": "Show submissions for activity_id: ACT-12345",
            "description": "Query with specific activity ID",
            "expected_entities": ["activity_id"]
        },
        {
            "text": "What are the events happening tomorrow in Delhi?",
            "description": "Events with date and city",
            "expected_entities": ["activity_type", "date", "city"]
        },
        {
            "text": "I need to redeem 500 points",
            "description": "Redeem action with points",
            "expected_entities": ["points", "action"]
        },
        {
            "text": "Show me all graded projects this month",
            "description": "Activity type with status and date range",
            "expected_entities": ["activity_type", "status", "date_range"]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'=' * 80}")
        print(f"TEST CASE {i}: {test_case['description']}")
        print(f"{'=' * 80}")
        print(f"Input: \"{test_case['text']}\"")
        print()
        
        # Perform NLU analysis
        result = await detector.detect_advanced(test_case['text'])
        
        # Display results
        print(f"🎯 Primary Intent: {result['intent']}")
        print(f"📊 Confidence: {result['confidence']:.2%}")
        print(f"😊 Sentiment: {result['sentiment']}")
        
        if result['action']:
            print(f"⚡ Action: {result['action']}")
        
        if result['entities']:
            print(f"\n📦 Extracted Entities:")
            for entity_type, entity_value in result['entities'].items():
                if isinstance(entity_value, dict):
                    print(f"   • {entity_type}:")
                    for key, value in entity_value.items():
                        print(f"      - {key}: {value}")
                else:
                    print(f"   • {entity_type}: {entity_value}")
        
        if result['secondary_intents']:
            print(f"\n🔄 Secondary Intents: {', '.join(result['secondary_intents'])}")
        
        # Extract filters from entities
        filters = detector.extract_filters_from_entities(result['entities'])
        if filters:
            print(f"\n🔍 Extracted Filters:")
            for key, value in filters.items():
                print(f"   • {key}: {value}")
        
        # Get MCP service
        mcp_service = detector.get_mcp_service(result['intent'])
        print(f"\n🔌 MCP Service: {mcp_service}")
        
        print()
    
    print("=" * 80)
    print("CONTEXT-AWARE DETECTION")
    print("=" * 80)
    
    # Test context-aware detection
    context = {
        "user_id": "user123",
        "user_role": "student",
        "last_intent": "activity_list"
    }
    
    print(f"\nContext: {context}")
    print(f"Input: \"in Mumbai\"")
    
    result = await detector.detect_advanced("in Mumbai", context=context)
    
    print(f"\n🎯 Detected Intent: {result['intent']}")
    print(f"📊 Confidence: {result['confidence']:.2%}")
    print(f"✅ Context Applied: {result['full_analysis']['context_aware']}")
    
    print("\n" + "=" * 80)
    print("BACKWARD COMPATIBILITY TEST")
    print("=" * 80)
    
    # Test backward compatible method
    simple_intent = detector.detect("show me all activities")
    print(f"\nSimple detect() method: {simple_intent}")
    
    print("\n" + "=" * 80)
    print("ALL TESTS COMPLETED!")
    print("=" * 80)


async def test_real_world_scenarios():
    """Test real-world conversation scenarios."""
    
    print("\n\n" + "=" * 80)
    print("REAL-WORLD CONVERSATION SCENARIOS")
    print("=" * 80)
    
    detector = IntentDetector()
    
    scenarios = [
        {
            "name": "Student Checking Assignments",
            "messages": [
                "Hi, what assignments do I have?",
                "Show me the ones in Mumbai",
                "What about quizzes due this week?"
            ]
        },
        {
            "name": "Teacher Creating Activity",
            "messages": [
                "I want to create a new quiz",
                "It's for the math class",
                "Make it due tomorrow"
            ]
        },
        {
            "name": "Admin Checking Submissions",
            "messages": [
                "Show me all submissions",
                "Filter by submitted status",
                "Only for activities in Bangalore"
            ]
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{'=' * 80}")
        print(f"SCENARIO: {scenario['name']}")
        print(f"{'=' * 80}")
        
        context = {}
        
        for i, message in enumerate(scenario['messages'], 1):
            print(f"\n[Message {i}] User: {message}")
            
            result = await detector.detect_advanced(message, context=context)
            
            print(f"   → Intent: {result['intent']} (confidence: {result['confidence']:.2%})")
            
            if result['entities']:
                entities_str = ", ".join([f"{k}={v}" for k, v in result['entities'].items()])
                print(f"   → Entities: {entities_str}")
            
            # Update context for next message
            context['last_intent'] = result['intent']
            context['last_entities'] = result['entities']


if __name__ == "__main__":
    print("\n🚀 Starting NLU Service Examples...\n")
    
    # Run basic examples
    asyncio.run(test_nlu_examples())
    
    # Run real-world scenarios
    asyncio.run(test_real_world_scenarios())
    
    print("\n✅ All examples completed successfully!\n")
