"""Activity service."""
from datetime import datetime
from typing import Optional, List
import logging
import uuid

from ..models.activity import Activity, ActivityCreate, ActivityUpdate, ActivityStatus
from ..core.database import get_db
from ..core.config import settings

logger = logging.getLogger(__name__)


class ActivityService:
    """Activity management service."""
    
    def __init__(self):
        """Initialize activity service."""
        self.db = get_db()
        self.activities_table = self.db.get_table(settings.activities_table)
    
    async def create_activity(self, activity: ActivityCreate) -> Activity:
        """
        Create a new activity.
        
        Args:
            activity: Activity creation data
            
        Returns:
            Created activity
        """
        activity_id = str(uuid.uuid4())
        now = datetime.utcnow().isoformat()
        
        activity_data = {
            "id": activity_id,
            "title": activity.title,
            "description": activity.description,
            "activity_type": activity.activity_type.value,
            "status": ActivityStatus.DRAFT.value,
            "created_by": activity.created_by,
            "created_at": now,
            "updated_at": now,
            "due_date": activity.due_date.isoformat() if activity.due_date else None,
            "points": activity.points or 0,
            "tags": activity.tags or [],
            "submission_count": 0
        }
        
        try:
            self.activities_table.put_item(Item=activity_data)
            logger.info(f"Activity created successfully: {activity_id}")
            
            return Activity(**activity_data)
        except Exception as e:
            logger.error(f"Error creating activity: {e}")
            raise ValueError(f"Failed to create activity: {str(e)}")
    
    async def get_activity(self, activity_id: str) -> Optional[Activity]:
        """
        Get activity by ID.
        
        Args:
            activity_id: Activity ID
            
        Returns:
            Activity if found, None otherwise
        """
        try:
            response = self.activities_table.get_item(Key={"id": activity_id})
            if "Item" in response:
                return Activity(**response["Item"])
            return None
        except Exception as e:
            logger.error(f"Error getting activity: {e}")
            return None
    
    async def list_activities(
        self,
        activity_type: Optional[str] = None,
        status: Optional[str] = None,
        city_id: Optional[str] = None,
        limit: int = 10
    ) -> List[Activity]:
        """
        List activities with optional filters.
        
        Args:
            activity_type: Filter by activity type
            status: Filter by status
            city_id: Filter by city ID
            limit: Maximum number of results
            
        Returns:
            List of activities
        """
        try:
            # In production, use proper DynamoDB queries with indexes
            # For now, scan with filters
            scan_kwargs = {"Limit": limit}
            
            filter_expressions = []
            expression_values = {}
            
            if activity_type:
                filter_expressions.append("activity_type = :type")
                expression_values[":type"] = activity_type
            
            if status:
                filter_expressions.append("#status = :status")
                expression_values[":status"] = status
                scan_kwargs["ExpressionAttributeNames"] = {"#status": "status"}
            
            if city_id:
                filter_expressions.append("cityId = :city")
                expression_values[":city"] = city_id
            
            if filter_expressions:
                scan_kwargs["FilterExpression"] = " AND ".join(filter_expressions)
                scan_kwargs["ExpressionAttributeValues"] = expression_values
            
            logger.info(f"Scanning table: {self.activities_table.name}")
            response = self.activities_table.scan(**scan_kwargs)
            logger.info(f"DynamoDB Scan Response: {response}")
            
            activities = []
            for item in response.get("Items", []):
                try:
                    # Map DynamoDB item to Activity model
                    activity_data = {
                        "id": item.get("id"),
                        "title": item.get("moduleName", "Untitled Activity"),
                        "description": item.get("description", "") or item.get("moduleType", ""),
                        "activity_type": "assignment", # Default to assignment as mapping is complex
                        "status": "published", # Default to published
                        "created_by": item.get("createdUserId", "system"),
                        "created_at": item.get("createdAt", datetime.utcnow().isoformat()),
                        "updated_at": item.get("updatedAt", datetime.utcnow().isoformat()),
                        "points": int(item.get("activityTokens", 0) if item.get("activityTokens") else 0),
                        "submission_count": 0
                    }
                    
                    # Map activity type if possible
                    db_type = item.get("activityType", "").upper()
                    if db_type == "MEMBERSHIP":
                        activity_data["activity_type"] = "event"
                    
                    activities.append(Activity(**activity_data))
                except Exception as e:
                    logger.warning(f"Skipping invalid activity item {item.get('id')}: {e}")
                    continue
            
            # Sort by updated_at in descending order (most recent first)
            activities.sort(key=lambda x: x.updated_at, reverse=True)
            
            return activities[:limit]
        except Exception as e:
            logger.error(f"Error listing activities: {e}")
            return []
    
    async def update_activity(
        self,
        activity_id: str,
        activity_update: ActivityUpdate
    ) -> Optional[Activity]:
        """
        Update activity.
        
        Args:
            activity_id: Activity ID
            activity_update: Update data
            
        Returns:
            Updated activity if successful, None otherwise
        """
        try:
            # Get current activity
            activity = await self.get_activity(activity_id)
            if not activity:
                return None
            
            # Build update expression
            update_data = activity_update.dict(exclude_unset=True)
            if not update_data:
                return activity
            
            update_data["updated_at"] = datetime.utcnow().isoformat()
            
            # Convert enums to strings
            if "status" in update_data and update_data["status"]:
                update_data["status"] = update_data["status"].value
            
            update_expression = "SET " + ", ".join(
                f"#{k} = :{k}" for k in update_data.keys()
            )
            expression_attribute_names = {
                f"#{k}": k for k in update_data.keys()
            }
            expression_attribute_values = {
                f":{k}": v for k, v in update_data.items()
            }
            
            response = self.activities_table.update_item(
                Key={"id": activity_id},
                UpdateExpression=update_expression,
                ExpressionAttributeNames=expression_attribute_names,
                ExpressionAttributeValues=expression_attribute_values,
                ReturnValues="ALL_NEW"
            )
            
            return Activity(**response["Attributes"])
        except Exception as e:
            logger.error(f"Error updating activity: {e}")
            return None
    
    async def delete_activity(self, activity_id: str) -> bool:
        """
        Delete activity.
        
        Args:
            activity_id: Activity ID
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.activities_table.delete_item(Key={"id": activity_id})
            logger.info(f"Activity deleted: {activity_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting activity: {e}")
            return False
