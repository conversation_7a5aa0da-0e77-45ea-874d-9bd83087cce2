"""
API response schemas.

This module contains Pydantic models for standardizing API responses.
"""

from typing import Any, Dict, Generic, List, Optional, TypeVar, Type
from pydantic import BaseModel, Field
from pydantic.generics import GenericModel

# Generic type variable for paginated data
T = TypeVar('T')

class ErrorDetail(BaseModel):
    """Standard error detail format."""
    message: str = Field(..., description="Human-readable error message")
    code: str = Field(..., description="Error code for programmatic handling")
    details: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional error details"
    )

class PaginationMeta(BaseModel):
    """Pagination metadata for list responses."""
    total: int = Field(..., description="Total number of items")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Number of items per page")
    total_pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there is a next page")
    has_previous: bool = Field(..., description="Whether there is a previous page")

class ApiResponse(BaseModel):
    """
    Standard API response format.
    
    This is the base response format for all API endpoints.
    """
    success: bool = Field(..., description="Indicates if the request was successful")
    data: Optional[Any] = Field(None, description="Response data")
    error: Optional[ErrorDetail] = Field(None, description="Error details if success is False")
    meta: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional metadata about the response"
    )

    @classmethod
    def success(
        cls,
        data: Any = None,
        meta: Optional[Dict[str, Any]] = None
    ) -> 'ApiResponse':
        """Create a successful API response.
        
        Args:
            data: The response data
            meta: Additional metadata about the response
            
        Returns:
            ApiResponse: A successful API response
        """
        return cls(
            success=True,
            data=data,
            meta=meta or {}
        )

    @classmethod
    def error(
        cls,
        message: str,
        code: str,
        details: Optional[Dict[str, Any]] = None,
        meta: Optional[Dict[str, Any]] = None
    ) -> 'ApiResponse':
        """Create an error API response.
        
        Args:
            message: Human-readable error message
            code: Error code for programmatic handling
            details: Additional error details
            meta: Additional metadata about the response
            
        Returns:
            ApiResponse: An error API response
        """
        return cls(
            success=False,
            error=ErrorDetail(
                message=message,
                code=code,
                details=details or {}
            ),
            meta=meta or {}
        )

class PaginatedResponse(GenericModel, Generic[T]):
    """Generic paginated response model."""
    items: List[T] = Field(..., description="List of items in the current page")
    meta: PaginationMeta = Field(..., description="Pagination metadata")

    class Config:
        json_schema_extra = {
            "example": {
                "items": [{"id": 1, "name": "Item 1"}, {"id": 2, "name": "Item 2"}],
                "meta": {
                    "total": 2,
                    "page": 1,
                    "size": 10,
                    "total_pages": 1,
                    "has_next": False,
                    "has_previous": False
                }
            }
        }

# Common error responses
class NotFoundResponse(ApiResponse):
    """404 Not Found response."""
    class Config:
        json_schema_extra = {
            "example": {
                "success": False,
                "error": {
                    "message": "The requested resource was not found",
                    "code": "not_found",
                    "details": {}
                }
            }
        }

class ValidationErrorResponse(ApiResponse):
    """422 Validation Error response."""
    class Config:
        json_schema_extra = {
            "example": {
                "success": False,
                "error": {
                    "message": "Validation error",
                    "code": "validation_error",
                    "details": {
                        "field_name": ["This field is required"]
                    }
                }
            }
        }

class InternalServerErrorResponse(ApiResponse):
    """500 Internal Server Error response."""
    class Config:
        json_schema_extra = {
            "example": {
                "success": False,
                "error": {
                    "message": "An unexpected error occurred",
                    "code": "internal_server_error",
                    "details": {}
                }
            }
        }
