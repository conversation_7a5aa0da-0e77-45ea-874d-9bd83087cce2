"""
Session models for managing user flows.

This module defines models for session management and user flows.
"""

from typing import Optional, Dict, Any, Literal, List
from pydantic import BaseModel, Field
from enum import Enum


class FlowType(str, Enum):
    """Types of user flows."""
    SIGNUP = "signup"
    LOGIN = "login"


class FlowStep(str, Enum):
    """Steps in user flows."""
    NAME = "name"
    FIRST_NAME = "firstName"
    LAST_NAME = "lastName"
    EMAIL = "email"
    PASSWORD = "password"
    PHONE_NUMBER = "phoneNumber"
    CITY_ID = "cityId"
    CITY_NAME = "cityName"
    CITY = "city"
    STATE = "state"
    ZIP_CODE = "zipCode"
    STREET_ADDRESS_ONE = "streetAddressOne"
    STREET_ADDRESS_TWO = "streetAddressTwo"
    ROLE = "role"
    ASSIGNED_ROLE = "assignedRole"
    USER_TYPE = "userType"
    TYPE = "type"
    REGISTERED_FROM = "registeredFrom"
    USER_ADDED_FROM = "userAddedFrom"
    CREATED_BY = "createdBy"
    IS_STAKEHOLDER = "isStakeholder"
    IS_ASSOCIATED = "isAssociated"
    GENDER = "gender"
    BIRTHDAY = "birthday"
    CITIES_ARRAY = "citiesArray"
    CITY_NAMES_ARRAY = "cityNamesArray"
    CONFIRM = "confirm"


class SessionData(BaseModel):
    """Session data model."""
    
    flow_step: Optional[FlowStep] = Field(default=None, description="Current flow step")
    flow_type: Optional[FlowType] = Field(default=None, description="Type of flow")
    collected_data: Dict[str, Any] = Field(default_factory=dict, description="Collected user data")
    created_at: str = Field(..., description="Session creation timestamp")
    last_activity: str = Field(..., description="Last activity timestamp")
    is_confirmed: bool = Field(default=False, description="Whether user has confirmed the details")


class UserData(BaseModel):
    """User data collected during flows."""

    # Basic user information
    name: Optional[str] = Field(default=None, description="User full name")
    firstName: Optional[str] = Field(default=None, description="User first name")
    lastName: Optional[str] = Field(default=None, description="User last name")
    email: Optional[str] = Field(default=None, description="User email")
    password: Optional[str] = Field(default=None, description="User password")
    phoneNumber: Optional[str] = Field(default=None, description="User phone number")

    # Location and city information
    cityId: Optional[str] = Field(default=None, description="City identifier")
    cityName: Optional[str] = Field(default=None, description="City name (user input)")
    city: Optional[str] = Field(default=None, description="City name (legacy field)")
    state: Optional[str] = Field(default=None, description="State or province")
    zipCode: Optional[str] = Field(default=None, description="ZIP or postal code")
    streetAddressOne: Optional[str] = Field(default=None, description="Primary street address")
    streetAddressTwo: Optional[str] = Field(default=None, description="Secondary street address")

    # Role and permissions
    role: Optional[str] = Field(default=None, description="User role")
    assignedRole: Optional[str] = Field(default=None, description="Assigned role")
    userType: Optional[str] = Field(default=None, description="Type of user")
    type: Optional[str] = Field(default=None, description="Account type")

    # Registration metadata
    registeredFrom: Optional[str] = Field(default=None, description="Registration source")
    userAddedFrom: Optional[str] = Field(default=None, description="Source where user was added from")
    createdBy: Optional[str] = Field(default=None, description="User ID who created this account")

    # Flags and associations
    isStakeholder: Optional[bool] = Field(default=None, description="Whether user is a stakeholder")
    isAssociated: Optional[bool] = Field(default=None, description="Whether user is associated with an organization")

    # Additional information
    gender: Optional[str] = Field(default=None, description="User gender")
    birthday: Optional[str] = Field(default=None, description="User birthday in YYYY-MM-DD format")

    # Cities array for multi-city access
    citiesArray: Optional[List[str]] = Field(default=None, description="Array of city IDs user has access to")
    cityNamesArray: Optional[List[str]] = Field(default=None, description="Array of city names user has access to (user input)")

    # Confirmation status
    is_confirmed: Optional[bool] = Field(default=False, description="Whether the user has confirmed their details")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary, excluding None values."""
        return {k: v for k, v in self.dict().items() if v is not None}
