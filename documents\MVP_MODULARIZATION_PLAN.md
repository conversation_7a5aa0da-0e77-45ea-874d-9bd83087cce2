# MVP Modularization Plan: Conversational AI Operating System
## Phase 1 & 2 - Technical Implementation Guide

**Document Version:** 1.0  
**Date:** November 2025  
**Status:** Architecture Design & Implementation Blueprint  

---

## Table of Contents

1. [Executive Summary](#executive-summary)
2. [Current Architecture Analysis](#current-architecture-analysis)
3. [Modularization Strategy](#modularization-strategy)
4. [Phase 1: Core MCP Services](#phase-1-core-mcp-services)
5. [Phase 2: Conversation Orchestrator](#phase-2-conversation-orchestrator)
6. [Implementation Roadmap](#implementation-roadmap)
7. [API Contract Specifications](#api-contract-specifications)
8. [File Structure & Code Organization](#file-structure--code-organization)
9. [Migration Path & Backward Compatibility](#migration-path--backward-compatibility)
10. [Deployment & Testing Strategy](#deployment--testing-strategy)

---

## Executive Summary

### Current State
Your system is currently a **monolithic FastAPI application** that handles multiple concerns:
- **Intent Classification** (user intent detection via Hugging Face)
- **Chat Generation** (Gemini AI responses)
- **Authentication** (signup/login via external API)
- **Session Management** (user flow orchestration)
- **Activity Management** (CRUD operations on activities)
- **External Integrations** (city data, authentication service, Gemini API)

### Target State
Transform into a **modular MCP-based architecture** where:
- Each business domain becomes an independent **MCP Service** with its own responsibility
- A **Conversation Orchestrator (Gateway)** routes user messages to the appropriate MCP
- MCPs expose standardized REST endpoints following MCP protocol
- Frontend (Next.js) communicates with the Orchestrator, which delegates to specialized services
- Services can be independently scaled, tested, and deployed

### MVP Scope (Phase 1 & 2)
- **Phase 1:** Extract `OnboardingMCP` and `ActivityMCP` from monolith
- **Phase 2:** Build minimal `ConversationOrchestrator` that routes between MCPs

---

## Current Architecture Analysis

### Monolithic Design Problems

```
┌─────────────────────────────────────────────────────────────┐
│                  FastAPI Main Application                   │
│  (/app/main.py)                                             │
├─────────────────────────────────────────────────────────────┤
│                                                              │
│  Routes Layer (/app/api/routes.py)                          │
│  ├─ POST /onboarding      ──→ Intent Classification         │
│  ├─ POST /gemini-chat     ──→ Gemini Service               │
│  ├─ POST /gemini-chat-with-intent ──→ Combined Logic       │
│  ├─ POST /auth/*          ──→ Auth Service                 │
│  ├─ GET/POST /activities/* ──→ Activity Service            │
│  └─ GET/POST /submissions/* ──→ Submission Service         │
│                                                              │
│  Services Layer (/app/services/)                            │
│  ├─ intent_service.py (Intent Classification Pipeline)     │
│  ├─ gemini_service.py (Gemini AI Integration)             │
│  ├─ auth_service.py (External Auth API)                   │
│  ├─ session_service.py (User Flow Orchestration)          │
│  ├─ activity_service.py (Activity Management)             │
│  ├─ city_service.py (City Data)                           │
│  └─ submission_service.py (Submissions)                   │
│                                                              │
│  Data Layer                                                 │
│  ├─ DynamoDB (activities, submissions)                     │
│  └─ In-memory Sessions (/app/services/session_service.py) │
│                                                              │
└─────────────────────────────────────────────────────────────┘
```

### Problems with Current Architecture
1. **Tight Coupling:** All routes depend on all services
2. **Single Deployment Unit:** Cannot scale individual features
3. **Mixed Concerns:** Session management mixes with business logic
4. **Difficult Testing:** Hard to test services in isolation
5. **Scaling Issues:** All traffic hits one application instance
6. **Knowledge Silos:** Intent classification, activity, and auth logic intertwined

---

## Modularization Strategy

### MCP Service Architecture

```
┌────────────────────────────────────────────────────────────────────────┐
│                        Next.js Frontend (Browser)                      │
└─────────────────────────┬──────────────────────────────────────────────┘
                          │
                          │ HTTP/WebSocket
                          ▼
┌────────────────────────────────────────────────────────────────────────┐
│              Conversation Orchestrator (Phase 2 - Gateway)             │
│  (/orchestrator/main.py)                                               │
│                                                                         │
│  Responsibilities:                                                      │
│  ├─ Receive user message via REST/WebSocket                           │
│  ├─ Extract session_id from request                                    │
│  ├─ Route to appropriate MCP based on intent/context                   │
│  ├─ Aggregate responses from multiple MCPs                             │
│  ├─ Maintain central session context (optional)                        │
│  └─ Return unified response to client                                  │
└─────────────┬──────────────┬──────────────────┬───────────────────────┘
              │              │                  │
         HTTP/REST      HTTP/REST           HTTP/REST
              │              │                  │
    ┌─────────▼─────┐ ┌──────▼──────┐ ┌───────▼────────┐
    │ OnboardingMCP │ │ ActivityMCP │ │ ChatMCP        │
    │ (Port 8001)   │ │ (Port 8002) │ │ (Port 8003)    │
    └───────────────┘ └─────────────┘ └────────────────┘
         │                 │                │
    ┌────┴────┐       ┌────┴────┐    ┌────┴────┐
    │ Services │       │ Services │    │ Services│
    │ ├─ Auth   │       │ ├─ Activity│    │ ├─ Gemini│
    │ ├─ Intent │       │ ├─ Submission  │ ├─ Intent│
    │ └─ Session│       │ └─ City   │    │ └─ Session
    └────┬────┘       └────┬────┘    └────┬────┘
         │                 │                │
    ┌────┴──────┬──────┬───┴────┬──┬──┬────┴──┐
    │ DynamoDB  │ Ext. │External│ │Gmini│Redis│
    │ Activities│ Auth │ City  │ │API  │Sess.│
    │           │ API  │ Service│ │     │     │
    └───────────┴──────┴────────┴──┴─────┴─────┘
```

### Key Principles

1. **MCP = Microservice Context Protocol**
   - Each MCP is a self-contained service with clear responsibilities
   - Exposes standardized endpoints (request/response protocol)
   - Can be developed, tested, and deployed independently

2. **Service Boundaries**
   - **OnboardingMCP:** User authentication, signup/login flows, session management
   - **ActivityMCP:** Activity CRUD, activity intent classification, submissions
   - **ChatMCP:** Conversational AI, intent classification for general queries, Gemini integration

3. **Communication Pattern**
   - Services communicate via HTTP/REST (not directly importing code)
   - Message format: `{ "type": "...", "session_id": "...", "data": {...} }`
   - Response format: `{ "success": bool, "type": "...", "data": {...}, "error": {...} }`

---

## Phase 1: Core MCP Services

### 1.1 OnboardingMCP Service

#### Responsibility
Handle all user onboarding workflows: authentication, signup/login, session management.

#### Directory Structure

```
my_onboarding_api/mcp/onboarding_mcp/
├── main.py                          # FastAPI app + service initialization
├── config.py                        # MCP-specific configuration
├── requirements.txt                 # MCP dependencies
├── Dockerfile                       # Container definition
├──── api/
│    ├── __init__.py
│    ├── routes.py                   # MCP endpoints
│    └── models.py                   # MCP request/response schemas
├──── services/
│    ├── __init__.py
│    ├── auth_service.py            # (MOVED from main app)
│    ├── session_service.py         # (MOVED from main app)
│    └── onboarding_flow.py         # (NEW) Flow orchestration
├──── schemas/
│    ├── __init__.py
│    ├── requests.py                # MCP request models
│    └── responses.py               # MCP response models
└──── core/
     ├── __init__.py
     ├── exceptions.py              # MCP-specific exceptions
     └── logging.py                 # MCP logging configuration
```

#### MCP Endpoints (OnboardingMCP)

```python
# Base URL: http://localhost:8001

# Endpoint 1: Initialize Session
POST /mcp/onboarding/session/init
Request:
{
  "session_id": "uuid",
  "user_agent": "string",
  "client_ip": "string"
}
Response:
{
  "success": true,
  "type": "session_initialized",
  "data": {
    "session_id": "uuid",
    "created_at": "ISO8601",
    "status": "active"
  }
}

# Endpoint 2: Process Signup
POST /mcp/onboarding/signup/process
Request:
{
  "session_id": "uuid",
  "message": "I want to sign up",
  "context": { "previous_step": "..." }
}
Response:
{
  "success": true,
  "type": "flow_response",
  "data": {
    "flow_type": "signup",
    "current_step": "first_name",
    "message": "What's your first name?",
    "flow_state": {...}
  }
}
OR
{
  "success": true,
  "type": "signup_complete",
  "data": {
    "user": { user_object },
    "token": "jwt_token",
    "message": "Welcome!"
  }
}

# Endpoint 3: Process Login
POST /mcp/onboarding/login/process
Request:
{
  "session_id": "uuid",
  "message": "<EMAIL>",  # OR password
  "context": { "previous_step": "..." }
}
Response:
{
  "success": true,
  "type": "flow_response",
  "data": {
    "flow_type": "login",
    "current_step": "password",
    "message": "Enter your password:",
    "flow_state": {...}
  }
}
OR
{
  "success": true,
  "type": "login_complete",
  "data": {
    "user": { user_object },
    "token": "jwt_token",
    "message": "Welcome back!"
  }
}

# Endpoint 4: Process Message (Unified)
POST /mcp/onboarding/message
Request:
{
  "session_id": "uuid",
  "message": "user input",
  "detected_intent": "signup|login|none",  # From orchestrator
  "context": { previous_flow_state }
}
Response:
{
  "success": true,
  "type": "flow_response|flow_complete|error",
  "data": {...},
  "requires_action": "flow_continue|flow_complete|error"
}

# Endpoint 5: Get Flow State
GET /mcp/onboarding/session/{session_id}/state
Response:
{
  "success": true,
  "type": "flow_state",
  "data": {
    "flow_type": "signup|login|none",
    "current_step": "first_name|email|password",
    "collected_data": {...},
    "validation_errors": [...]
  }
}

# Endpoint 6: Health Check
GET /mcp/onboarding/health
Response:
{
  "status": "healthy",
  "service": "onboarding_mcp",
  "version": "1.0.0",
  "dependencies": {
    "database": "connected",
    "external_auth_api": "connected"
  }
}
```

#### OnboardingMCP Implementation Details

**File: `my_onboarding_api/mcp/onboarding_mcp/main.py`**

```python
from fastapi import FastAPI, HTTPException
from contextlib import asynccontextmanager
from typing import Dict, Any

# Core
from .core.config import settings as onboarding_settings
from .core.logging import setup_logging, get_logger

# Services
from .services.auth_service import auth_service
from .services.session_service import session_service
from .services.onboarding_flow import onboarding_flow_orchestrator

# Routes
from .api.routes import router as onboarding_router

logger = get_logger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan manager for OnboardingMCP."""
    # Startup
    logger.info("Starting OnboardingMCP service")
    try:
        # Initialize dependencies
        await auth_service.initialize()
        logger.info("Auth service initialized")
        
        yield  # Application runs here
        
    except Exception as e:
        logger.error(f"Error during startup: {str(e)}")
        raise
    finally:
        # Shutdown
        logger.info("Shutting down OnboardingMCP service")
        await auth_service.cleanup()

def create_onboarding_mcp() -> FastAPI:
    """Create OnboardingMCP FastAPI application."""
    setup_logging()
    
    app = FastAPI(
        title="OnboardingMCP",
        version="1.0.0",
        description="MCP service for user onboarding workflows",
        docs_url="/docs" if onboarding_settings.debug else None,
        redoc_url="/redoc" if onboarding_settings.debug else None,
        lifespan=lifespan
    )
    
    # Add CORS
    from fastapi.middleware.cors import CORSMiddleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=onboarding_settings.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Include routers
    app.include_router(onboarding_router, prefix="/mcp/onboarding", tags=["onboarding"])
    
    # Health endpoint
    @app.get("/health")
    async def health_check():
        return {
            "status": "healthy",
            "service": "onboarding_mcp",
            "version": "1.0.0"
        }
    
    return app

app = create_onboarding_mcp()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=onboarding_settings.host,
        port=onboarding_settings.port,
        reload=onboarding_settings.debug
    )
```

**File: `my_onboarding_api/mcp/onboarding_mcp/api/routes.py`**

```python
from fastapi import APIRouter, HTTPException, status, Header
from typing import Annotated, Dict, Any, Optional

from ..services.session_service import session_service
from ..services.onboarding_flow import onboarding_flow_orchestrator
from .models import (
    InitSessionRequest,
    ProcessMessageRequest,
    FlowResponseModel,
    FlowCompleteResponseModel
)
from ..core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()

@router.post("/session/init")
async def init_session(request: InitSessionRequest) -> Dict[str, Any]:
    """Initialize a new onboarding session."""
    try:
        session = session_service.create_session(
            session_id=request.session_id,
            metadata={
                "user_agent": request.user_agent,
                "client_ip": request.client_ip
            }
        )
        
        return {
            "success": True,
            "type": "session_initialized",
            "data": {
                "session_id": session.id,
                "created_at": session.created_at,
                "status": "active"
            }
        }
    except Exception as e:
        logger.error(f"Failed to initialize session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/message")
async def process_message(
    request: ProcessMessageRequest
) -> Dict[str, Any]:
    """
    Process incoming message in onboarding flow.
    
    Handles both signup and login flows with unified interface.
    """
    session_id = request.session_id
    message = request.message
    detected_intent = request.detected_intent
    
    try:
        logger.info(f"Processing message for session {session_id}, intent: {detected_intent}")
        
        # Get or create session
        session = session_service.get_session(session_id)
        
        # Handle flow based on intent
        if detected_intent == "signup":
            result = await onboarding_flow_orchestrator.process_signup_message(
                session_id, message
            )
        elif detected_intent == "login":
            result = await onboarding_flow_orchestrator.process_login_message(
                session_id, message
            )
        else:
            return {
                "success": False,
                "type": "error",
                "data": {"error": "Unrecognized intent for onboarding flow"}
            }
        
        return result
        
    except Exception as e:
        logger.error(f"Error processing message: {str(e)}")
        return {
            "success": False,
            "type": "error",
            "data": {"error": str(e)}
        }

@router.get("/session/{session_id}/state")
async def get_flow_state(session_id: str) -> Dict[str, Any]:
    """Get current flow state for a session."""
    try:
        session = session_service.get_session(session_id)
        
        return {
            "success": True,
            "type": "flow_state",
            "data": {
                "flow_type": session.flow_type,
                "current_step": session.flow_step,
                "collected_data": session.collected_data,
                "validation_errors": session.validation_errors or []
            }
        }
    except Exception as e:
        logger.error(f"Error getting flow state: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "onboarding_mcp",
        "version": "1.0.0"
    }
```

---

### 1.2 ActivityMCP Service

#### Responsibility
Manage all activity-related operations: CRUD, classification, submissions.

#### Directory Structure

```
my_onboarding_api/mcp/activity_mcp/
├── main.py                          # FastAPI app
├── config.py                        # MCP-specific configuration
├── requirements.txt
├── Dockerfile
├──── api/
│    ├── __init__.py
│    ├── routes.py                   # Activity CRUD endpoints
│    └── models.py                   # Request/response schemas
├──── services/
│    ├── __init__.py
│    ├── activity_service.py        # (MOVED from main app)
│    ├── activity_classifier.py     # (EXTRACTED) Intent classification
│    └── submission_service.py      # (MOVED from main app)
├──── schemas/
│    ├── __init__.py
│    ├── activity.py                # Activity models
│    └── submission.py              # Submission models
└──── core/
     ├── __init__.py
     ├── exceptions.py
     └── logging.py
```

#### MCP Endpoints (ActivityMCP)

```python
# Base URL: http://localhost:8002

# Endpoint 1: Create Activity
POST /mcp/activities/
Request:
{
  "title": "string",
  "description": "string",
  "content": "string",
  "type": "string",
  "city_id": "string",
  "created_by": "user_id"
}
Response:
{
  "success": true,
  "type": "activity_created",
  "data": {
    "id": "activity_id",
    "title": "string",
    "created_at": "ISO8601",
    ...
  }
}

# Endpoint 2: Get Activity
GET /mcp/activities/{activity_id}
Response:
{
  "success": true,
  "type": "activity_retrieved",
  "data": { activity_object }
}

# Endpoint 3: List Activities
GET /mcp/activities/?city_id=...&type=...&limit=10
Response:
{
  "success": true,
  "type": "activities_list",
  "data": {
    "activities": [...],
    "total": 50,
    "limit": 10,
    "offset": 0
  }
}

# Endpoint 4: Update Activity
PUT /mcp/activities/{activity_id}
Request:
{
  "title": "updated",
  "description": "updated"
}
Response:
{
  "success": true,
  "type": "activity_updated",
  "data": { updated_activity }
}

# Endpoint 5: Delete Activity
DELETE /mcp/activities/{activity_id}
Response:
{
  "success": true,
  "type": "activity_deleted",
  "data": { "id": "activity_id" }
}

# Endpoint 6: Classify Activity Intent
POST /mcp/activities/classify
Request:
{
  "text": "string"
}
Response:
{
  "success": true,
  "type": "intent_classification",
  "data": {
    "intents": [
      { "label": "community_service", "score": 0.95 },
      { "label": "learning", "score": 0.04 }
    ]
  }
}

# Endpoint 7: Create Submission
POST /mcp/submissions/
Request:
{
  "activity_id": "string",
  "user_id": "string",
  "content": "string"
}
Response:
{
  "success": true,
  "type": "submission_created",
  "data": { submission_object }
}

# Endpoint 8: Health Check
GET /mcp/activities/health
Response:
{
  "status": "healthy",
  "service": "activity_mcp",
  "version": "1.0.0"
}
```

---

### 1.3 Chat/Conversational MCP Service (Optional Phase 1 Extension)

#### Responsibility
Handle Gemini AI integration and general conversation (not user flows).

#### Directory Structure

```
my_onboarding_api/mcp/chat_mcp/
├── main.py
├── config.py
├── requirements.txt
├── Dockerfile
├──── api/
│    ├── __init__.py
│    ├── routes.py
│    └── models.py
├──── services/
│    ├── __init__.py
│    ├── gemini_service.py        # (MOVED from main app)
│    ├── intent_classifier.py     # (EXTRACTED) General intent classification
│    └── conversation_manager.py  # (NEW) Conversation context management
├──── schemas/
│    ├── __init__.py
│    └── messages.py
└──── core/
     ├── __init__.py
     ├── exceptions.py
     └── logging.py
```

#### MCP Endpoints (ChatMCP)

```python
# Base URL: http://localhost:8003

# Endpoint 1: General Chat
POST /mcp/chat/message
Request:
{
  "session_id": "uuid",
  "message": "Hello, I'd like to learn about activities",
  "context": { "previous_messages": [...] }
}
Response:
{
  "success": true,
  "type": "chat_response",
  "data": {
    "message": "AI response",
    "intent_detected": "general_inquiry",
    "suggested_actions": []
  }
}

# Endpoint 2: Classify General Intent
POST /mcp/chat/classify-intent
Request:
{
  "message": "string"
}
Response:
{
  "success": true,
  "type": "intent_classification",
  "data": {
    "intents": [
      { "label": "general_inquiry", "score": 0.92 }
    ]
  }
}

# Endpoint 3: Get Conversation History
GET /mcp/chat/session/{session_id}/history
Response:
{
  "success": true,
  "type": "conversation_history",
  "data": {
    "messages": [...],
    "total": 25
  }
}

# Endpoint 4: Health Check
GET /mcp/chat/health
Response:
{
  "status": "healthy",
  "service": "chat_mcp",
  "version": "1.0.0"
}
```

---

## Phase 2: Conversation Orchestrator

### 2.1 Architecture

The Conversation Orchestrator is a **lightweight gateway** that:
1. Receives user messages from the frontend
2. Determines which MCP should handle the message
3. Forwards to the appropriate MCP
4. Aggregates and transforms the response
5. Returns unified response to client

### 2.2 Directory Structure

```
my_onboarding_api/orchestrator/
├── main.py                          # FastAPI app + initialization
├── config.py                        # Orchestrator configuration
├── requirements.txt
├── Dockerfile
├── docker-compose.yml              # For local development
├──── api/
│    ├── __init__.py
│    ├── routes.py                   # Main endpoints
│    └── models.py                   # Request/response schemas
├──── services/
│    ├── __init__.py
│    ├── router.py                   # MCP routing logic
│    ├── intent_detector.py         # Intent detection for routing
│    ├── session_manager.py         # Cross-MCP session context
│    └── mcp_client.py              # HTTP client for MCP calls
├──── core/
│    ├── __init__.py
│    ├── config.py
│    ├── exceptions.py
│    ├── logging.py
│    └── constants.py               # MCP endpoints, timeouts, etc.
└──── schemas/
     ├── __init__.py
     ├── requests.py                # Unified request models
     └── responses.py               # Unified response models
```

### 2.3 Key Components

#### 2.3.1 Message Router Service

**File: `orchestrator/services/router.py`**

```python
from enum import Enum
from typing import Literal, Dict, Any
from ..core.logging import get_logger

logger = get_logger(__name__)

class MCPServiceType(Enum):
    """Available MCP services."""
    ONBOARDING = "onboarding"
    ACTIVITY = "activity"
    CHAT = "chat"

class MessageRouter:
    """Routes messages to appropriate MCP based on detected intent."""
    
    def __init__(self):
        """Initialize router."""
        self.mcp_endpoints = {
            MCPServiceType.ONBOARDING: "http://localhost:8001/mcp/onboarding",
            MCPServiceType.ACTIVITY: "http://localhost:8002/mcp/activities",
            MCPServiceType.CHAT: "http://localhost:8003/mcp/chat"
        }
    
    def route(
        self, 
        message: str, 
        detected_intent: str,
        flow_context: Dict[str, Any]
    ) -> MCPServiceType:
        """
        Determine which MCP should handle this message.
        
        Logic:
        1. If active flow (signup/login), route to ONBOARDING
        2. If activity-related intent, route to ACTIVITY
        3. Otherwise, route to CHAT
        
        Args:
            message: User message
            detected_intent: Primary intent from classifier
            flow_context: Active flow information
            
        Returns:
            Target MCP service type
        """
        logger.debug(f"Routing message. Intent: {detected_intent}, Flow: {flow_context.get('flow_type')}")
        
        # Priority 1: Active onboarding flow
        if flow_context.get('flow_type') in ['signup', 'login']:
            logger.debug("Routing to ONBOARDING (active flow)")
            return MCPServiceType.ONBOARDING
        
        # Priority 2: Onboarding intents
        if detected_intent in ['signup', 'login']:
            logger.debug("Routing to ONBOARDING (intent-based)")
            return MCPServiceType.ONBOARDING
        
        # Priority 3: Activity-related intents
        if detected_intent in ['activity_query', 'create_activity', 'view_activities']:
            logger.debug("Routing to ACTIVITY (activity intent)")
            return MCPServiceType.ACTIVITY
        
        # Default: General conversation
        logger.debug("Routing to CHAT (default)")
        return MCPServiceType.CHAT
    
    def get_mcp_endpoint(self, service_type: MCPServiceType) -> str:
        """Get HTTP endpoint for MCP service."""
        return self.mcp_endpoints.get(service_type)

# Singleton instance
message_router = MessageRouter()
```

#### 2.3.2 Intent Detector Service

**File: `orchestrator/services/intent_detector.py`**

```python
from typing import Tuple, Dict, Any
import httpx
from ..core.logging import get_logger

logger = get_logger(__name__)

class IntentDetector:
    """
    Detect user intent for message routing.
    
    Strategy:
    1. Check if message matches signup/login keywords
    2. Call intent classification MCP (delegated to chat_mcp)
    3. Return primary intent with confidence
    """
    
    # Keyword patterns for quick intent detection
    SIGNUP_KEYWORDS = {'sign up', 'register', 'create account', 'new account', 'join'}
    LOGIN_KEYWORDS = {'login', 'sign in', 'log in', 'authenticate'}
    
    async def detect_intent(self, message: str) -> Tuple[str, float]:
        """
        Detect primary user intent.
        
        Returns:
            Tuple of (intent_label, confidence_score)
        """
        message_lower = message.lower()
        
        # Quick keyword-based detection
        for keyword in self.SIGNUP_KEYWORDS:
            if keyword in message_lower:
                logger.debug(f"Detected SIGNUP intent via keyword: {keyword}")
                return "signup", 0.95
        
        for keyword in self.LOGIN_KEYWORDS:
            if keyword in message_lower:
                logger.debug(f"Detected LOGIN intent via keyword: {keyword}")
                return "login", 0.95
        
        # Fallback: Use intent classification (can delegate to chat MCP)
        try:
            return await self._call_intent_classifier(message)
        except Exception as e:
            logger.warning(f"Intent classification failed: {str(e)}, defaulting to 'general_inquiry'")
            return "general_inquiry", 0.5
    
    async def _call_intent_classifier(self, message: str) -> Tuple[str, float]:
        """Call intent classification endpoint (ChatMCP)."""
        # This would be implemented to call ChatMCP's classify endpoint
        # For now, return default
        return "general_inquiry", 0.5

# Singleton instance
intent_detector = IntentDetector()
```

#### 2.3.3 Session Manager

**File: `orchestrator/services/session_manager.py`**

```python
from typing import Dict, Any, Optional
from datetime import datetime, timezone
from ..core.logging import get_logger

logger = get_logger(__name__)

class SessionContext:
    """Central session context across MCPs."""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.created_at = datetime.now(timezone.utc).isoformat()
        self.last_activity = datetime.now(timezone.utc).isoformat()
        
        # Flow context
        self.flow_type: Optional[str] = None  # 'signup', 'login', None
        self.flow_step: Optional[str] = None
        
        # Metadata
        self.user_data: Dict[str, Any] = {}
        self.intent_history: list = []
        self.mcp_responses: Dict[str, Any] = {}  # Track responses from each MCP
    
    def update_flow_context(self, flow_type: Optional[str], flow_step: Optional[str]) -> None:
        """Update current flow state."""
        self.flow_type = flow_type
        self.flow_step = flow_step
        self.last_activity = datetime.now(timezone.utc).isoformat()
    
    def record_intent(self, intent: str, confidence: float) -> None:
        """Record detected intent for analysis."""
        self.intent_history.append({
            "intent": intent,
            "confidence": confidence,
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
    
    def get_flow_context(self) -> Dict[str, Any]:
        """Get flow context for MCP routing."""
        return {
            "flow_type": self.flow_type,
            "flow_step": self.flow_step,
            "session_id": self.session_id
        }

class SessionManager:
    """Manage session contexts across MCPs."""
    
    def __init__(self):
        self._sessions: Dict[str, SessionContext] = {}
    
    def get_session(self, session_id: str) -> SessionContext:
        """Get or create session context."""
        if session_id not in self._sessions:
            logger.debug(f"Creating new session context: {session_id}")
            self._sessions[session_id] = SessionContext(session_id)
        
        return self._sessions[session_id]
    
    def end_session(self, session_id: str) -> None:
        """End session and clean up."""
        if session_id in self._sessions:
            logger.debug(f"Ending session: {session_id}")
            del self._sessions[session_id]

# Singleton instance
session_manager = SessionManager()
```

#### 2.3.4 MCP Client

**File: `orchestrator/services/mcp_client.py`**

```python
from typing import Dict, Any, Optional
import httpx
from ..core.logging import get_logger
from ..core.config import settings

logger = get_logger(__name__)

class MCPClient:
    """HTTP client for communicating with MCP services."""
    
    def __init__(self):
        self.timeout = settings.mcp_request_timeout
        self.client = httpx.AsyncClient(timeout=self.timeout)
    
    async def send_message(
        self,
        mcp_url: str,
        endpoint: str,
        request_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Send request to MCP and return response.
        
        Args:
            mcp_url: Base URL of MCP
            endpoint: Endpoint path (e.g., "/message")
            request_data: Request payload
            
        Returns:
            MCP response data
            
        Raises:
            httpx.HTTPError: If request fails
            TimeoutError: If request times out
        """
        full_url = f"{mcp_url}{endpoint}"
        logger.debug(f"Sending request to {full_url}")
        
        try:
            response = await self.client.post(
                full_url,
                json=request_data,
                headers={
                    "Content-Type": "application/json",
                    "X-Orchestrator-Request": "true"
                }
            )
            
            response.raise_for_status()
            return response.json()
            
        except httpx.TimeoutException as e:
            logger.error(f"MCP request timeout to {full_url}: {str(e)}")
            raise
        except httpx.HTTPError as e:
            logger.error(f"MCP request failed to {full_url}: {str(e)}")
            raise
    
    async def health_check(self, mcp_url: str) -> bool:
        """Check if MCP service is healthy."""
        try:
            response = await self.client.get(
                f"{mcp_url}/health",
                timeout=5
            )
            return response.status_code == 200
        except Exception as e:
            logger.warning(f"Health check failed for {mcp_url}: {str(e)}")
            return False
    
    async def close(self):
        """Close HTTP client."""
        await self.client.aclose()

# Singleton instance
mcp_client = MCPClient()
```

### 2.4 Orchestrator Main Endpoints

**File: `orchestrator/api/routes.py`**

```python
from fastapi import APIRouter, HTTPException, Header, status
from typing import Annotated, Dict, Any, Optional
import uuid

from ..services.router import message_router, MCPServiceType
from ..services.intent_detector import intent_detector
from ..services.session_manager import session_manager
from ..services.mcp_client import mcp_client
from .models import (
    UserMessageRequest,
    OrchestrationResponse,
    MCPDelegationResponse
)
from ..core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()

@router.post("/message")
async def process_user_message(
    request: UserMessageRequest,
    session_id: Annotated[str, Header()] = None
) -> OrchestrationResponse:
    """
    Main endpoint for processing user messages.
    
    Flow:
    1. Create/get session
    2. Detect intent
    3. Route to appropriate MCP
    4. Transform and return response
    
    Args:
        request: User message request
        session_id: Optional session ID header
        
    Returns:
        Unified response from delegated MCP
    """
    # Generate or use provided session ID
    if not session_id:
        session_id = str(uuid.uuid4())
    
    logger.info(f"Processing message for session: {session_id}")
    logger.debug(f"Message: {request.message[:100]}...")
    
    try:
        # Get session context
        session = session_manager.get_session(session_id)
        
        # Detect intent
        intent, confidence = await intent_detector.detect_intent(request.message)
        session.record_intent(intent, confidence)
        
        logger.debug(f"Detected intent: {intent} (confidence: {confidence})")
        
        # Route to appropriate MCP
        target_mcp = message_router.route(
            request.message,
            intent,
            session.get_flow_context()
        )
        
        logger.info(f"Routing to MCP: {target_mcp.value}")
        
        # Build MCP request
        mcp_request = {
            "session_id": session_id,
            "message": request.message,
            "detected_intent": intent,
            "context": session.get_flow_context()
        }
        
        # Get MCP endpoint
        mcp_endpoint = message_router.get_mcp_endpoint(target_mcp)
        
        # Call MCP service
        mcp_response = await mcp_client.send_message(
            mcp_url=mcp_endpoint,
            endpoint="/message",
            request_data=mcp_request
        )
        
        # Update session based on MCP response
        if mcp_response.get("success"):
            flow_data = mcp_response.get("data", {})
            if "flow_type" in flow_data:
                session.update_flow_context(
                    flow_type=flow_data.get("flow_type"),
                    flow_step=flow_data.get("current_step")
                )
        
        # Return unified response
        return OrchestrationResponse(
            success=mcp_response.get("success", False),
            session_id=session_id,
            delegated_to=target_mcp.value,
            intent_detected=intent,
            response=mcp_response,
            timestamp=session.last_activity
        )
        
    except Exception as e:
        logger.error(f"Error processing message: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Message processing failed",
                "message": str(e),
                "session_id": session_id
            }
        )

@router.get("/session/{session_id}/info")
async def get_session_info(session_id: str) -> Dict[str, Any]:
    """Get session information."""
    try:
        session = session_manager.get_session(session_id)
        return {
            "success": True,
            "data": {
                "session_id": session_id,
                "created_at": session.created_at,
                "flow_type": session.flow_type,
                "flow_step": session.flow_step,
                "intent_history": session.intent_history[-5:]  # Last 5 intents
            }
        }
    except Exception as e:
        logger.error(f"Error getting session info: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """Check orchestrator and all MCP services health."""
    from ..services.router import message_router
    
    mcp_health = {}
    for service_type in MCPServiceType:
        endpoint = message_router.get_mcp_endpoint(service_type)
        is_healthy = await mcp_client.health_check(endpoint)
        mcp_health[service_type.value] = "healthy" if is_healthy else "unhealthy"
    
    return {
        "status": "healthy",
        "service": "orchestrator",
        "version": "1.0.0",
        "mcp_services": mcp_health
    }
```

---

## Implementation Roadmap

### Phase 1: Modularization (Weeks 1-3)

#### Week 1: Onboarding MCP
1. Create directory structure
2. Extract auth_service and session_service
3. Create OnboardingMCP main app
4. Implement `/mcp/onboarding/session/init` endpoint
5. Implement `/mcp/onboarding/message` endpoint
6. Write unit tests

#### Week 2: Activity MCP
1. Create directory structure
2. Extract activity_service and submission_service
3. Create ActivityMCP main app
4. Implement activity CRUD endpoints
5. Implement activity classification endpoint
6. Write unit tests

#### Week 3: Chat MCP (Optional)
1. Create directory structure
2. Extract gemini_service and intent_classifier
3. Create ChatMCP main app
4. Implement chat endpoints
5. Write unit tests

### Phase 2: Orchestrator (Weeks 4-5)

#### Week 4: Core Orchestrator
1. Create orchestrator directory structure
2. Implement message router
3. Implement intent detector
4. Implement session manager
5. Implement MCP client
6. Write unit tests

#### Week 5: Integration & Testing
1. Implement main orchestrator routes
2. Integration tests with all MCPs
3. Load testing
4. Documentation

### Execution Steps

1. **Backup current code:**
   ```bash
   git checkout -b feature/mcp-modularization
   ```

2. **Create MCP service directories:**
   ```bash
   mkdir -p my_onboarding_api/mcp/{onboarding_mcp,activity_mcp,chat_mcp,orchestrator}/{api,services,schemas,core}
   ```

3. **Extract services incrementally** (one at a time)
4. **Run parallel testing** (old monolith + new MCPs)
5. **Gradual traffic migration** to new orchestrator
6. **Decommission monolith** once stable

---

## API Contract Specifications

### Request/Response Pattern

All MCP services follow this contract:

**MCP Request:**
```json
{
  "session_id": "uuid",
  "message": "user input",
  "detected_intent": "intent_label",
  "context": {
    "flow_type": "signup|login|null",
    "flow_step": "step_name",
    "previous_responses": [...]
  }
}
```

**MCP Response (Success):**
```json
{
  "success": true,
  "type": "response_type",
  "data": {
    "message": "User-facing message",
    "next_action": "flow_continue|flow_complete|action_required",
    "metadata": {}
  }
}
```

**MCP Response (Error):**
```json
{
  "success": false,
  "type": "error",
  "error": {
    "code": "error_code",
    "message": "Human-readable error",
    "details": {}
  }
}
```

### Status Codes

- `200`: Successful operation
- `400`: Bad request / validation error
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not found
- `500`: Internal server error
- `503`: Service unavailable

---

## File Structure & Code Organization

### Monolith to MCP Migration Map

| Current Location | MCP Location | MCP Service |
|---|---|---|
| `app/services/intent_service.py` | `mcp/chat_mcp/services/` | ChatMCP |
| `app/services/gemini_service.py` | `mcp/chat_mcp/services/` | ChatMCP |
| `app/services/auth_service.py` | `mcp/onboarding_mcp/services/` | OnboardingMCP |
| `app/services/session_service.py` | `mcp/onboarding_mcp/services/` | OnboardingMCP |
| `app/services/activity_service.py` | `mcp/activity_mcp/services/` | ActivityMCP |
| `app/services/submission_service.py` | `mcp/activity_mcp/services/` | ActivityMCP |
| `app/services/city_service.py` | Shared library or as utility | All MCPs |

### Shared Code Strategy

```
my_onboarding_api/
├── shared/                          # Shared code across MCPs
│   ├── __init__.py
│   ├── models/
│   │   ├── __init__.py
│   │   ├── user.py                  # User model
│   │   ├── activity.py              # Activity model
│   │   ├── session.py               # Session model
│   │   └── responses.py             # Common response models
│   ├── exceptions/
│   │   ├── __init__.py
│   │   └── common.py                # Common exceptions
│   ├── logging/
│   │   ├── __init__.py
│   │   └── config.py                # Shared logging config
│   ├── utils/
│   │   ├── __init__.py
│   │   └── validators.py            # Common validators
│   └── database/
│       ├── __init__.py
│       ├── dynamodb.py              # DynamoDB connection
│       └── models.py                # Base models
├── mcp/
│   ├── onboarding_mcp/
│   ├── activity_mcp/
│   ├── chat_mcp/
│   └── orchestrator/
└── (monolith remains for now)
```

---

## Migration Path & Backward Compatibility

### Strategy: Parallel Deployment

```
Phase 1: Deploy alongside monolith
├── Monolith still handles all requests
├── MCPs running independently
├── No production traffic to MCPs yet

Phase 2: Route selected users to MCPs
├── Use feature flags for gradual traffic shift
├── Monolith still fallback for errors
├── Monitor metrics and logs

Phase 3: Deprecate monolith
├── All traffic on MCPs + Orchestrator
├── Monolith in standby only
├── Can be decommissioned after stability period
```

### Monolith Compatibility Layer

To maintain backward compatibility, the monolith can act as a compatibility layer:

**File: `app/api/routes_compat.py`** (NEW)

```python
"""
Compatibility layer that routes to MCPs internally.
Allows gradual migration without client changes.
"""

from fastapi import APIRouter
import httpx

router = APIRouter()

@router.post("/onboarding")
async def onboarding_compat(msg: MessageRequest):
    """Route to OnboardingMCP."""
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8001/mcp/onboarding/message",
            json={
                "session_id": session_id,
                "message": msg.message,
                "detected_intent": "general",
                "context": {}
            }
        )
        return response.json()
```

This allows:
1. Existing frontend code works without changes
2. Monolith acts as proxy to MCPs
3. Can gradually migrate to direct MCPs communication

---

## Deployment & Testing Strategy

### Docker Compose for Local Development

**File: `docker-compose.yml`**

```yaml
version: '3.8'

services:
  onboarding-mcp:
    build:
      context: .
      dockerfile: my_onboarding_api/mcp/onboarding_mcp/Dockerfile
    ports:
      - "8001:8000"
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - AUTH_API_URL=${AUTH_API_URL}
    depends_on:
      - dynamodb-local

  activity-mcp:
    build:
      context: .
      dockerfile: my_onboarding_api/mcp/activity_mcp/Dockerfile
    ports:
      - "8002:8000"
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
    depends_on:
      - dynamodb-local

  chat-mcp:
    build:
      context: .
      dockerfile: my_onboarding_api/mcp/chat_mcp/Dockerfile
    ports:
      - "8003:8000"
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - GEMINI_API_KEY=${GEMINI_API_KEY}

  orchestrator:
    build:
      context: .
      dockerfile: my_onboarding_api/orchestrator/Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - ONBOARDING_MCP_URL=http://onboarding-mcp:8000
      - ACTIVITY_MCP_URL=http://activity-mcp:8000
      - CHAT_MCP_URL=http://chat-mcp:8000
    depends_on:
      - onboarding-mcp
      - activity-mcp
      - chat-mcp

  dynamodb-local:
    image: amazon/dynamodb-local
    ports:
      - "8005:8000"
```

### Testing Strategy

```
tests/
├── unit/
│   ├── test_onboarding_mcp/
│   │   ├── test_routes.py
│   │   ├── test_services.py
│   │   └── test_flows.py
│   ├── test_activity_mcp/
│   └── test_orchestrator/
├── integration/
│   ├── test_mcp_communication.py
│   ├── test_flow_scenarios.py
│   └── test_orchestrator_routing.py
├── e2e/
│   ├── test_signup_flow.py
│   ├── test_login_flow.py
│   └── test_activity_workflow.py
└── performance/
    ├── test_load.py
    └── test_latency.py
```

### Validation Checklist

- [ ] OnboardingMCP: All auth flows work (signup, login)
- [ ] ActivityMCP: CRUD operations functional
- [ ] ChatMCP: Gemini integration working
- [ ] Orchestrator: Correct MCP routing
- [ ] Session management: Consistent across MCPs
- [ ] Error handling: Proper error responses
- [ ] Performance: <200ms average latency
- [ ] Load testing: 100+ concurrent users
- [ ] Security: Input validation, rate limiting

---

## Summary of Deliverables

### Phase 1 Deliverables
1. ✅ OnboardingMCP service with full signup/login flows
2. ✅ ActivityMCP service with CRUD and classification
3. ✅ ChatMCP service with Gemini integration
4. ✅ Comprehensive test suite for all MCPs
5. ✅ Docker configurations for each MCP
6. ✅ API documentation (OpenAPI/Swagger)

### Phase 2 Deliverables
1. ✅ ConversationOrchestrator gateway
2. ✅ Intelligent message router
3. ✅ Intent detection service
4. ✅ Central session management
5. ✅ MCP health checking
6. ✅ Integration tests and documentation

### Additional Resources

- **Frontend Integration Guide:** See `FRONTEND_INTEGRATION.md`
- **MCP Protocol Specification:** See `MCP_PROTOCOL_SPEC.md`
- **Troubleshooting Guide:** See `TROUBLESHOOTING.md`
- **Deployment Guide:** See `DEPLOYMENT.md`

---

## Next Steps

1. **Review this document** with the team
2. **Identify blockers** and constraints
3. **Start Phase 1, Week 1** with OnboardingMCP extraction
4. **Set up CI/CD** for MCP deployment
5. **Plan frontend integration** with Next.js
6. **Establish monitoring** for all services

---

## Appendix: Configuration Template

### OnboardingMCP Config (`mcp/onboarding_mcp/config.py`)

```python
from pydantic_settings import BaseSettings

class OnboardingMCPSettings(BaseSettings):
    # Server
    host: str = "0.0.0.0"
    port: int = 8001
    debug: bool = False
    
    # External APIs
    auth_api_url: str
    auth_api_timeout: int = 10
    
    # Database
    dynamodb_region: str = "us-east-1"
    dynamodb_table_sessions: str = "onboarding_sessions"
    
    # Logging
    log_level: str = "INFO"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = OnboardingMCPSettings()
```

---

**Document prepared for:** MyVillage AI Project  
**For questions or clarifications:** See team wiki or contact architecture lead
