# Setup Guide for <PERSON>

Follow these simple steps to connect your Activities Service to <PERSON>.

## 1. Install Requirements

Since you have multiple Python versions, we need to make sure we install the packages for the Python version <PERSON> is using (`Python 3.13`).

Open your terminal in the `mcp-activities` folder and run this exact command:

```powershell
& "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" -m pip install -r requirements.txt
```

*Note: This ensures the `mcp` library is installed where <PERSON> can find it.*

## 2. Configure Claude Desktop

1.  Open the config file:
    *   **Windows**: Press `Win+R`, type `%APPDATA%\Claude\claude_desktop_config.json`, and press Enter.
    *   **Mac**: Open `~/Library/Application Support/Claude/claude_desktop_config.json`.

2.  Paste this code. **IMPORTANT**: Replace the AWS credentials and table names with your actual values from your `.env` file:

```json
{
  "mcpServers": {
    "myvillage-activities": {
      "command": "python",
      "args": [
        "d:/Projects/myvillage/Git_Repo/myvillageai/myvillage-ai-monorepo/mcp-activities/mcp_server.py"
      ],
      "cwd": "d:/Projects/myvillage/Git_Repo/myvillageai/myvillage-ai-monorepo",
      "env": {
        "PYTHONPATH": "d:/Projects/myvillage/Git_Repo/myvillageai/myvillage-ai-monorepo/mcp-activities;d:/Projects/myvillage/Git_Repo/myvillageai/myvillage-ai-monorepo/common",
        "AWS_REGION": "us-east-1",
        "AWS_ACCESS_KEY_ID": "YOUR_AWS_ACCESS_KEY",
        "AWS_SECRET_ACCESS_KEY": "YOUR_AWS_SECRET_KEY",
        "DYNAMODB_ACTIVITIES_TABLE": "YOUR_ACTIVITIES_TABLE_NAME",
        "DYNAMODB_SUBMISSIONS_TABLE": "YOUR_SUBMISSIONS_TABLE_NAME"
      }
    },
    "myvillage-onboarding": {
      "command": "python",
      "args": [
        "d:/Projects/myvillage/Git_Repo/myvillageai/myvillage-ai-monorepo/mcp-onboarding/mcp_server.py"
      ],
      "cwd": "d:/Projects/myvillage/Git_Repo/myvillageai/myvillage-ai-monorepo",
      "env": {
        "PYTHONPATH": "d:/Projects/myvillage/Git_Repo/myvillageai/myvillage-ai-monorepo/mcp-onboarding;d:/Projects/myvillage/Git_Repo/myvillageai/myvillage-ai-monorepo/common",
        "AWS_REGION": "us-east-1",
        "AUTH_API_URL": "https://6s0bso8sri.execute-api.us-east-1.amazonaws.com/amplifydev/setAuthToken",
        "SIGNUP_API_URL": "https://6s0bso8sri.execute-api.us-east-1.amazonaws.com/amplifydev/userCreatev2",
        "JWT_SECRET": "YOUR_JWT_SECRET"
      }
    }
  }
}
```

**To find your values:**
1. Open your `.env` file in the `myvillage-ai-monorepo` folder
2. Copy the values for:
   - `AWS_ACCESS_KEY_ID`
   - `AWS_SECRET_ACCESS_KEY`
   - `DYNAMODB_ACTIVITIES_TABLE`
   - `DYNAMODB_SUBMISSIONS_TABLE`
   - `JWT_SECRET`
3. Replace the `YOUR_*` placeholders above with your actual values

*Note: This adds both Activities (for listing activities) and Onboarding (for login/signup) services.*

## 3. Restart Claude

Completely close and restart Claude Desktop. You should see a plug icon 🔌 indicating it's connected.

## FAQ

*   **Do I need to run any script?**
    No. Claude Desktop runs the server automatically using the config above.
*   **What about `start-all.sh`?**
    That script is for running the web API locally. You don't need it for Claude Desktop, but you can run it if you want the web API running too.
*   **Why are no activities showing?**
    - Check that your `.env` file has the correct AWS credentials
    - Verify the DynamoDB table name is correct (`DYNAMODB_ACTIVITIES_TABLE=activities`)
    - Make sure there's actual data in your DynamoDB `activities` table
    - The table name in your `.env` should match the actual table name in AWS
