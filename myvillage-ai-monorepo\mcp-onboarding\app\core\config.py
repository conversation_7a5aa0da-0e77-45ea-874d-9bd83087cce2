"""Configuration for Onboarding MCP."""
from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    """Application settings."""
    
    # Service
    service_name: str = "onboarding-mcp"
    service_version: str = "1.0.0"
    port: int = 8001
    
    # Database
    database_url: str = os.getenv("DATABASE_URL", "")
    aws_region: str = os.getenv("AWS_REGION", "us-east-1")
    
    # External Auth APIs (same as my_onboarding_api)
    auth_api_url: str = os.getenv(
        "AUTH_API_URL",
        "https://6s0bso8sri.execute-api.us-east-1.amazonaws.com/amplifydev/setAuthToken"
    )
    signup_api_url: str = os.getenv(
        "SIGNUP_API_URL",
        "https://6s0bso8sri.execute-api.us-east-1.amazonaws.com/amplifydev/userCreatev2"
    )
    auth_api_timeout: int = int(os.getenv("AUTH_API_TIMEOUT", "10"))
    api_bearer_token: str = os.getenv("API_BEARER_TOKEN", "")
    
    # Security
    jwt_secret: str = os.getenv("JWT_SECRET", "change-this-secret-key")
    jwt_algorithm: str = "HS256"
    jwt_expiration: int = 3600  # 1 hour
    
    # Logging
    log_level: str = "INFO"
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"  # Ignore extra fields from .env


settings = Settings()
