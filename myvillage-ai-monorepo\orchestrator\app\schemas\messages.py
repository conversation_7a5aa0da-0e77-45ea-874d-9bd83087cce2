"""Message schemas."""
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any


class ChatRequest(BaseModel):
    """Chat request model."""
    user_id: str = Field(..., description="User ID")
    text: str = Field(..., min_length=1, description="User message")
    chat_type: Optional[str] = Field(None, description="Chat type or conversation ID")
    session_id: Optional[str] = Field(None, description="Session ID")


class ChatResponse(BaseModel):
    """Chat response model."""
    success: bool
    message: str
    response: Optional[str] = None  # Alias for message for frontend compatibility
    intent: str
    routed_to: str
    mcp_service: Optional[str] = None  # Alias for routed_to
    data: Optional[Dict[str, Any]] = None
    
    def __init__(self, **data):
        super().__init__(**data)
        # Set response as alias for message
        if not self.response:
            self.response = self.message
        # Set mcp_service as alias for routed_to
        if not self.mcp_service:
            self.mcp_service = self.routed_to
