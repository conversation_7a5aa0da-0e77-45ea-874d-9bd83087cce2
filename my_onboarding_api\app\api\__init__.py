"""
API package initialization.

This module initializes the FastAPI application and includes all API routers.
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.core.config import settings
from app.api.routes import router as api_router
from app.api.endpoints.activities import router as activities_router
from app.api.endpoints.submissions import router as submissions_router

def get_application() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        FastAPI: Configured FastAPI application instance
    """
    # Create FastAPI app
    app = FastAPI(
        title=settings.app_name,
        description="MyVillage Onboarding API with AI-powered chat and activity management",
        version=settings.app_version,
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url=f"{settings.API_V1_STR}/openapi.json"
    )
    
    # Set up CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=settings.cors_allow_credentials,
        allow_methods=settings.cors_allow_methods,
        allow_headers=settings.cors_allow_headers,
    )
    
    # Include routers
    app.include_router(api_router, prefix=settings.API_V1_STR)
    app.include_router(activities_router, prefix=f"{settings.API_V1_STR}/activities", tags=["activities"])
    app.include_router(submissions_router, prefix=f"{settings.API_V1_STR}/submissions", tags=["submissions"])
    
    return app

# Create the FastAPI application
app = get_application()

# Models are now defined in app/models/ and imported as needed by the endpoints
