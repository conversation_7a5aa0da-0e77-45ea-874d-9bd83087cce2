# Onboarding Chat Page

## Overview

A dedicated chat interface that connects to the **MVP Conversation Orchestrator** service for intelligent conversation routing and intent detection.

## Location

**URL:** `/onboarding-chat`

**File:** `src/app/onboarding-chat/page.tsx`

## Features

### 1. Real-time Chat Interface
- Clean, modern chat UI with message bubbles
- User messages on the right (blue)
- Assistant messages on the left (white/gray)
- Error messages highlighted in red
- Flow messages highlighted in green

### 2. Orchestrator Integration
- Connects to `/api/v1/chat-orchestrated` endpoint
- Sends messages with session ID for context
- Handles different response types:
  - **Flow responses** (signup/login)
  - **General responses** (activities, help)
  - **Error responses**

### 3. Session Management
- Unique session ID generated per page load
- Session ID displayed in header
- Session ID sent with every request for context

### 4. Quick Actions
Pre-filled message buttons for common actions:
- "Sign up"
- "Log in"
- "View activities"
- "Help"

### 5. Visual Feedback
- Loading spinner while waiting for response
- Error banner for connection issues
- Timestamp on each message
- Flow metadata display (flow type and step)

### 6. Dark Mode Support
- Fully styled for both light and dark themes
- Smooth transitions between themes

## Configuration

### Environment Variables

Add to your `.env.local` file:

```env
NEXT_PUBLIC_API_URL=http://localhost:8000
```

### Backend Requirements

The page requires:
1. **Backend API** running on port 8000 (or configured URL)
2. **Orchestrator service** running on port 8100
3. `/api/v1/chat-orchestrated` endpoint available

## Usage

### Start the Services

1. **Start Orchestrator:**
   ```bash
   cd mvp-conversation-orchestrator
   python -m app.main
   ```

2. **Start Backend:**
   ```bash
   cd my_onboarding_api
   python main.py
   ```

3. **Start Frontend:**
   ```bash
   cd gemini-interface
   npm run dev
   ```

4. **Open Browser:**
   ```
   http://localhost:3000/onboarding-chat
   ```

## Testing

### Test Signup Flow

1. Type: "I want to sign up"
2. Expected response: "Great! Let's get you signed up. What's your name?"
3. Flow indicator shows: `Flow: signup • Step: name`

### Test Login Flow

1. Type: "I need to log in"
2. Expected response: "Welcome back! Please enter your email to log in."
3. Flow indicator shows: `Flow: login • Step: email`

### Test Activity Query

1. Type: "Show me activities"
2. Expected response: List of activities
3. No flow indicator (general response)

### Test General Query

1. Type: "What can you help me with?"
2. Expected response: General help message
3. No flow indicator (general response)

## API Request Format

```typescript
POST /api/v1/chat-orchestrated
Headers:
  Content-Type: application/json
  session_id: session-1234567890

Body:
{
  "message": "I want to sign up"
}
```

## API Response Formats

### Flow Response (Signup/Login)

```json
{
  "message": "Great! Let's get you signed up. What's your name?",
  "flow_step": "name",
  "flow_type": "signup"
}
```

### General Response (Activity/Help)

```json
{
  "success": true,
  "input": "Show me activities",
  "detected_intent": "activity",
  "gemini_response": "Here are some activities available...",
  "message": "Response from activity_mcp: Here are some activities..."
}
```

### Error Response

```json
{
  "detail": {
    "error": "Orchestrator unavailable",
    "message": "External orchestrator service is not available..."
  }
}
```

## Error Handling

### Orchestrator Unavailable

**Error Message:**
```
Error: Orchestrator service unavailable at http://localhost:8100/chat. 
Please ensure the orchestrator is running.
```

**Solution:**
1. Check if orchestrator is running: `curl http://localhost:8100/health`
2. Start orchestrator if not running
3. Verify `NEXT_PUBLIC_API_URL` in `.env.local`

### Backend Unavailable

**Error Message:**
```
Error: Failed to send message. Please make sure the backend and 
orchestrator services are running.
```

**Solution:**
1. Check if backend is running: `curl http://localhost:8000/api/v1/health`
2. Start backend if not running
3. Verify backend can reach orchestrator

### Network Errors

**Error Message:**
```
Error: Network request failed
```

**Solution:**
1. Check browser console for CORS errors
2. Verify `NEXT_PUBLIC_API_URL` is correct
3. Check firewall/network settings

## Customization

### Change Colors

Edit the Tailwind classes in `page.tsx`:

```typescript
// User messages
className="bg-blue-600 text-white"

// Assistant messages
className="bg-white dark:bg-gray-800 text-gray-900 dark:text-white"

// Error messages
className="bg-red-100 dark:bg-red-900/30 text-red-900 dark:text-red-200"

// Flow messages
className="bg-green-50 dark:bg-green-900/20 text-green-900 dark:text-green-200"
```

### Add More Quick Actions

Add buttons in the quick actions section:

```typescript
<button
  onClick={() => setInputValue('Your custom message')}
  disabled={isLoading}
  className="text-xs px-3 py-1.5 rounded-full bg-gray-100 dark:bg-gray-700..."
>
  Custom Action
</button>
```

### Change API Endpoint

Update the fetch URL in `sendMessage()`:

```typescript
const response = await fetch(`${apiUrl}/api/v1/your-custom-endpoint`, {
  // ...
});
```

## Architecture

```
Frontend (Next.js)
    ↓
/onboarding-chat page
    ↓
POST /api/v1/chat-orchestrated
    ↓
Backend (FastAPI)
    ↓
Orchestrator Client
    ↓
MVP Conversation Orchestrator
    ↓
[OnboardingMCP | ActivityMCP | ChatMCP]
    ↓
Response back to frontend
```

## Component Structure

```typescript
OnboardingChatPage
├── Header
│   ├── Title
│   ├── Description
│   └── Session ID
├── Error Banner (conditional)
├── Messages Container
│   ├── Message Bubbles
│   │   ├── User Messages
│   │   ├── Assistant Messages
│   │   ├── Flow Messages
│   │   └── Error Messages
│   └── Loading Indicator
└── Input Area
    ├── Text Input
    ├── Send Button
    └── Quick Actions
```

## State Management

```typescript
// Messages array
const [messages, setMessages] = useState<Message[]>([...]);

// Input value
const [inputValue, setInputValue] = useState('');

// Loading state
const [isLoading, setIsLoading] = useState(false);

// Session ID (generated once)
const [sessionId] = useState(() => `session-${Date.now()}`);

// Error state
const [error, setError] = useState<string | null>(null);
```

## TypeScript Types

```typescript
interface Message {
  id: string;
  content: string;
  sender: 'user' | 'assistant';
  timestamp: Date;
  type?: 'text' | 'flow' | 'error';
  flowData?: {
    flow_type?: string;
    flow_step?: string;
  };
}
```

## Accessibility

- Keyboard navigation (Enter to send)
- Screen reader friendly
- Focus management
- ARIA labels on interactive elements
- High contrast colors for readability

## Performance

- Auto-scroll to latest message
- Smooth animations
- Optimized re-renders
- Lazy loading for long conversations

## Future Enhancements

- [ ] Message persistence (localStorage)
- [ ] Conversation history
- [ ] File upload support
- [ ] Voice input
- [ ] Typing indicators
- [ ] Read receipts
- [ ] Message reactions
- [ ] Export conversation
- [ ] Multi-language support
- [ ] Rich text formatting

## Related Documentation

- `my_onboarding_api/ORCHESTRATOR_INTEGRATION.md` - Backend integration
- `my_onboarding_api/QUICK_START_ORCHESTRATOR.md` - Quick start guide
- `mvp-conversation-orchestrator/README.md` - Orchestrator docs

---

**Status:** ✅ Ready to Use
**Version:** 1.0.0
**Created:** November 13, 2025
