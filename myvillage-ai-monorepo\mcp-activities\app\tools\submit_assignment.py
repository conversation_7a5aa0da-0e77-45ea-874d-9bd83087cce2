"""Tool: Submit Assignment."""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "common"))

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import Optional
import logging

from common.models.base import BaseResponse
from ..services.submission_service import SubmissionService
from ..models.submission import SubmissionCreate

logger = logging.getLogger(__name__)
router = APIRouter()
submission_service = SubmissionService()


class SubmitAssignmentRequest(BaseModel):
    """Submit assignment request model."""
    activity_id: str = Field(..., description="Activity ID")
    user_id: str = Field(..., description="User ID")
    title: str = Field(..., min_length=1, max_length=200, description="Submission title")
    description: Optional[str] = Field(None, description="Submission description")
    content: Optional[str] = Field(None, description="Submission content")
    is_public: bool = Field(True, description="Whether submission is public")


@router.post("/submit_assignment", response_model=BaseResponse)
async def submit_assignment(request: SubmitAssignmentRequest):
    """
    Submit an assignment for an activity.
    
    This tool creates a submission for a specific activity.
    The submission is automatically marked as submitted.
    
    Args:
        request: Submission request
        
    Returns:
        BaseResponse with submission data
        
    Raises:
        HTTPException: If submission fails
    """
    try:
        logger.info(f"Submitting assignment for activity: {request.activity_id} by user: {request.user_id}")
        
        # Create submission
        submission_create = SubmissionCreate(
            activity_id=request.activity_id,
            user_id=request.user_id,
            title=request.title,
            description=request.description,
            content=request.content,
            is_public=request.is_public
        )
        
        submission = await submission_service.create_submission(submission_create)
        
        logger.info(f"Assignment submitted successfully: {submission.id}")
        
        return BaseResponse(
            success=True,
            message="Assignment submitted successfully",
            data={
                "submission_id": submission.id,
                "activity_id": submission.activity_id,
                "user_id": submission.user_id,
                "title": submission.title,
                "status": submission.status.value,
                "submitted_at": submission.submitted_at.isoformat() if submission.submitted_at else None
            }
        )
    except ValueError as e:
        logger.warning(f"Submission failed: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error submitting assignment: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to submit assignment: {str(e)}"
        )
