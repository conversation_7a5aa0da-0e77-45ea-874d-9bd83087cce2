# ⚡ Quick Test Guide - Conversational Flows

## 🚀 Start Everything

```bash
# Terminal 1: Backend
cd myvillage-ai-monorepo
./restart-all.sh

# Terminal 2: Frontend
cd myvillage-chat-ui
npm run dev
```

Open: **http://localhost:3000**

---

## 🧪 Test Scenarios

### ✅ Test 1: Complete Signup Flow

```
You: "I want to sign up"
Bot: "Great! Let's get you signed up. What's your full name?"

You: "<PERSON>"
Bot: "Thanks <PERSON>! What's your email address?"

You: "<EMAIL>"
Bot: "Perfect! Now create a password (minimum 8 characters):"

You: "password123"
Bot: "Almost done! What's your phone number? (optional, press skip to continue)"

You: "skip"
Bot: "🎉 Welcome John <PERSON>! Your account has been created successfully!"
```

---

### ✅ Test 2: Complete Login Flow

```
You: "I want to log in"
Bot: "Welcome back! What's your email address?"

You: "<EMAIL>"
Bot: "Great! Now enter your password:"

You: "password123"
Bot: "✅ Welcome back! You're now logged in."
```

---

### ✅ Test 3: Cancel Flow

```
You: "sign up"
Bot: "What's your full name?"

You: "cancel"
Bot: "Conversation cancelled. How else can I help you?"
```

---

### ✅ Test 4: Invalid Input

```
You: "sign up"
Bot: "What's your full name?"

You: "A"
Bot: "Invalid name. What's your full name?"

You: "John Doe"
Bot: "Thanks <PERSON> Doe! What's your email address?"

You: "notanemail"
Bot: "Invalid email. What's your email address?"

You: "<EMAIL>"
Bot: "Perfect! Now create a password (minimum 8 characters):"
```

---

### ✅ Test 5: Create Activity Flow

```
You: "Create an activity"
Bot: "Let's create an activity! What's the title?"

You: "Math Quiz"
Bot: "Great title! Now provide a description:"

You: "A quiz about algebra"
Bot: "What type of activity is this? (assignment, quiz, project, discussion)"

You: "quiz"
Bot: "✅ Activity 'Math Quiz' created successfully!"
```

---

### ✅ Test 6: Check Rewards

```
You: "Check my rewards"
Bot: "🎁 You have 0 reward points available!"
```

---

### ✅ Test 7: List Activities

```
You: "Show me activities"
Bot: "Here are the available activities:"
[Shows list of activities]
```

---

## 🎯 What to Look For

### ✅ Success Indicators
- Bot asks ONE question at a time
- Each answer moves to next step
- Validation errors show helpful messages
- Completion shows success message
- Cancel works at any step

### ❌ Issues to Watch
- Bot asks multiple questions at once
- No validation on inputs
- Can't cancel mid-flow
- Session expires too quickly
- Errors not handled gracefully

---

## 📊 Check Logs

```bash
# Watch orchestrator logs
tail -f myvillage-ai-monorepo/logs/orchestrator.log

# Look for:
# - "Started signup flow for user..."
# - "Completed signup flow for user..."
# - "Cancelled signup flow for user..."
```

---

## 🐛 Quick Fixes

### Backend not responding
```bash
cd myvillage-ai-monorepo
./stop-all.sh
./start-all.sh
```

### Frontend not updating
```bash
# Clear browser cache
# Hard refresh: Ctrl+Shift+R (Windows) or Cmd+Shift+R (Mac)
```

### Port conflicts
```bash
# Check ports
netstat -ano | findstr :8100
netstat -ano | findstr :3000

# Kill if needed
taskkill /PID <PID> /F
```

---

## ✨ Expected Behavior

1. **One Question at a Time** ✅
2. **Clear Prompts** ✅
3. **Input Validation** ✅
4. **Optional Fields** ✅
5. **Cancel Anytime** ✅
6. **Success Messages** ✅
7. **Error Handling** ✅

---

## 🎉 Success!

If all tests pass, your conversational AI is working perfectly!

**Enjoy the step-by-step experience!** 🚀
