"use client";

import { useState, useEffect } from "react";
import {
  ChevronLeft,
  ChevronRight,
  Lightbulb,
  BookOpen,
  TreePine,
  ArrowRight,
  BarChart3,
  Target,
  TrendingUp,
  CheckCircle,
  Coins,
  Activity,
  Clock,
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

export default function Sidebar() {
  const { isAuthenticated, user } = useAuth();
  const [isOpen, setIsOpen] = useState(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("sidebarOpen");
      return saved !== null ? JSON.parse(saved) : true;
    }
    return true;
  });

  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("sidebarOpen", JSON.stringify(isOpen));
    }
  }, [isOpen]);

  // Hide sidebar completely when not authenticated
  if (!isAuthenticated) {
    return null;
  }

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed left-0 top-1/2 -translate-y-1/2 bg-primary text-primary-foreground p-2 rounded-r-lg z-10 shadow-lg hover:bg-primary-dark transition-colors"
      >
        <ChevronRight className="w-5 h-5" />
      </button>
    );
  }

  return (
    <div className="w-80 h-full flex flex-col bg-card border-r border-border">
      {/* Header */}
      <div className="p-4 border-b border-border flex justify-between items-center">
        <h2 className="text-lg font-semibold text-foreground">My Village OS</h2>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsOpen(false)}
            className="text-muted-foreground hover:text-foreground p-1 rounded hover:bg-muted"
            title="Collapse sidebar"
          >
            <ChevronLeft className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Sidebar Content with Scrollbar */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 scrollbar-thin scrollbar-thumb-neutral-300 scrollbar-track-neutral-100">
        {/* User Stats Card - Only show when authenticated */}
        {isAuthenticated && user && (
          <div className="bg-gradient-to-br from-primary-light to-secondary-light rounded-3xl p-5 border border-primary">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                <Activity className="w-3 h-3 text-primary-foreground" />
              </div>
              <h2 className="text-lg font-semibold text-foreground">
                Your Stats
              </h2>
            </div>

            <div className="space-y-3">
              {/* MVP Tokens */}
              <div className="bg-card rounded-2xl p-3 shadow-sm border border-border">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Coins className="w-4 h-4 text-warning" />
                    <span className="text-sm font-medium text-foreground">MVP Tokens</span>
                  </div>
                  <span className="text-lg font-bold text-warning">{user.mvpPoints || 250}</span>
                </div>
              </div>

              {/* Impact Score */}
              <div className="bg-card rounded-2xl p-3 shadow-sm border border-border">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="w-4 h-4 text-success" />
                    <span className="text-sm font-medium text-foreground">Impact Score</span>
                  </div>
                  <span className="text-lg font-bold text-success">{user.impactScore || 87}%</span>
                </div>
              </div>

              {/* Activities Completed */}
              <div className="bg-card rounded-2xl p-3 shadow-sm border border-border">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-primary" />
                    <span className="text-sm font-medium text-foreground">Completed</span>
                  </div>
                  <span className="text-lg font-bold text-primary">
                    {user.activitiesCompleted || user.activities?.filter((a: any) => a.status === 'completed').length || 0}
                  </span>
                </div>
              </div>

              {/* Pending Activities */}
              <div className="bg-card rounded-2xl p-3 shadow-sm border border-border">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-warning" />
                    <span className="text-sm font-medium text-foreground">Pending</span>
                  </div>
                  <span className="text-lg font-bold text-warning">
                    {user.pendingActivities || user.activities?.filter((a: any) => a.status === 'pending' || a.status === 'in_progress').length || 0}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* User Activities Card - Show activities from API */}
        {isAuthenticated && user && user.activities && user.activities.length > 0 && (
          <div className="bg-gradient-to-br from-success-light to-primary-light rounded-3xl p-5 border border-success">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-6 h-6 bg-success rounded-full flex items-center justify-center">
                <CheckCircle className="w-3 h-3 text-success-foreground" />
              </div>
              <h2 className="text-lg font-semibold text-foreground">
                Your Activities
              </h2>
            </div>

            <div className="space-y-3 max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-neutral-300 scrollbar-track-neutral-100">
              {user.activities.map((activity: any, index: number) => (
                <div key={activity.id || index} className="bg-card rounded-2xl p-4 shadow-sm border border-border hover:shadow-md transition-shadow">
                  <div className="flex items-start gap-3">
                    <div className={`w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 ${
                      activity.status === 'completed' ? 'bg-success-light' : 
                      activity.status === 'in_progress' ? 'bg-warning-light' : 
                      'bg-primary-light'
                    }`}>
                      {activity.status === 'completed' ? (
                        <CheckCircle className="w-4 h-4 text-success" />
                      ) : activity.status === 'in_progress' ? (
                        <Clock className="w-4 h-4 text-warning" />
                      ) : (
                        <BookOpen className="w-4 h-4 text-primary" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-semibold text-foreground mb-1">
                        {activity.title}
                      </h3>
                      {activity.type && (
                        <p className="text-xs text-muted-foreground mb-1">
                          Type: {activity.type}
                        </p>
                      )}
                      {activity.date && (
                        <p className="text-xs text-muted-foreground mb-1">
                          📅 {activity.date}
                        </p>
                      )}
                      {activity.location && (
                        <p className="text-xs text-muted-foreground mb-1">
                          📍 {activity.location}
                        </p>
                      )}
                      {activity.organizer && (
                        <p className="text-xs text-muted-foreground mb-1">
                          👤 {activity.organizer}
                        </p>
                      )}
                      {activity.description && (
                        <p className="text-xs text-muted-foreground mt-2">
                          {activity.description}
                        </p>
                      )}
                      {activity.status && (
                        <span className={`inline-block mt-2 text-xs px-2 py-0.5 rounded-full ${
                          activity.status === 'completed' ? 'bg-success-light text-success' :
                          activity.status === 'in_progress' ? 'bg-warning-light text-warning' :
                          'bg-primary-light text-primary'
                        }`}>
                          {activity.status.replace('_', ' ')}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Activity Suggestions Card */}
        <div className="bg-primary-light rounded-3xl p-5 border border-primary">
          {/* Header with lightbulb icon */}
          <div className="flex items-center gap-2 mb-4">
            <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
              <Lightbulb className="w-3 h-3 text-primary-foreground" />
            </div>
            <h2 className="text-lg font-semibold text-foreground">
              Activity Suggestions
            </h2>
          </div>

          {/* Activity Cards */}
          <div className="space-y-3">
            {/* Weekend Book Drive */}
            <div className="bg-card rounded-2xl p-4 shadow-sm border border-border hover:shadow-md transition-shadow cursor-pointer">
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-warning-light rounded-xl flex items-center justify-center flex-shrink-0">
                  <BookOpen className="w-4 h-4 text-warning" />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-semibold text-foreground mb-1">
                    📚 Weekend Book Drive
                  </h3>
                  <p className="text-muted-foreground text-xs mb-2">
                    Based on your tutoring history
                  </p>
                  <button className="text-primary text-xs font-medium flex items-center gap-1 hover:text-primary-dark transition-colors">
                    Learn more
                    <ArrowRight className="w-3 h-3" />
                  </button>
                </div>
              </div>
            </div>

            {/* Tree Plantation Event */}
            <div className="bg-card rounded-2xl p-4 shadow-sm border border-border hover:shadow-md transition-shadow cursor-pointer">
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-success-light rounded-xl flex items-center justify-center flex-shrink-0">
                  <TreePine className="w-4 h-4 text-success" />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-semibold text-foreground mb-1">
                    🌱 Tree Plantation Event
                  </h3>
                  <p className="text-muted-foreground text-xs mb-2">
                    High impact opportunity nearby
                  </p>
                  <button className="text-primary text-xs font-medium flex items-center gap-1 hover:text-primary-dark transition-colors">
                    Learn more
                    <ArrowRight className="w-3 h-3" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Performance Analytics Card */}
        <div className="bg-warning-light rounded-3xl p-5 border border-warning">
          {/* Header with chart icon */}
          <div className="flex items-center gap-2 mb-4">
            <div className="w-6 h-6 bg-warning rounded-full flex items-center justify-center">
              <BarChart3 className="w-3 h-3 text-warning-foreground" />
            </div>
            <h2 className="text-lg font-semibold text-foreground">
              📊 Performance Analytics
            </h2>
          </div>

          {/* This Week Section */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-muted-foreground">This Week</span>
              <span className="text-sm font-semibold text-warning">+15%</span>
            </div>
            
            <div className="mb-3">
              <div className="flex justify-between items-center mb-1">
                <span className="text-xs text-muted-foreground">Activities</span>
                <span className="text-xs text-muted-foreground">5/7</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div className="bg-warning h-2 rounded-full" style={{ width: '71%' }}></div>
              </div>
            </div>

            <button className="w-full text-warning text-sm font-medium hover:text-warning-dark transition-colors">
              View Full Report
            </button>
          </div>
        </div>

        {/* Goal Progress Card */}
        <div className="bg-accent-light rounded-3xl p-5 border border-accent">
          {/* Header with target icon */}
          <div className="flex items-center gap-2 mb-4">
            <div className="w-6 h-6 bg-accent rounded-full flex items-center justify-center">
              <Target className="w-3 h-3 text-accent-foreground" />
            </div>
            <h2 className="text-lg font-semibold text-foreground">
              🎯 Goal Progress
            </h2>
          </div>

          {/* Monthly Goal Section */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-muted-foreground">Monthly Goal</span>
              <span className="text-sm font-semibold text-accent">75%</span>
            </div>
            
            <div className="w-full bg-muted rounded-full h-2 mb-2">
              <div className="bg-accent h-2 rounded-full" style={{ width: '75%' }}></div>
            </div>
            
            <p className="text-xs text-muted-foreground mb-4">
              12 of 16 activities completed
            </p>

            <button className="w-full bg-accent text-accent-foreground text-sm font-medium py-2 px-4 rounded-2xl hover:bg-accent-dark transition-colors">
              Set New Goal
            </button>
          </div>
        </div>

        {/* Impact Predictions Card */}
        <div className="bg-success-light rounded-3xl p-5 border border-success">
          {/* Header with trending up icon */}
          <div className="flex items-center gap-2 mb-4">
            <div className="w-6 h-6 bg-success rounded-full flex items-center justify-center">
              <TrendingUp className="w-3 h-3 text-success-foreground" />
            </div>
            <h2 className="text-lg font-semibold text-foreground">
              📈 Impact Predictions
            </h2>
          </div>

          {/* Next Activity Section */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-muted-foreground">Next Activity</span>
              <span className="text-sm font-semibold text-success">+5%</span>
            </div>
            
            <div className="w-full bg-muted rounded-full h-2 mb-3">
              <div className="bg-success h-2 rounded-full" style={{ width: '87%' }}></div>
            </div>
            
            <p className="text-xs text-muted-foreground mb-2">
              Complete 1 more activity to reach 92% impact score
            </p>

            <div className="mt-4 p-3 bg-card rounded-2xl border border-border">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-4 h-4 bg-destructive-light rounded-full flex items-center justify-center">
                  <Target className="w-2 h-2 text-destructive" />
                </div>
                <span className="text-sm font-semibold text-foreground">
                  🎯 Goal: 300 tokens by month-end
                </span>
              </div>
              <p className="text-xs text-muted-foreground">
                You need 50 more tokens (2-3 activities)
              </p>
            </div>
          </div>
        </div>

        {/* Recommendations Card */}
        <div className="bg-secondary-light rounded-3xl p-5 border border-secondary mb-4">
          {/* Header with check circle icon */}
          <div className="flex items-center gap-2 mb-4">
            <div className="w-6 h-6 bg-secondary rounded-full flex items-center justify-center">
              <CheckCircle className="w-3 h-3 text-secondary-foreground" />
            </div>
            <h2 className="text-lg font-semibold text-foreground">
              🎯 Recommendations
            </h2>
          </div>

          {/* Recommendations List */}
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="w-5 h-5 bg-secondary-light rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <CheckCircle className="w-3 h-3 text-secondary" />
              </div>
              <p className="text-xs text-foreground">
                Submit activities within 24hrs for 10% bonus tokens
              </p>
            </div>

            <div className="flex items-start gap-3">
              <div className="w-5 h-5 bg-secondary-light rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <CheckCircle className="w-3 h-3 text-secondary" />
              </div>
              <p className="text-xs text-foreground">
                Join team activities for 2x impact score
              </p>
            </div>

            <div className="flex items-start gap-3">
              <div className="w-5 h-5 bg-secondary-light rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <CheckCircle className="w-3 h-3 text-secondary" />
              </div>
              <p className="text-xs text-foreground">
                Your best time: Weekends (higher completion rate)
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
