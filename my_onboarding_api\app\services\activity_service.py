import os
from typing import List, Optional, Dict, Any
from datetime import datetime
from fastapi import HTTPException, status
import boto3
from botocore.exceptions import ClientError
from transformers import pipeline, Pipeline
from boto3.dynamodb.conditions import Attr

from ..models.activity import Activity, ActivityCreate, ActivityUpdate, ActivityListResponse
from ..models.responses import IntentClassificationResult
from ..core.database import get_db
from ..core.logging import get_logger
from ..core.exceptions import IntentClassificationError, ConfigurationError
from ..core.config import settings

logger = get_logger(__name__)

class ActivityService:
    def __init__(self):
        """Initialize the activity service."""
        self.db = get_db()
        self.table_name = os.getenv('DYNAMODB_ACTIVITIES_TABLE', 'activities')
        self.table = self.db.get_table(self.table_name)
        
        # Initialize intent classification pipeline
        self._pipeline: Optional[Pipeline] = None
        self._model_name = "jalpesh088/activity-intent-model"
        self._initialize_pipeline()
    
    def _initialize_pipeline(self) -> None:
        """Initialize the Hugging Face pipeline for activity classification."""
        try:
            # Set the Hugging Face token from settings
            os.environ["HUGGINGFACEHUB_API_TOKEN"] = settings.hf_token

            logger.info(f"Initializing activity classification pipeline with model: {self._model_name}")

            # Create the pipeline
            self._pipeline = pipeline(
                "text-classification",
                model=self._model_name,
                top_k=None,  # Return all scores
                device=-1  # Use CPU
            )

            logger.info("Activity classification pipeline initialized successfully")

        except Exception as e:
            logger.warning(f"Primary activity model init failed: {str(e)}")
            # Try a single safe fallback model before failing hard.
            fallback_model = os.getenv("ACTIVITY_MODEL_FALLBACK", "distilbert-base-uncased-finetuned-sst-2-english")
            try:
                logger.info(f"Attempting fallback activity model: {fallback_model}")
                self._pipeline = pipeline(
                    "text-classification",
                    model=fallback_model,
                    top_k=None,
                    device=-1
                )
                # replace model_name to reflect actual loaded model
                self._model_name = fallback_model
                logger.info(f"Activity classification pipeline initialized successfully with fallback model: {fallback_model}")
                return
            except Exception as e2:
                logger.error(f"Fallback activity model init also failed: {str(e2)}")
                # Mirror intent_service behavior: raise ConfigurationError with details about both attempts
                raise ConfigurationError(
                    f"Failed to initialize activity classification: primary error: {str(e)}; fallback error: {str(e2)}",
                    details={"primary_model": self._model_name, "fallback_model": fallback_model}
                )
            
    def classify_intent(self, text: str) -> List[IntentClassificationResult]:
        """Classify activity-related intent of the given text."""
        logger.info("[ACTIVITY] Starting activity intent classification")

        if not self._pipeline:
            error_msg = "[ERROR] Activity classification pipeline not initialized"
            logger.error(error_msg)
            raise IntentClassificationError(error_msg)

        if not text or not text.strip():
            error_msg = "[ERROR] Input text cannot be empty"
            logger.error(error_msg)
            raise IntentClassificationError(error_msg)

        try:
            logger.info(f"[ACTIVITY] Classifying activity intent for text: {text[:100]}{'...' if len(text) > 100 else ''}")
            logger.debug(f"[ACTIVITY] Full text length: {len(text)} characters")

            # Call pipeline
            logger.debug("[ACTIVITY] Calling pipeline with text")
            raw_results = self._pipeline(text.strip())
            logger.info("[ACTIVITY] Pipeline call completed")

            logger.debug(f"[ACTIVITY] Raw pipeline output type: {type(raw_results)}")
            logger.debug(f"[ACTIVITY] Raw pipeline output: {raw_results}")

            # Handle different output formats from Hugging Face pipeline
            if isinstance(raw_results, list):
                # Handle batch-like output where first element may be a list
                if len(raw_results) > 0 and isinstance(raw_results[0], list):
                    results = raw_results[0]
                else:
                    results = raw_results
            else:
                logger.error(f"Unexpected pipeline output format: {type(raw_results)}")
                raise IntentClassificationError(f"Unexpected pipeline output format: {type(raw_results)}")

            # Validate results
            if not isinstance(results, list):
                raise IntentClassificationError(f"Expected list of results, got {type(results)}")

            intent_results: List[IntentClassificationResult] = []
            for i, result in enumerate(results):
                if not isinstance(result, dict):
                    logger.error(f"Result {i} is not a dictionary: {type(result)} - {result}")
                    raise IntentClassificationError(f"Expected dictionary result, got {type(result)}")

                if "label" not in result or "score" not in result:
                    logger.error(f"Result {i} missing required keys: {result}")
                    raise IntentClassificationError(f"Result missing required keys 'label' or 'score': {result}")

                intent_results.append(
                    IntentClassificationResult(
                        label=result["label"],
                        score=float(result["score"])
                    )
                )

            # Sort by confidence and return
            intent_results.sort(key=lambda x: x.score, reverse=True)
            logger.debug(f"Activity classification completed. Top intent: {intent_results[0].label} ({intent_results[0].score:.3f})")
            return intent_results

        except Exception as e:
            logger.error(f"Activity classification failed: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise IntentClassificationError(
                f"Failed to classify activity intent: {str(e)}",
                details={
                    "input_text": text[:100],
                    "exception_type": str(type(e)),
                    "traceback": traceback.format_exc()
                }
            )
            
    def get_top_intent(self, text: str, threshold: Optional[float] = None) -> str:
        """Get the top activity intent for the given text."""
        base_threshold = threshold or settings.confidence_threshold
        
        try:
            results = self.classify_intent(text)
            if results and results[0].score >= base_threshold:
                return results[0].label
        except Exception as e:
            logger.error(f"Error getting top activity intent: {str(e)}")
            
        return "unknown"

    async def create_activity(self, activity: ActivityCreate) -> Activity:
        """Create a new activity."""
        try:
            db_activity = DBActivity(**activity.dict())
            self.db.add(db_activity)
            self.db.commit()
            self.db.refresh(db_activity)
            return Activity.from_orm(db_activity)
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating activity: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create activity"
            )

    async def get_activities(self, activity_type: str, session_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Fetch activities matching the given activity_type.

        This is a lightweight helper used by the API routes to retrieve
        activity data for intent-handling. The implementation tries to
        match the `activity_type` against the DBActivity.type field.
        Returns a list of dict-serializable activity records.
        """
        # Log the complete request payload before making the API call
        request_payload = {
            "activity_type": activity_type,
            "session_id": session_id,
            "limit": limit,
            "table_name": self.table_name,
            "method": "DynamoDB.scan"
        }
        logger.info(f"[ACTIVITIES_FETCH] Starting activities fetch with payload: {request_payload}")

        try:
            # DynamoDB table access via self.table
            scan_kwargs: Dict[str, Any] = {"Limit": limit}

            # Temporary user id override for debugging / local testing
            temp_user_env = os.getenv("TEMP_USER_ID")
            user_id = None
            if temp_user_env:
                user_id = temp_user_env
            elif session_id == "test-session-1":
                # Per request: use this user id temporarily
                user_id = "156b88d8-9788-4daf-bc24-4c03a1f56eb8"

            filter_expr = None
            # Use the specific moduleId for fetching activities
            module_id = "240cc115-922f-478f-9bcc-50b71096c460"
            filter_expr = Attr("moduleId").eq(module_id)

            # If user_id is present, constrain to items assigned to or created by the user
            if user_id:
                user_expr = Attr("assigned_to").contains(user_id) | Attr("created_by").eq(user_id)
                filter_expr = user_expr if filter_expr is None else (filter_expr & user_expr)

            if filter_expr is not None:
                scan_kwargs["FilterExpression"] = filter_expr

            # Log the complete DynamoDB scan parameters
            logger.info(f"[ACTIVITIES_FETCH] DynamoDB scan parameters: {scan_kwargs}")
            logger.debug(f"[ACTIVITIES_FETCH] Scanning activities table '{self.table_name}' with args: {scan_kwargs}")

            # Make the DynamoDB API call
            response = self.table.scan(**scan_kwargs)

            # Log the complete response received from the activities API
            response_metadata = {
                "items_count": len(response.get("Items", [])),
                "scanned_count": response.get("ScannedCount", 0),
                "consumed_capacity": response.get("ConsumedCapacity"),
                "last_evaluated_key": response.get("LastEvaluatedKey"),
                "response_metadata": response.get("ResponseMetadata", {})
            }
            logger.info(f"[ACTIVITIES_FETCH] DynamoDB response metadata: {response_metadata}")

            items = response.get("Items", [])
            logger.debug(f"[ACTIVITIES_FETCH] Raw items retrieved from DynamoDB: {items}")

            # If there are more pages, we currently ignore them (limit applies)
            activities = []
            for item in items:
                try:
                    converted_activity = Activity(**item).dict()
                    activities.append(converted_activity)
                    logger.debug(f"[ACTIVITIES_FETCH] Successfully converted item to Activity model: {item.get('id', 'unknown_id')}")
                except Exception as conversion_error:
                    # If conversion to Pydantic model fails, include raw item
                    activities.append(item)
                    logger.warning(f"[ACTIVITIES_FETCH] Failed to convert item to Activity model, using raw item: {item.get('id', 'unknown_id')}. Error: {str(conversion_error)}")

            # Log the final response data being returned
            final_response = {
                "total_activities_returned": len(activities),
                "activity_ids": [activity.get('id', 'unknown_id') for activity in activities],
                "activity_types": [activity.get('type', 'unknown_type') for activity in activities]
            }
            logger.info(f"[ACTIVITIES_FETCH] Final response data: {final_response}")
            logger.debug(f"[ACTIVITIES_FETCH] Complete activities data being returned: {activities}")

            return activities

        except Exception as e:
            error_details = {
                "error_message": str(e),
                "error_type": type(e).__name__,
                "request_payload": request_payload
            }
            logger.error(f"[ACTIVITIES_FETCH] Error fetching activities: {error_details}")
            raise

    async def get_activity(self, activity_id: str) -> Optional[Activity]:
        """Get an activity by ID."""
        # Log the complete request payload before making the database query
        request_payload = {
            "activity_id": activity_id,
            "method": "SQL.query",
            "table": "DBActivity"
        }
        logger.info(f"[ACTIVITY_FETCH_BY_ID] Starting single activity fetch with payload: {request_payload}")

        try:
            # Log the SQL query parameters
            logger.debug(f"[ACTIVITY_FETCH_BY_ID] Executing SQL query: SELECT * FROM DBActivity WHERE id = '{activity_id}'")

            db_activity = self.db.query(DBActivity).filter(DBActivity.id == activity_id).first()

            if not db_activity:
                logger.info(f"[ACTIVITY_FETCH_BY_ID] Activity not found for ID: {activity_id}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Activity not found"
                )

            # Convert to Pydantic model
            activity_response = Activity.from_orm(db_activity)

            # Log the complete response data
            response_data = {
                "activity_id": activity_response.id,
                "activity_type": activity_response.type,
                "activity_title": getattr(activity_response, 'title', 'N/A'),
                "activity_status": getattr(activity_response, 'status', 'N/A'),
                "created_by": getattr(activity_response, 'created_by', 'N/A'),
                "created_at": getattr(activity_response, 'created_at', 'N/A')
            }
            logger.info(f"[ACTIVITY_FETCH_BY_ID] Successfully retrieved activity: {response_data}")
            logger.debug(f"[ACTIVITY_FETCH_BY_ID] Complete activity data: {activity_response.dict()}")

            return activity_response

        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            error_details = {
                "error_message": str(e),
                "error_type": type(e).__name__,
                "request_payload": request_payload
            }
            logger.error(f"[ACTIVITY_FETCH_BY_ID] Error fetching activity by ID: {error_details}")
            raise

    async def update_activity(
        self, 
        activity_id: str, 
        activity_update: ActivityUpdate,
        current_user_id: str
    ) -> Activity:
        """Update an existing activity."""
        db_activity = self.db.query(DBActivity).filter(DBActivity.id == activity_id).first()
        if not db_activity:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Activity not found"
            )
        
        # Check if user has permission to update
        if str(db_activity.created_by) != current_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to update this activity"
            )
        
        update_data = activity_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_activity, field, value)
        
        db_activity.updated_at = datetime.utcnow()
        
        try:
            self.db.commit()
            self.db.refresh(db_activity)
            return Activity.from_orm(db_activity)
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating activity: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update activity"
            )

    async def delete_activity(self, activity_id: str, current_user_id: str) -> bool:
        """Delete an activity."""
        db_activity = self.db.query(DBActivity).filter(DBActivity.id == activity_id).first()
        if not db_activity:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Activity not found"
            )
        
        # Check if user has permission to delete
        if str(db_activity.created_by) != current_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to delete this activity"
            )
        
        try:
            self.db.delete(db_activity)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error deleting activity: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete activity"
            )

    async def get_user_activities(
        self,
        user_id: str,
        status: Optional[str] = None,
        limit: int = 10,
        offset: int = 0
    ) -> ActivityListResponse:
        """Get activities assigned to a user with optional status filter."""
        # Log the complete request payload before making the database query
        request_payload = {
            "user_id": user_id,
            "status_filter": status,
            "limit": limit,
            "offset": offset,
            "method": "SQL.query",
            "table": "DBActivity"
        }
        logger.info(f"[USER_ACTIVITIES_FETCH] Starting user activities fetch with payload: {request_payload}")

        try:
            # Build the query
            query = self.db.query(DBActivity).filter(
                or_(
                    DBActivity.assigned_to.any(id=user_id),
                    DBActivity.created_by == user_id
                )
            )

            # Log the base query
            logger.debug(f"[USER_ACTIVITIES_FETCH] Base SQL query: SELECT * FROM DBActivity WHERE (assigned_to CONTAINS '{user_id}' OR created_by = '{user_id}')")

            if status:
                query = query.filter(DBActivity.status == status)
                logger.debug(f"[USER_ACTIVITIES_FETCH] Added status filter: status = '{status}'")

            # Log the query execution
            logger.debug(f"[USER_ACTIVITIES_FETCH] Executing count query for pagination")
            total = query.count()

            logger.debug(f"[USER_ACTIVITIES_FETCH] Executing main query with offset={offset}, limit={limit}")
            activities = query.offset(offset).limit(limit).all()

            # Log the database response
            db_response_data = {
                "total_count": total,
                "returned_count": len(activities),
                "activity_ids": [activity.id for activity in activities],
                "activity_types": [getattr(activity, 'type', 'N/A') for activity in activities],
                "activity_statuses": [getattr(activity, 'status', 'N/A') for activity in activities]
            }
            logger.info(f"[USER_ACTIVITIES_FETCH] Database response data: {db_response_data}")

            # Convert to Pydantic models
            converted_activities = [Activity.from_orm(activity) for activity in activities]

            # Create the response
            response = ActivityListResponse(
                activities=converted_activities,
                total=total,
                limit=limit,
                offset=offset
            )

            # Log the final response data
            final_response_data = {
                "total_activities": response.total,
                "returned_activities": len(response.activities),
                "limit": response.limit,
                "offset": response.offset,
                "activity_summaries": [
                    {
                        "id": activity.id,
                        "type": getattr(activity, 'type', 'N/A'),
                        "status": getattr(activity, 'status', 'N/A'),
                        "title": getattr(activity, 'title', 'N/A')
                    }
                    for activity in response.activities
                ]
            }
            logger.info(f"[USER_ACTIVITIES_FETCH] Final response data: {final_response_data}")
            logger.debug(f"[USER_ACTIVITIES_FETCH] Complete response object: {response.dict()}")

            return response

        except Exception as e:
            error_details = {
                "error_message": str(e),
                "error_type": type(e).__name__,
                "request_payload": request_payload
            }
            logger.error(f"[USER_ACTIVITIES_FETCH] Error fetching user activities: {error_details}")
            raise

    async def assign_activity(
        self,
        activity_id: str,
        user_ids: List[str],
        current_user_id: str
    ) -> Activity:
        """Assign an activity to users."""
        db_activity = self.db.query(DBActivity).filter(DBActivity.id == activity_id).first()
        if not db_activity:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Activity not found"
            )
        
        # Check if user has permission to assign
        if str(db_activity.created_by) != current_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to assign this activity"
            )
        
        # Get users to assign
        from ..db.models import User
        users = self.db.query(User).filter(User.id.in_(user_ids)).all()
        
        # Update assigned users
        db_activity.assigned_to = users
        db_activity.updated_at = datetime.utcnow()
        
        try:
            self.db.commit()
            self.db.refresh(db_activity)
            return Activity.from_orm(db_activity)
        except Exception as e:
            logger.error(f"Error assigning activity: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to assign activity"
            )


# Singleton instance of ActivityService
_activity_service = None

def get_activity_service() -> ActivityService:
    """
    Get a singleton instance of ActivityService.
    
    Returns:
        ActivityService: The singleton instance
    """
    global _activity_service
    if _activity_service is None:
        _activity_service = ActivityService()
    return _activity_service
