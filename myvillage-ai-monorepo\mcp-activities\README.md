# Activities MCP Service

Activity management and submission handling microservice.

## 🎯 Purpose

This service handles:
- Activity creation and management
- Activity listing and filtering
- Assignment submissions
- Submission grading
- Activity status tracking

## 🔧 Tools

### 1. list_activities
List available activities with optional filters.

**Parameters:**
- `activity_type` (string, optional): Filter by type (assignment, event, survey, discussion, volunteer)
- `status` (string, optional): Filter by status (draft, published, archived, completed)
- `limit` (integer, optional): Maximum results (1-100, default: 10)

**Example:**
```bash
curl http://localhost:8002/tools/list_activities?limit=5
```

### 2. get_activity
Get detailed information about a specific activity.

**Parameters:**
- `activity_id` (string, required): Activity ID

**Example:**
```bash
curl -X POST http://localhost:8002/tools/get_activity \
  -H "Content-Type: application/json" \
  -d '{"activity_id": "activity-123"}'
```

### 3. create_activity
Create a new activity.

**Parameters:**
- `title` (string, required): Activity title
- `description` (string, required): Description
- `activity_type` (string, required): Type of activity
- `created_by` (string, required): Creator user ID
- `due_date` (datetime, optional): Due date
- `points` (integer, optional): Points for completion
- `tags` (array, optional): Tags

**Example:**
```bash
curl -X POST http://localhost:8002/tools/create_activity \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Community Cleanup",
    "description": "Help clean up the local park",
    "activity_type": "volunteer",
    "created_by": "user-123",
    "points": 50
  }'
```

### 4. submit_assignment
Submit an assignment for an activity.

**Parameters:**
- `activity_id` (string, required): Activity ID
- `user_id` (string, required): User ID
- `title` (string, required): Submission title
- `description` (string, optional): Description
- `content` (string, optional): Content
- `is_public` (boolean, optional): Public visibility

**Example:**
```bash
curl -X POST http://localhost:8002/tools/submit_assignment \
  -H "Content-Type: application/json" \
  -d '{
    "activity_id": "activity-123",
    "user_id": "user-456",
    "title": "My Submission",
    "content": "Here is my work..."
  }'
```

### 5. grade_submission
Grade a submission.

**Parameters:**
- `submission_id` (string, required): Submission ID
- `grade` (number, required): Grade (0-100)
- `feedback` (string, required): Feedback
- `grader_id` (string, required): Grader user ID

**Example:**
```bash
curl -X POST http://localhost:8002/tools/grade_submission \
  -H "Content-Type: application/json" \
  -d '{
    "submission_id": "sub-123",
    "grade": 95,
    "feedback": "Excellent work!",
    "grader_id": "teacher-789"
  }'
```

### 6. list_submissions
List submissions with optional filters.

**Parameters:**
- `activity_id` (string, optional): Filter by activity ID
- `user_id` (string, optional): Filter by user ID
- `status` (string, optional): Filter by status (draft, submitted, graded, returned)
- `limit` (integer, optional): Maximum results (1-100, default: 10)

**Example:**
```bash
# List all submissions
curl http://localhost:8002/tools/list_submissions?limit=10

# Filter by activity
curl http://localhost:8002/tools/list_submissions?activity_id=act-123

# Filter by user
curl http://localhost:8002/tools/list_submissions?user_id=user-456

# Filter by status
curl http://localhost:8002/tools/list_submissions?status=graded
```

## 🚀 Running the Service

### Local Development

```bash
pip install -r requirements.txt
python -m app.main
```

### Docker

```bash
docker build -t mcp-activities .
docker run -p 8002:8002 mcp-activities
```

## 📡 Endpoints

- `GET /health` - Health check
- `GET /manifest` - MCP manifest
- `GET /tools/list_activities` - List activities
- `POST /tools/get_activity` - Get activity details
- `POST /tools/create_activity` - Create activity
- `POST /tools/submit_assignment` - Submit assignment
- `POST /tools/grade_submission` - Grade submission
- `GET /tools/list_submissions` - List submissions

## 🗄️ Database

Uses DynamoDB with the following tables:

**Table: activities**
- Primary Key: `id`
- Attributes: title, description, activity_type, status, created_by, created_at, updated_at, due_date, points, tags, submission_count

**Table: submissions**
- Primary Key: `id`
- Attributes: activity_id, user_id, title, description, content, status, is_public, created_at, updated_at, submitted_at, grade, feedback, graded_by, graded_at, likes_count

## 🔐 Activity Types

- `assignment` - Homework or tasks
- `event` - Community events
- `survey` - Surveys and polls
- `discussion` - Discussion topics
- `volunteer` - Volunteer opportunities

## 📊 Activity Status

- `draft` - Not yet published
- `published` - Active and visible
- `archived` - No longer active
- `completed` - Finished

## 📊 Submission Status

- `draft` - Work in progress
- `submitted` - Awaiting review
- `graded` - Reviewed and graded
- `returned` - Returned to student

## 🧪 Testing

```bash
# Health check
curl http://localhost:8002/health

# Get manifest
curl http://localhost:8002/manifest

# List activities
curl http://localhost:8002/tools/list_activities?limit=5

# List submissions
curl http://localhost:8002/tools/list_submissions?limit=10
```

## 📦 Port

8002
