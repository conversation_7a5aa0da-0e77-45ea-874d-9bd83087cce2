'use client'
'use client'

import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, MapPin, Users, School, Building2, Briefcase } from "lucide-react"

interface Activity {
    id: string
    moduleName?: string
    title?: string
    moduleType?: string
    description?: string
    activityType?: string
    activity_type?: string
    requestStatus?: string
    status?: string
    cityId?: string
    createdAt?: string
    created_at?: string
    type?: string
    [key: string]: any
}

interface ActivityListProps {
    activities: Activity[]
}

export default function ActivityList({ activities }: ActivityListProps) {
    if (!activities || activities.length === 0) {
        return null
    }

    const getIcon = (type: string) => {
        const lowerType = (type || '').toLowerCase()
        if (lowerType.includes('school')) return <School className="w-4 h-4" />
        if (lowerType.includes('student')) return <Users className="w-4 h-4" />
        if (lowerType.includes('project')) return <Briefcase className="w-4 h-4" />
        if (lowerType.includes('association')) return <Building2 className="w-4 h-4" />
        return <Calendar className="w-4 h-4" />
    }

    const getStatusColor = (status: string) => {
        const lowerStatus = (status || '').toLowerCase()
        if (lowerStatus.includes('approved') || lowerStatus.includes('published')) return "default" // primary
        if (lowerStatus.includes('pending')) return "secondary"
        if (lowerStatus.includes('rejected')) return "destructive"
        return "outline"
    }

    return (
        <div className="grid gap-3 w-full mt-3">
            {activities.map((activity) => {
                // Map API fields to display fields
                const name = activity.moduleName || activity.title || 'Unnamed Activity'
                const type = activity.moduleType || activity.description || 'Activity'
                const actType = activity.activityType || activity.activity_type || 'Event'
                const status = activity.requestStatus || activity.status
                const date = activity.createdAt || activity.created_at

                return (
                    <Card key={activity.id || Math.random().toString()} className="p-3 hover:shadow-md transition-shadow bg-card/50">
                        <div className="flex items-start justify-between gap-3">
                            <div className="flex items-start gap-3">
                                <div className="mt-1 p-2 rounded-full bg-primary/10 text-primary">
                                    {getIcon(type)}
                                </div>
                                <div>
                                    <h4 className="font-semibold text-sm">{name}</h4>
                                    <div className="flex flex-wrap gap-2 mt-1">
                                        <Badge variant="secondary" className="text-[10px] px-1.5 h-5">
                                            {type}
                                        </Badge>
                                        <Badge variant="outline" className="text-[10px] px-1.5 h-5">
                                            {actType}
                                        </Badge>
                                    </div>
                                    {date && (
                                        <p className="text-xs text-muted-foreground mt-1.5">
                                            Created: {new Date(date).toLocaleDateString()}
                                        </p>
                                    )}
                                </div>
                            </div>
                            {status && (
                                <Badge variant={getStatusColor(status) as any} className="text-[10px]">
                                    {status}
                                </Badge>
                            )}
                        </div>
                    </Card>
                )
            })}
        </div>
    )
}
