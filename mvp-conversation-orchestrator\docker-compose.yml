version: '3.8'

services:
  orchestrator:
    build: .
    container_name: mvp-orchestrator
    ports:
      - "8000:8000"
    environment:
      - DEBUG=true
      - LOG_LEVEL=INFO
      - ONBOARDING_MCP_URL=http://localhost:8001
      - ACTIVITY_MCP_URL=http://localhost:8002
    volumes:
      - ./app:/app/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import httpx; httpx.get('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
