@echo off
setlocal enabledelayedexpansion

:: Set the project root directory
set "PROJECT_ROOT=%~dp0"

:: Function to start a service in a new window
:start_service
set "SERVICE_NAME=%~1"
set "COMMAND=%~2"
set "WORKING_DIR=%~3"

echo Starting %SERVICE_NAME%...
start "%SERVICE_NAME%" cmd /k "cd /d "%WORKING_DIR%" && %COMMAND%"
timeout /t 2 /nobreak >nul

goto :eof

:: Start all services
call :start_service "API Gateway" "uvicorn app.main:app --port 8000 --reload" "%PROJECT_ROOT%myvillageai\api"
call :start_service "Onboarding Service" "uvicorn app.main:app --port 8001 --reload" "%PROJECT_ROOT%myvillageai\services\onboarding"
call :start_service "Gemini Service" "uvicorn app.main:app --port 8002 --reload" "%PROJECT_ROOT%myvillageai\services\gemini"
call :start_service "MCP Service" "uvicorn app.main:app --port 8003 --reload" "%PROJECT_ROOT%myvillageai\mcp"

echo.
echo All services have been started in separate windows.
echo.
echo API Gateway:    http://localhost:8000
echo Onboarding API: http://localhost:8001
echo Gemini API:     http://localhost:8002
echo MCP API:        http://localhost:8003
echo.
pause

echo.
echo Stopping all services...
taskkill /F /IM python.exe /T >nul 2>&1
taskkill /F /IM uvicorn.exe /T >nul 2>&1
echo All services have been stopped.
pause
