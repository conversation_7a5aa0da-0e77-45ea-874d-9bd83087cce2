# MVP Conversation Orchestrator

A standalone FastAPI service that routes user messages to appropriate MCP (Micro Context Protocol) services based on intent detection.

## Overview

This orchestrator acts as a gateway between the frontend and specialized MCP services:
- **OnboardingMCP**: Handles signup/login flows
- **ActivityMCP**: Manages activity-related queries and operations
- **ChatMCP**: General conversation (future)

## Architecture

```
Frontend → Orchestrator → [OnboardingMCP | ActivityMCP | ChatMCP]
              ↓
         Intent Detection
              ↓
         Route to MCP
              ↓
      Unified Response
```

## Features

- ✅ Single `/chat` endpoint for all user messages
- ✅ Automatic intent detection (onboarding vs activity)
- ✅ Mock MCP clients (ready for real integration)
- ✅ Comprehensive logging for debugging
- ✅ Health check endpoints
- ✅ CORS enabled
- ✅ Docker support

## Quick Start

### Local Development

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the server:**
   ```bash
   python -m app.main
   ```
   
   Or with uvicorn directly:
   ```bash
   uvicorn app.main:app --reload --port 8100
   ```
   
   Or use the start script:
   ```bash
   # Windows
   start.bat
   
   # PowerShell
   .\test_requests.ps1
   ```

3. **Test the endpoint:**
   ```bash
   curl -X POST http://localhost:8100/chat \
     -H "Content-Type: application/json" \
     -d '{"user_id": "user-123", "text": "I want to sign up"}'
   ```

### Docker

1. **Build the image:**
   ```bash
   docker build -t mvp-orchestrator .
   ```

2. **Run the container:**
   ```bash
   docker run -p 8100:8100 mvp-orchestrator
   ```
   
   Or use docker-compose:
   ```bash
   docker-compose up
   ```

## API Endpoints

### POST /chat

Main endpoint for processing user messages.

**Request:**
```json
{
  "user_id": "user-123",
  "text": "I want to sign up",
  "session_id": "session-abc"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Great! Let's get you signed up. What's your email address?",
  "intent": "onboarding",
  "routed_to": "onboarding_mcp",
  "data": {
    "flow_type": "signup",
    "current_step": "email",
    "session_id": "session-abc",
    "next_expected": "email"
  }
}
```

### GET /health

Health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "service": "MVP Conversation Orchestrator",
  "version": "1.0.0"
}
```

### GET /

Root endpoint (same as /health).

**Response:**
```json
{
  "status": "healthy",
  "service": "MVP Conversation Orchestrator",
  "version": "1.0.0"
}
```

## Project Structure

```
mvp-conversation-orchestrator/
├── app/
│   ├── main.py                    # FastAPI app + startup
│   ├── routers/
│   │   └── chat_router.py         # /chat endpoint
│   ├── services/
│   │   ├── intent_client.py       # Intent detection
│   │   ├── onboarding_client.py   # OnboardingMCP client (mock)
│   │   └── activity_client.py     # ActivityMCP client (mock)
│   ├── schemas/
│   │   └── message_schema.py      # Request/response models
│   └── core/
│       └── config.py               # Configuration
├── requirements.txt
├── Dockerfile
├── .gitignore
└── README.md
```

## Intent Detection

The orchestrator uses keyword-based intent detection:

**Onboarding Keywords:**
- sign up, signup, register, create account, new account, join
- login, log in, sign in, authenticate

**Activity Keywords:**
- activity, activities, event, events
- what can i do, things to do, submit, create activity

**Default:** Routes to ActivityMCP if no specific intent detected.

## Logging

Comprehensive logging at every step:
- Request received
- Intent detection
- MCP routing
- Response building
- Errors and exceptions

Example log output:
```
2025-11-13 10:30:45 - app.routers.chat_router - INFO - ================================================================================
2025-11-13 10:30:45 - app.routers.chat_router - INFO - [ChatRouter] NEW MESSAGE from user: user-123
2025-11-13 10:30:45 - app.routers.chat_router - INFO - [ChatRouter] Text: 'I want to sign up'
2025-11-13 10:30:45 - app.routers.chat_router - INFO - [ChatRouter] Step 1: Detecting intent...
2025-11-13 10:30:45 - app.services.intent_client - INFO - [IntentClient] Detected ONBOARDING intent (keyword: 'sign up')
2025-11-13 10:30:45 - app.routers.chat_router - INFO - [ChatRouter] Step 2: Routing to ONBOARDING client...
2025-11-13 10:30:45 - app.services.onboarding_client - INFO - [OnboardingClient] Returning SIGNUP flow response
2025-11-13 10:30:45 - app.routers.chat_router - INFO - [ChatRouter] ✓ Request processed successfully
```

## Configuration

Edit `app/core/config.py` or use environment variables:

```python
# Server
HOST=0.0.0.0
PORT=8100
DEBUG=true

# MCP URLs (for future real integration)
ONBOARDING_MCP_URL=http://localhost:8001
ACTIVITY_MCP_URL=http://localhost:8002
CHAT_MCP_URL=http://localhost:8003

# Logging
LOG_LEVEL=INFO
```

## Next Steps

1. **Connect Real MCPs**: Replace mock clients with actual HTTP calls to MCP services
2. **Add Authentication**: Implement user authentication/authorization
3. **Session Management**: Add persistent session storage
4. **Error Handling**: Enhanced error recovery and retry logic
5. **Monitoring**: Add metrics and observability
6. **Testing**: Unit and integration tests

## Testing Examples

**Onboarding Intent (Signup):**
```bash
curl -X POST http://localhost:8100/chat \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user-123", "text": "I want to sign up"}'
```

**Onboarding Intent (Login):**
```bash
curl -X POST http://localhost:8100/chat \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user-456", "text": "I need to log in"}'
```

**Activity Intent:**
```bash
curl -X POST http://localhost:8100/chat \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user-789", "text": "Show me activities"}'
```

**General Query (defaults to Activity):**
```bash
curl -X POST http://localhost:8100/chat \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user-999", "text": "Hello, what can you do?"}'
```

## License

MIT

## Related Documentation

- `MVP_MODULARIZATION_PLAN.md` - Overall architecture plan
- `CODE_EXTRACTION_GUIDE.md` - Service extraction guide
- `IMPLEMENTATION_QUICK_START.md` - Implementation guide
