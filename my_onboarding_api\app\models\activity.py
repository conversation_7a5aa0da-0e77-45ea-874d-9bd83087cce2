from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, <PERSON>, validator
from enum import Enum

class ActivityStatus(str, Enum):
    ACTIVE = "active"
    COMPLETED = "completed"
    PENDING = "pending"
    ARCHIVED = "archived"

class ActivityType(str, Enum):
    ASSIGNMENT = "assignment"
    PROJECT = "project"
    QUIZ = "quiz"
    DISCUSSION = "discussion"
    OTHER = "other"

class ActivityBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=2000)
    activity_type: ActivityType = ActivityType.ASSIGNMENT
    status: ActivityStatus = ActivityStatus.ACTIVE
    due_date: Optional[datetime] = None
    points: Optional[int] = Field(None, ge=0)
    metadata: Dict[str, Any] = Field(default_factory=dict)

class ActivityCreate(ActivityBase):
    assigned_to: List[str] = Field(default_factory=list)
    created_by: str
    module_id: Optional[str] = None
    module_type: Optional[str] = None

class ActivityUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=2000)
    activity_type: Optional[ActivityType] = None
    status: Optional[ActivityStatus] = None
    due_date: Optional[datetime] = None
    points: Optional[int] = Field(None, ge=0)
    metadata: Optional[Dict[str, Any]] = None

class Activity(ActivityBase):
    id: str
    created_at: datetime
    updated_at: datetime
    created_by: str
    assigned_to: List[str]
    module_id: Optional[str] = None
    module_type: Optional[str] = None

    class Config:
        from_attributes = True

class ActivityListResponse(BaseModel):
    activities: List[Activity]
    total: int
    limit: int
    offset: int
