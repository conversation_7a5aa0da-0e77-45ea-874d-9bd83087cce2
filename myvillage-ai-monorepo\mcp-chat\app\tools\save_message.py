"""MCP Tool: Save chat message."""
import logging
from typing import Dict, Any

from ..services.chat_service import get_chat_service
from ..models.chat import ChatMessageC<PERSON>, MessageType

logger = logging.getLogger(__name__)


async def save_message(
    userId: str,
    chatType: str,
    message: str,
    role: str,
    messageType: str = "TEXT",
    fileUrl: str = ""
) -> Dict[str, Any]:
    """
    Save a chat message to DynamoDB.
    
    Args:
        userId: User ID
        chatType: Type of chat (e.g., "GeneralWeb", "AcademicWeb")
        message: Message content
        role: Message role ("user" or "assistant")
        messageType: Type of message ("TEXT", "FILE", "URL")
        fileUrl: Optional file URL
        
    Returns:
        Dictionary with success status and saved message data
    """
    try:
        chat_service = get_chat_service()
        
        # Create message data
        message_data = ChatMessageCreate(
            userId=userId,
            chatType=chatType,
            message=message,
            role=role,
            messageType=MessageType(messageType),
            fileUrl=fileUrl
        )
        
        # Save message
        saved_message = chat_service.save_message(message_data)
        
        return {
            "success": True,
            "message": "Chat message saved successfully",
            "data": saved_message.dict()
        }
        
    except Exception as e:
        logger.error(f"Error in save_message tool: {e}")
        return {
            "success": False,
            "message": f"Failed to save message: {str(e)}",
            "data": None
        }


# MCP Tool metadata
TOOL_METADATA = {
    "name": "save_message",
    "description": "Save a chat message to the database",
    "parameters": {
        "type": "object",
        "properties": {
            "userId": {
                "type": "string",
                "description": "User ID"
            },
            "chatType": {
                "type": "string",
                "description": "Type of chat (e.g., GeneralWeb, AcademicWeb)"
            },
            "message": {
                "type": "string",
                "description": "Message content"
            },
            "role": {
                "type": "string",
                "enum": ["user", "assistant"],
                "description": "Message role"
            },
            "messageType": {
                "type": "string",
                "enum": ["TEXT", "FILE", "URL"],
                "description": "Type of message",
                "default": "TEXT"
            },
            "fileUrl": {
                "type": "string",
                "description": "Optional file URL",
                "default": ""
            }
        },
        "required": ["userId", "chatType", "message", "role"]
    }
}
