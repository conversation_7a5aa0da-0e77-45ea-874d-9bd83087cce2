"""
Test script for orchestrator integration.

This script tests the integration between the backend and the orchestrator service.
"""

import asyncio
import sys
from app.services.orchestrator_client import send_to_orchestrator, is_orchestrator_available


async def test_orchestrator_health():
    """Test if orchestrator is available."""
    print("=" * 80)
    print("Testing Orchestrator Health Check")
    print("=" * 80)
    
    is_available = is_orchestrator_available()
    
    if is_available:
        print("✅ Orchestrator is available and healthy")
        return True
    else:
        print("❌ Orchestrator is NOT available")
        print("   Please ensure the orchestrator is running:")
        print("   cd mvp-conversation-orchestrator")
        print("   python -m app.main")
        return False


async def test_signup_intent():
    """Test signup intent detection."""
    print("\n" + "=" * 80)
    print("Test 1: Signup Intent")
    print("=" * 80)
    
    try:
        response = await send_to_orchestrator(
            user_id="test-user-1",
            text="I want to sign up",
            session_id="test-session-1"
        )
        
        print(f"✅ Response received:")
        print(f"   Intent: {response.get('intent')}")
        print(f"   Routed to: {response.get('routed_to')}")
        print(f"   Message: {response.get('message')[:80]}...")
        
        assert response.get('intent') == 'onboarding', "Expected onboarding intent"
        assert response.get('routed_to') == 'onboarding_mcp', "Expected routing to onboarding_mcp"
        
        print("✅ Test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False


async def test_login_intent():
    """Test login intent detection."""
    print("\n" + "=" * 80)
    print("Test 2: Login Intent")
    print("=" * 80)
    
    try:
        response = await send_to_orchestrator(
            user_id="test-user-2",
            text="I need to log in",
            session_id="test-session-2"
        )
        
        print(f"✅ Response received:")
        print(f"   Intent: {response.get('intent')}")
        print(f"   Routed to: {response.get('routed_to')}")
        print(f"   Message: {response.get('message')[:80]}...")
        
        assert response.get('intent') == 'onboarding', "Expected onboarding intent"
        assert response.get('routed_to') == 'onboarding_mcp', "Expected routing to onboarding_mcp"
        
        print("✅ Test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False


async def test_activity_intent():
    """Test activity intent detection."""
    print("\n" + "=" * 80)
    print("Test 3: Activity Intent")
    print("=" * 80)
    
    try:
        response = await send_to_orchestrator(
            user_id="test-user-3",
            text="Show me activities",
            session_id="test-session-3"
        )
        
        print(f"✅ Response received:")
        print(f"   Intent: {response.get('intent')}")
        print(f"   Routed to: {response.get('routed_to')}")
        print(f"   Message: {response.get('message')[:80]}...")
        
        assert response.get('intent') == 'activity', "Expected activity intent"
        assert response.get('routed_to') == 'activity_mcp', "Expected routing to activity_mcp"
        
        print("✅ Test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False


async def test_general_query():
    """Test general query (defaults to activity)."""
    print("\n" + "=" * 80)
    print("Test 4: General Query")
    print("=" * 80)
    
    try:
        response = await send_to_orchestrator(
            user_id="test-user-4",
            text="Hello, what can you help me with?",
            session_id="test-session-4"
        )
        
        print(f"✅ Response received:")
        print(f"   Intent: {response.get('intent')}")
        print(f"   Routed to: {response.get('routed_to')}")
        print(f"   Message: {response.get('message')[:80]}...")
        
        # General queries default to activity
        assert response.get('routed_to') == 'activity_mcp', "Expected routing to activity_mcp"
        
        print("✅ Test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False


async def main():
    """Run all tests."""
    print("\n" + "=" * 80)
    print("ORCHESTRATOR INTEGRATION TEST SUITE")
    print("=" * 80)
    
    # Test health check first
    health_ok = await test_orchestrator_health()
    
    if not health_ok:
        print("\n" + "=" * 80)
        print("❌ TESTS ABORTED: Orchestrator not available")
        print("=" * 80)
        sys.exit(1)
    
    # Run all tests
    results = []
    results.append(await test_signup_intent())
    results.append(await test_login_intent())
    results.append(await test_activity_intent())
    results.append(await test_general_query())
    
    # Summary
    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("✅ ALL TESTS PASSED!")
        sys.exit(0)
    else:
        print(f"❌ {total - passed} TEST(S) FAILED")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
