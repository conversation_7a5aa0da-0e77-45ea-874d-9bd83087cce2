"""Mock intent service for testing."""
from typing import List, Dict, Any
from unittest.mock import MagicMock

class MockIntentService:
    def __init__(self):
        self.classify_intent = MagicMock(return_value=[
            {"label": "signup", "score": 0.95},
            {"label": "login", "score": 0.05}
        ])
        self.get_top_intent = MagicMock(return_value="signup")
        self.is_ready = MagicMock(return_value=True)

# Create a singleton instance
intent_service = MockIntentService()
