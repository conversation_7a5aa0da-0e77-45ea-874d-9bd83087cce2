import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { ToolProvider } from "@/contexts/ToolContext";
import { ChatProvider } from "@/contexts/ChatContext";
import { AuthProvider } from "@/contexts/AuthContext";
import Toaster from "@/components/ui/Toaster";
import { toast } from "@/components/ui/Toaster";
import HtmlWrapper from "@/components/layout/HtmlWrapper";
import MainNav from "@/components/layout/MainNav";
import { AppProviders } from "@/components/providers/AppProviders";

// Make toast available globally for easier access
type ToastType = 'default' | 'success' | 'error' | 'warning' | 'info';
declare global {
  interface Window {
    toast: typeof toast & {
      (message: string, type?: ToastType, duration?: number): string;
      success: (message: string, duration?: number) => string;
      error: (message: string, duration?: number) => string;
      warning: (message: string, duration?: number) => string;
      info: (message: string, duration?: number) => string;
      remove: (id: string) => void;
    };
  }
}

// Fonts
const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "My Village AI - Your Community Assistant",
  description: "Connect with My Village AI, your intelligent community assistant designed to help with local information, support, and guidance.",
  keywords: ["AI assistant", "community", "village", "local support", "chatbot"],
  authors: [{ name: "My Village AI Team" }],
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "hsl(var(--primary))" },
    { media: "(prefers-color-scheme: dark)", color: "hsl(var(--primary))" },
    { media: "(prefers-color-scheme: dark)", color: "#9cc954" }
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className={`${geistSans.variable} ${geistMono.variable} font-sans`}>
        <AppProviders>
          <AuthProvider>
            <ChatProvider>
              <ToolProvider>
                <HtmlWrapper>
                  <div className="min-h-screen bg-background flex flex-col">
                    <MainNav />
                    <main className="flex-1">{children}</main>
                  </div>
                </HtmlWrapper>
                <Toaster />
              </ToolProvider>
            </ChatProvider>
          </AuthProvider>
        </AppProviders>
      </body>
    </html>
  );
}
