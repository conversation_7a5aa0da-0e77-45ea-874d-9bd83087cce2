"""Tool: Redeem Reward."""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "common"))

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
import logging

from common.models.base import BaseResponse
from ..services.rewards_service import RewardsService

logger = logging.getLogger(__name__)
router = APIRouter()
rewards_service = RewardsService()


class RedeemRewardRequest(BaseModel):
    """Redeem reward request model."""
    user_id: str = Field(..., description="User ID")
    reward_id: str = Field(..., description="Reward ID to redeem")
    points: int = Field(..., ge=1, description="Points to redeem")
    description: str = Field("Reward redemption", description="Description of redemption")


@router.post("/redeem_reward", response_model=BaseResponse)
async def redeem_reward(request: RedeemRewardRequest):
    """
    Redeem points for a reward.
    
    This tool redeems points from the user's balance for a specific reward.
    
    Args:
        request: Redeem reward request
        
    Returns:
        BaseResponse with updated reward data
        
    Raises:
        HTTPException: If insufficient points or redemption fails
    """
    try:
        logger.info(f"Redeeming reward: {request.reward_id} for user: {request.user_id}")
        
        # Redeem points
        reward = await rewards_service.redeem_points(
            user_id=request.user_id,
            points=request.points,
            reward_id=request.reward_id,
            description=request.description
        )
        
        logger.info(f"Reward redeemed: {request.points} points. Remaining: {reward.available_points}")
        
        return BaseResponse(
            success=True,
            message=f"Successfully redeemed {request.points} points",
            data={
                "user_id": reward.user_id,
                "points_redeemed": request.points,
                "available_points": reward.available_points,
                "total_redeemed": reward.redeemed_points,
                "reward_id": request.reward_id
            }
        )
    except ValueError as e:
        logger.warning(f"Redemption failed: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error redeeming reward: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to redeem reward: {str(e)}"
        )
