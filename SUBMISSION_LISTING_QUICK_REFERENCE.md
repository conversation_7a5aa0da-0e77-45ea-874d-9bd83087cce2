# 📋 Submission Listing - Quick Reference Card

## 🚀 Quick Start

### Via Chat UI
```
User: "Show me submissions"
Bot: "Would you like to filter by: Activity, User, Status, or All?"
User: "All"
Bot: Shows all submissions
```

### Via Direct API
```bash
# All submissions
curl "http://localhost:8002/tools/list_submissions?limit=10"

# By activity
curl "http://localhost:8002/tools/list_submissions?activity_id=act-123"

# By user
curl "http://localhost:8002/tools/list_submissions?user_id=user-456"

# By status
curl "http://localhost:8002/tools/list_submissions?status=graded"
```

---

## 🎯 Filter Options

| Option | Description | Example |
|--------|-------------|---------|
| **Activity** | All submissions for an activity | Teacher reviewing assignment |
| **User** | All submissions by a user | Student's submission history |
| **Status** | Submissions by status | All graded submissions |
| **All** | All submissions | Admin overview |

---

## 📊 Submission Statuses

| Status | Meaning |
|--------|---------|
| `draft` | Work in progress |
| `submitted` | Awaiting review |
| `graded` | Reviewed and graded |
| `returned` | Returned to student |

---

## 🔑 Trigger Keywords

Say any of these to start:
- "submissions"
- "show submissions"
- "list submissions"
- "view submissions"
- "my submissions"

---

## 📡 Endpoints

| Service | Endpoint | Method |
|---------|----------|--------|
| Orchestrator | `/chat` | POST |
| Activities MCP | `/tools/list_submissions` | GET |

---

## 🧪 Test Commands

```bash
# Health checks
curl http://localhost:8100/health  # Orchestrator
curl http://localhost:8002/health  # Activities MCP

# Run test script
python test_submission_listing.py

# Manual test
curl -X POST http://localhost:8100/chat \
  -H "Content-Type: application/json" \
  -d '{"user_id": "test", "text": "show submissions"}'
```

---

## 📦 Files Modified

```
✅ mcp-activities/app/tools/list_submissions.py (NEW)
✅ mcp-activities/app/main.py
✅ mcp-activities/mcp-manifest.json
✅ mcp-activities/README.md
✅ orchestrator/app/services/conversation_manager.py
✅ orchestrator/app/services/activities_client.py
✅ orchestrator/app/services/intent_detector.py
✅ orchestrator/app/routers/chat_router.py
```

---

## 🎨 Response Example

```json
{
  "success": true,
  "message": "Found 3 submission(s)",
  "data": {
    "submissions": [
      {
        "id": "sub-123",
        "title": "My Assignment",
        "status": "graded",
        "grade": 95.0,
        "updated_at": "2025-11-21T15:30:00"
      }
    ],
    "count": 3
  }
}
```

---

## 🔧 Parameters

| Parameter | Type | Required | Default | Range |
|-----------|------|----------|---------|-------|
| `activity_id` | string | No | - | - |
| `user_id` | string | No | - | - |
| `status` | string | No | - | draft, submitted, graded, returned |
| `limit` | integer | No | 10 | 1-100 |

---

## 📚 Documentation

- **Full Guide:** `SUBMISSION_LISTING_GUIDE.md`
- **Summary:** `SUBMISSION_LISTING_IMPLEMENTATION_SUMMARY.md`
- **Tests:** `test_submission_listing.py`
- **Service README:** `mcp-activities/README.md`

---

## ⚡ Common Use Cases

### 1. Teacher Reviews Activity
```
"Show submissions for activity act-123"
→ See all student submissions for that activity
```

### 2. Student Checks History
```
"Show my submissions"
→ See all submissions by that user
```

### 3. Admin Monitors Grading
```
"Show graded submissions"
→ See all submissions that have been graded
```

### 4. Overview Dashboard
```
"Show all submissions"
→ See recent submissions across all activities
```

---

## 🎯 Architecture

```
Chat UI → Orchestrator → Activities MCP → DynamoDB
           ↓
    Intent Detection
           ↓
    Conversation Flow
           ↓
    Filter & Sort
```

---

**Quick Reference v1.0** | November 21, 2025
