"""Chat service for managing chat messages in DynamoDB."""
import uuid
import logging
from datetime import datetime
from typing import List, Optional, Dict
from boto3.dynamodb.conditions import Key, Attr
from botocore.exceptions import ClientError

from ..core.database import get_db
from ..core.config import settings
from ..models.chat import (
    ChatMessage,
    ChatMessageCreate,
    ConversationMetadata,
    MessageType
)

logger = logging.getLogger(__name__)


class ChatService:
    """Service for managing chat messages."""
    
    def __init__(self):
        """Initialize chat service."""
        self.db = get_db()
        self.table_name = settings.dynamodb_table_name
    
    def save_message(self, message_data: ChatMessageCreate) -> ChatMessage:
        """
        Save a chat message to DynamoDB.
        
        Args:
            message_data: Chat message data
            
        Returns:
            Saved chat message
        """
        try:
            table = self.db.get_table(self.table_name)
            
            # Generate unique ID and timestamp
            message_id = str(uuid.uuid4())
            created_at = datetime.utcnow().isoformat() + "Z"
            
            # Create message item
            item = {
                "id": message_id,
                "userId": message_data.userId,
                "chatType": message_data.chatType,
                "message": message_data.message,
                "role": message_data.role,
                "messageType": message_data.messageType.value if isinstance(message_data.messageType, MessageType) else message_data.messageType,
                "fileUrl": message_data.fileUrl or "",
                "isDeleted": "false",
                "createdAt": created_at
            }
            
            # Save to DynamoDB
            table.put_item(Item=item)
            
            logger.info(f"Saved message {message_id} for user {message_data.userId}")
            
            return ChatMessage(**item)
            
        except ClientError as e:
            logger.error(f"Error saving message: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error saving message: {e}")
            raise
    
    def get_chat_history(
        self,
        user_id: str,
        chat_type: Optional[str] = None,
        limit: int = 50
    ) -> List[ChatMessage]:
        """
        Get chat history for a user.
        
        Args:
            user_id: User ID
            chat_type: Optional chat type filter
            limit: Maximum number of messages to return
            
        Returns:
            List of chat messages
        """
        try:
            table = self.db.get_table(self.table_name)
            
            # Query using GSI (chatGPTByDate)
            query_params = {
                "IndexName": "chatGPTByDate",
                "KeyConditionExpression": Key("userId").eq(user_id),
                "FilterExpression": Attr("isDeleted").eq("false"),
                "ScanIndexForward": True,  # Sort by createdAt ascending
                "Limit": limit
            }
            
            # Add chat type filter if provided
            if chat_type:
                query_params["FilterExpression"] = query_params["FilterExpression"] & Attr("chatType").eq(chat_type)
            
            response = table.query(**query_params)
            
            messages = []
            for item in response.get("Items", []):
                messages.append(ChatMessage(**item))
            
            logger.info(f"Retrieved {len(messages)} messages for user {user_id}")
            
            return messages
            
        except ClientError as e:
            logger.error(f"Error retrieving chat history: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error retrieving chat history: {e}")
            raise
    
    def get_conversations(self, user_id: str) -> List[ConversationMetadata]:
        """
        Get list of conversations for a user.
        
        Groups messages by chatType and creates conversation metadata.
        
        Args:
            user_id: User ID
            
        Returns:
            List of conversation metadata
        """
        try:
            # Get all messages for user
            messages = self.get_chat_history(user_id, limit=1000)
            
            # Group by chatType
            conversations_dict: Dict[str, List[ChatMessage]] = {}
            for msg in messages:
                if msg.chatType not in conversations_dict:
                    conversations_dict[msg.chatType] = []
                conversations_dict[msg.chatType].append(msg)
            
            # Create conversation metadata
            conversations = []
            for chat_type, msgs in conversations_dict.items():
                if not msgs:
                    continue
                
                # Sort by createdAt
                sorted_msgs = sorted(msgs, key=lambda x: x.createdAt)
                
                # Get first user message for title
                title = "New Conversation"
                for msg in sorted_msgs:
                    if msg.role == "user":
                        title = msg.message[:50] + ("..." if len(msg.message) > 50 else "")
                        break
                
                # Get last message
                last_msg = sorted_msgs[-1]
                
                conversation = ConversationMetadata(
                    conversationId=chat_type,  # Using chatType as conversation ID
                    userId=user_id,
                    chatType=chat_type,
                    title=title,
                    lastMessage=last_msg.message[:100],
                    lastMessageTime=last_msg.createdAt,
                    messageCount=len(msgs)
                )
                conversations.append(conversation)
            
            # Sort by last message time (most recent first)
            conversations.sort(key=lambda x: x.lastMessageTime, reverse=True)
            
            logger.info(f"Retrieved {len(conversations)} conversations for user {user_id}")
            
            return conversations
            
        except Exception as e:
            logger.error(f"Error retrieving conversations: {e}")
            raise
    
    def delete_message(self, message_id: str, user_id: str) -> bool:
        """
        Soft delete a message (set isDeleted to "true").
        
        Args:
            message_id: Message ID
            user_id: User ID (for verification)
            
        Returns:
            True if successful
        """
        try:
            table = self.db.get_table(self.table_name)
            
            # Update the message
            response = table.update_item(
                Key={"id": message_id},
                UpdateExpression="SET isDeleted = :deleted",
                ConditionExpression="userId = :user_id",
                ExpressionAttributeValues={
                    ":deleted": "true",
                    ":user_id": user_id
                },
                ReturnValues="UPDATED_NEW"
            )
            
            logger.info(f"Deleted message {message_id}")
            
            return True
            
        except ClientError as e:
            if e.response['Error']['Code'] == 'ConditionalCheckFailedException':
                logger.warning(f"Message {message_id} not found or user mismatch")
                return False
            logger.error(f"Error deleting message: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error deleting message: {e}")
            raise


# Global service instance
chat_service = ChatService()


def get_chat_service() -> ChatService:
    """
    Get chat service instance.
    
    Returns:
        ChatService instance
    """
    return chat_service
