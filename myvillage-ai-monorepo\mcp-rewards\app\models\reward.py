"""Reward models."""
from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime


class Reward(BaseModel):
    """Reward model."""
    id: str
    user_id: str
    total_points: int = 0
    available_points: int = 0
    redeemed_points: int = 0
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class RewardTransaction(BaseModel):
    """Reward transaction model."""
    id: str
    user_id: str
    activity_id: Optional[str] = None
    reward_id: Optional[str] = None
    points: int
    transaction_type: str  # "earn" or "redeem"
    description: str
    created_at: datetime

    class Config:
        from_attributes = True
