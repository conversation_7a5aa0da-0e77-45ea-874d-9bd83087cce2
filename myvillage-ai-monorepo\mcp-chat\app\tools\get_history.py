"""MCP Tool: Get chat history."""
import logging
from typing import Dict, Any, Optional

from ..services.chat_service import get_chat_service

logger = logging.getLogger(__name__)


async def get_history(
    userId: str,
    chatType: Optional[str] = None,
    limit: int = 50
) -> Dict[str, Any]:
    """
    Get chat history for a user.
    
    Args:
        userId: User ID
        chatType: Optional chat type filter
        limit: Maximum number of messages to return
        
    Returns:
        Dictionary with success status and chat messages
    """
    try:
        chat_service = get_chat_service()
        
        # Get chat history
        messages = chat_service.get_chat_history(userId, chatType, limit)
        
        return {
            "success": True,
            "message": f"Retrieved {len(messages)} messages",
            "data": [msg.dict() for msg in messages]
        }
        
    except Exception as e:
        logger.error(f"Error in get_history tool: {e}")
        return {
            "success": False,
            "message": f"Failed to retrieve chat history: {str(e)}",
            "data": []
        }


# MCP Tool metadata
TOOL_METADATA = {
    "name": "get_history",
    "description": "Get chat history for a user",
    "parameters": {
        "type": "object",
        "properties": {
            "userId": {
                "type": "string",
                "description": "User ID"
            },
            "chatType": {
                "type": "string",
                "description": "Optional chat type filter"
            },
            "limit": {
                "type": "integer",
                "description": "Maximum number of messages to return",
                "default": 50
            }
        },
        "required": ["userId"]
    }
}
