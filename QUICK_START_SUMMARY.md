# 🚀 Quick Start Summary - Monorepo Migration

## Current State → Target State

### What You Have Now
```
📁 my_onboarding_api/          (Monolithic - everything in one)
   ├── Onboarding
   ├── Activities
   ├── Submissions
   ├── Auth
   └── Intent Classification

📁 mvp-conversation-orchestrator/  (Basic orchestrator with mocks)
   └── Mock clients (not calling real services)
```

### What You'll Build
```
📁 myvillage-ai-monorepo/
   ├── orchestrator/           (Intent detection & routing ONLY)
   ├── mcp-onboarding/        (User signup/login/verification)
   ├── mcp-activities/        (Activity management & submissions)
   ├── mcp-rewards/           (Rewards tracking - NEW)
   ├── mcp-approval/          (Activity approvals - NEW)
   └── common/                (Shared code library)
```

---

## 🎯 Architecture Pattern

```
┌─────────────┐
│   Frontend  │
└──────┬──────┘
       │
       ▼
┌─────────────────┐
│  Orchestrator   │  ← Intent Detection & Routing
│   (Port 8100)   │
└────────┬────────┘
         │
    ┌────┴────┬─────────┬──────────┐
    ▼         ▼         ▼          ▼
┌────────┐ ┌────────┐ ┌────────┐ ┌────────┐
│Onboard │ │Activity│ │Rewards │ │Approval│
│  8001  │ │  8002  │ │  8003  │ │  8004  │
└────┬───┘ └────┬───┘ └────┬───┘ └────┬───┘
     └──────────┴──────────┴──────────┘
                    │
                    ▼
              ┌──────────┐
              │ Database │
              └──────────┘
```

---

## 📋 6-Week Implementation Plan

| Week | Phase | What You'll Do | Output |
|------|-------|----------------|--------|
| **1** | Foundation | Create monorepo structure, common library, Docker setup | Working monorepo skeleton |
| **2** | Onboarding | Extract auth code from my_onboarding_api | Working onboarding MCP |
| **3** | Activities | Extract activity code from my_onboarding_api | Working activities MCP |
| **4** | New Services | Create rewards & approval services | 2 new MCPs |
| **5** | Orchestrator | Replace mocks with real HTTP clients | Working orchestrator |
| **6** | Testing | Integration tests, deployment, docs | Production-ready! |

---

## 🛠️ Step 1: Create Monorepo (Day 1)

```bash
# Create directory structure
mkdir myvillage-ai-monorepo
cd myvillage-ai-monorepo

# Create service directories
mkdir -p orchestrator/{app,tests}
mkdir -p mcp-onboarding/{app,tests}
mkdir -p mcp-activities/{app,tests}
mkdir -p mcp-rewards/{app,tests}
mkdir -p mcp-approval/{app,tests}
mkdir -p common/{models,utils,config}

# Initialize git
git init
```

---

## 📦 Step 2: Create Docker Compose (Day 1)

**File: `docker-compose.yml`**
```yaml
version: '3.8'
services:
  orchestrator:
    build: ./orchestrator
    ports: ["8100:8100"]
    environment:
      - ONBOARDING_MCP_URL=http://mcp-onboarding:8001
      - ACTIVITIES_MCP_URL=http://mcp-activities:8002
    depends_on: [mcp-onboarding, mcp-activities]

  mcp-onboarding:
    build: ./mcp-onboarding
    ports: ["8001:8001"]

  mcp-activities:
    build: ./mcp-activities
    ports: ["8002:8002"]

  mcp-rewards:
    build: ./mcp-rewards
    ports: ["8003:8003"]

  mcp-approval:
    build: ./mcp-approval
    ports: ["8004:8004"]
```

---

## 🔄 Step 3: Code Migration Map

### From `my_onboarding_api` → `mcp-onboarding`
```
✂️ Cut these files:
app/services/auth_service.py
app/services/session_service.py
app/models/user.py
app/api/auth.py

📋 Paste to:
mcp-onboarding/app/services/
mcp-onboarding/app/models/
mcp-onboarding/app/tools/
```

### From `my_onboarding_api` → `mcp-activities`
```
✂️ Cut these files:
app/api/endpoints/activities.py
app/api/endpoints/submissions.py
app/services/activity_service.py
app/services/submission_service.py
app/models/activity.py
app/models/submission.py

📋 Paste to:
mcp-activities/app/tools/
mcp-activities/app/services/
mcp-activities/app/models/
```

### From `mvp-conversation-orchestrator` → `orchestrator`
```
✂️ Cut these files:
app/main.py
app/routers/chat_router.py
app/services/intent_client.py

📋 Paste to:
orchestrator/app/main.py
orchestrator/app/routers/chat_router.py
orchestrator/app/services/intent_detector.py

⚠️ Replace mock clients with real HTTP clients!
```

---

## 🎨 Key Changes Required

### 1. Orchestrator: Replace Mocks with Real Clients

**Before (Mock):**
```python
class OnboardingClient:
    async def process_message(self, user_id, text):
        # Returns mock data
        return {"message": "Mock response"}
```

**After (Real):**
```python
class OnboardingClient:
    async def create_user(self, name, email, password):
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/tools/create_user",
                json={"name": name, "email": email, "password": password}
            )
            return response.json()
```

### 2. Each MCP: Add Manifest Endpoint

```python
@app.get("/manifest")
async def get_manifest():
    return {
        "name": "onboarding-mcp",
        "version": "1.0.0",
        "tools": [
            {
                "name": "create_user",
                "description": "Create a new user account",
                "parameters": {
                    "name": {"type": "string", "required": True},
                    "email": {"type": "string", "required": True}
                }
            }
        ]
    }
```

### 3. Each MCP: Tool Endpoints

```python
@router.post("/tools/create_user")
async def create_user(request: CreateUserRequest):
    user = await auth_service.create_user(
        name=request.name,
        email=request.email,
        password=request.password
    )
    return {"success": True, "data": {"user_id": user.id}}
```

---

## 🧪 Testing Your Setup

```bash
# Start all services
docker-compose up -d

# Test orchestrator
curl http://localhost:8100/health

# Test onboarding MCP
curl http://localhost:8001/health
curl http://localhost:8001/manifest

# Test activities MCP
curl http://localhost:8002/health
curl http://localhost:8002/manifest

# Test end-to-end flow
curl -X POST http://localhost:8100/chat \
  -H "Content-Type: application/json" \
  -d '{"user_id": "test-123", "text": "I want to sign up"}'
```

---

## ✅ Success Checklist

### Week 1 (Foundation)
- [ ] Monorepo directory created
- [ ] Common library with shared models
- [ ] Docker Compose configuration
- [ ] All services have Dockerfile
- [ ] .env.example created

### Week 2 (Onboarding MCP)
- [ ] Auth code extracted from my_onboarding_api
- [ ] Onboarding MCP running on port 8001
- [ ] MCP manifest created
- [ ] Tools endpoints working
- [ ] Health check passing

### Week 3 (Activities MCP)
- [ ] Activity code extracted from my_onboarding_api
- [ ] Activities MCP running on port 8002
- [ ] MCP manifest created
- [ ] Tools endpoints working
- [ ] Health check passing

### Week 4 (New Services)
- [ ] Rewards MCP created (port 8003)
- [ ] Approval MCP created (port 8004)
- [ ] Both services have manifests
- [ ] Basic functionality implemented

### Week 5 (Orchestrator)
- [ ] Mock clients replaced with real HTTP clients
- [ ] Intent detection working
- [ ] Routing to all MCPs working
- [ ] Error handling implemented

### Week 6 (Testing & Deploy)
- [ ] Integration tests passing
- [ ] All services deployed to staging
- [ ] Performance testing completed
- [ ] Documentation updated
- [ ] Production deployment successful

---

## 🚨 Common Mistakes to Avoid

❌ **Don't:** Keep business logic in orchestrator  
✅ **Do:** Orchestrator only detects intent and routes

❌ **Don't:** Have MCPs call each other directly  
✅ **Do:** All communication goes through orchestrator

❌ **Don't:** Share database connections between services  
✅ **Do:** Each service has its own connection pool

❌ **Don't:** Use same port for multiple services  
✅ **Do:** Assign unique ports (8100, 8001, 8002, etc.)

❌ **Don't:** Skip the common library  
✅ **Do:** Put shared code in common/ to avoid duplication

---

## 📚 Key Files to Create

### 1. Common Library
- `common/models/base.py` - Base response models
- `common/utils/validation.py` - Shared validation
- `common/utils/security.py` - Security utilities
- `common/config/settings.py` - Shared settings

### 2. Each MCP Service
- `app/main.py` - FastAPI app
- `app/tools/*.py` - Tool implementations
- `mcp-manifest.json` - Tool definitions
- `Dockerfile` - Container config
- `requirements.txt` - Dependencies

### 3. Orchestrator
- `app/main.py` - FastAPI app
- `app/routers/chat_router.py` - Chat endpoint
- `app/services/intent_detector.py` - Intent detection
- `app/services/*_client.py` - HTTP clients for MCPs

---

## 🎯 Quick Commands

```bash
# Start everything
docker-compose up -d

# View logs
docker-compose logs -f

# Restart a service
docker-compose restart mcp-onboarding

# Stop everything
docker-compose down

# Rebuild and restart
docker-compose up -d --build

# Run tests
pytest tests/integration/
```

---

## 📞 Need Help?

1. **Read the full guide:** `MONOREPO_MIGRATION_GUIDE.md`
2. **Check architecture:** `my_onboarding_api/architecture.md`
3. **Review code examples** in the full guide
4. **Test incrementally** - don't wait until the end

---

## 🎉 You're Ready!

Start with **Week 1** and follow the plan step by step. The full guide has all the code examples and detailed instructions you need.

**Good luck with your migration! 🚀**
