'use client'

import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { FileText, CheckCircle, Clock, XCircle, Star, User } from "lucide-react"

interface Submission {
    id: string
    title?: string
    description?: string
    text?: string
    status?: string
    submissionStatus?: string
    grade?: number
    feedback?: string
    createdAt?: string
    created_at?: string
    updatedAt?: string
    updated_at?: string
    submittedAt?: string
    submitted_at?: string
    gradedAt?: string
    graded_at?: string
    userId?: string
    user_id?: string
    createdBy?: string
    activityId?: string
    activity_id?: string
    projectId?: string
    isPublic?: boolean
    likeCount?: number
    viewCount?: number
    [key: string]: any
}

interface SubmissionListProps {
    submissions: Submission[]
}

export default function SubmissionList({ submissions }: SubmissionListProps) {
    if (!submissions || submissions.length === 0) {
        return null
    }

    const getStatusIcon = (status: string) => {
        const lowerStatus = (status || '').toLowerCase()
        if (lowerStatus.includes('graded') || lowerStatus.includes('approved')) {
            return <CheckCircle className="w-4 h-4" />
        }
        if (lowerStatus.includes('submitted') || lowerStatus.includes('inreview')) {
            return <Clock className="w-4 h-4" />
        }
        if (lowerStatus.includes('rejected') || lowerStatus.includes('returned')) {
            return <XCircle className="w-4 h-4" />
        }
        return <FileText className="w-4 h-4" />
    }

    const getStatusColor = (status: string) => {
        const lowerStatus = (status || '').toLowerCase()
        if (lowerStatus.includes('graded') || lowerStatus.includes('approved')) {
            return "default" // success/primary
        }
        if (lowerStatus.includes('submitted') || lowerStatus.includes('inreview')) {
            return "secondary" // pending
        }
        if (lowerStatus.includes('rejected')) {
            return "destructive" // error
        }
        if (lowerStatus.includes('draft')) {
            return "outline" // draft
        }
        return "outline"
    }

    const formatGrade = (grade?: number) => {
        if (grade === undefined || grade === null) return null
        return `${grade.toFixed(1)}%`
    }

    return (
        <div className="grid gap-3 w-full mt-3">
            {submissions.map((submission) => {
                // Map API fields to display fields
                const title = submission.title || submission.text || 'Untitled Submission'
                const description = submission.description
                const status = submission.status || submission.submissionStatus || 'SUBMITTED'
                const grade = submission.grade
                const feedback = submission.feedback
                const submittedDate = submission.submittedAt || submission.submitted_at || submission.createdAt || submission.created_at
                const gradedDate = submission.gradedAt || submission.graded_at
                const userId = submission.userId || submission.user_id || submission.createdBy
                const activityId = submission.activityId || submission.activity_id || submission.projectId
                const likeCount = submission.likeCount || 0
                const viewCount = submission.viewCount || 0

                return (
                    <Card 
                        key={submission.id || Math.random().toString()} 
                        className="p-3 hover:shadow-md transition-shadow bg-card/50"
                    >
                        <div className="flex items-start justify-between gap-3">
                            <div className="flex items-start gap-3 flex-1">
                                <div className="mt-1 p-2 rounded-full bg-primary/10 text-primary">
                                    {getStatusIcon(status)}
                                </div>
                                <div className="flex-1 min-w-0">
                                    <h4 className="font-semibold text-sm truncate">{title}</h4>
                                    
                                    {description && (
                                        <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                                            {description}
                                        </p>
                                    )}
                                    
                                    <div className="flex flex-wrap gap-2 mt-2">
                                        <Badge variant={getStatusColor(status) as any} className="text-[10px] px-1.5 h-5">
                                            {status}
                                        </Badge>
                                        
                                        {grade !== undefined && grade !== null && (
                                            <Badge variant="default" className="text-[10px] px-1.5 h-5 bg-green-600">
                                                <Star className="w-3 h-3 mr-1" />
                                                {formatGrade(grade)}
                                            </Badge>
                                        )}
                                        
                                        {activityId && (
                                            <Badge variant="outline" className="text-[10px] px-1.5 h-5">
                                                Activity: {activityId.substring(0, 8)}...
                                            </Badge>
                                        )}
                                    </div>
                                    
                                    <div className="flex flex-wrap gap-3 mt-2 text-xs text-muted-foreground">
                                        {submittedDate && (
                                            <span>
                                                📅 {new Date(submittedDate).toLocaleDateString()}
                                            </span>
                                        )}
                                        
                                        {gradedDate && (
                                            <span>
                                                ✅ Graded: {new Date(gradedDate).toLocaleDateString()}
                                            </span>
                                        )}
                                        
                                        {(likeCount > 0 || viewCount > 0) && (
                                            <span>
                                                👁️ {viewCount} • ❤️ {likeCount}
                                            </span>
                                        )}
                                    </div>
                                    
                                    {feedback && (
                                        <div className="mt-2 p-2 bg-muted/50 rounded text-xs">
                                            <p className="font-medium text-muted-foreground mb-1">Feedback:</p>
                                            <p className="text-foreground line-clamp-2">{feedback}</p>
                                        </div>
                                    )}
                                </div>
                            </div>
                            
                            {userId && (
                                <div className="flex-shrink-0">
                                    <div className="p-1.5 rounded-full bg-secondary/50">
                                        <User className="w-3 h-3 text-secondary-foreground" />
                                    </div>
                                </div>
                            )}
                        </div>
                    </Card>
                )
            })}
        </div>
    )
}
