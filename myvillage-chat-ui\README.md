# MyVillage AI Chat UI

A ChatGPT-like interface for MyVillage AI services built with Next.js, TypeScript, and Tailwind CSS.

## Features

- 💬 Real-time chat interface
- 🎨 ChatGPT-inspired design
- 🔄 Intent detection display
- 🎯 MCP service routing visualization
- 📱 Responsive design
- ⚡ Fast and lightweight

## Prerequisites

- Node.js 18+ 
- npm or yarn
- MyVillage AI backend services running (see `myvillage-ai-monorepo`)

## Quick Start

### 1. Install Dependencies

```bash
cd myvillage-chat-ui
npm install
```

### 2. Start Backend Services

Make sure your backend services are running:

```bash
cd ../myvillage-ai-monorepo
./start-all.sh
```

### 3. Run the Chat UI

```bash
cd ../myvillage-chat-ui
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## Environment Variables

Create a `.env.local` file (already created):

```env
NEXT_PUBLIC_API_URL=http://localhost:8100
```

## Project Structure

```
myvillage-chat-ui/
├── app/
│   ├── layout.tsx          # Root layout
│   ├── page.tsx            # Home page
│   └── globals.css         # Global styles
├── components/
│   ├── ChatInterface.tsx   # Main chat component
│   ├── ChatMessage.tsx     # Message bubble
│   ├── ChatInput.tsx       # Input field
│   └── Sidebar.tsx         # Sidebar with history
├── package.json
├── tsconfig.json
├── tailwind.config.ts
└── next.config.js
```

## Testing the Features

### Test Activities Intent
```
User: "Show me available activities"
```

### Test Rewards Intent
```
User: "Check my rewards"
```

### Test Onboarding Intent
```
User: "I want to sign up"
```

### Test General Chat
```
User: "Hello, how are you?"
```

## API Integration

The chat UI connects to the orchestrator at `http://localhost:8100/chat`:

```typescript
POST /chat
{
  "user_id": "user-123",
  "text": "Show me activities"
}
```

Response includes:
- `response`: AI-generated response
- `intent`: Detected intent (activities, rewards, onboarding, general)
- `mcp_service`: Which MCP service handled the request

## Customization

### Colors

Edit `tailwind.config.ts`:

```typescript
colors: {
  primary: '#10a37f',    // Main brand color
  secondary: '#19c37d',  // Hover states
}
```

### API URL

Edit `.env.local`:

```env
NEXT_PUBLIC_API_URL=http://your-api-url
```

## Build for Production

```bash
npm run build
npm start
```

## Troubleshooting

### Backend not responding
- Make sure all backend services are running: `./start-all.sh`
- Check health: `curl http://localhost:8100/health`

### CORS errors
- Backend already has CORS enabled for localhost:3000
- Check orchestrator logs: `tail -f logs/orchestrator.log`

### Port 3000 already in use
```bash
# Use a different port
PORT=3001 npm run dev
```

## Tech Stack

- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **HTTP Client**: Axios
- **UI**: Custom components

## License

MIT
