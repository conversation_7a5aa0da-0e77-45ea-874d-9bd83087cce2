# Required API Keys
HF_TOKEN=your_huggingface_token_here
MODEL_NAME=jalpesh088/myvillage-onboarding-intent
GEMINI_API_KEY=your_gemini_api_key_here

# Optional Configuration
DEBUG=false
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000

# MCP Server Configuration
API_BASE_URL=http://localhost:8000
MCP_HOST=0.0.0.0
MCP_PORT=5000
RUN_MODE=both

# Orchestrator Configuration
ORCHESTRATOR_URL=http://localhost:8100/chat
USE_ORCHESTRATOR=true

# Security Settings
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
CONFIDENCE_THRESHOLD=0.85

# External Services
AUTH_API_URL=https://6s0bso8sri.execute-api.us-east-1.amazonaws.com/amplifydev/setAuthToken
SIGNUP_API_URL=https://6s0bso8sri.execute-api.us-east-1.amazonaws.com/amplifydev/userCreatev2
AUTH_API_TIMEOUT=10
API_BEARER_TOKEN=your_bearer_token_here

# CORS Settings (optional)
# CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1

# Optional: Only needed if using temporary credentials
# AWS_SESSION_TOKEN=your_session_token

# DynamoDB Configuration
DYNAMODB_CITIES_TABLE=cities

# Optional: For local development with DynamoDB Local
# DYNAMODB_ENDPOINT_URL=http://localhost:8000
