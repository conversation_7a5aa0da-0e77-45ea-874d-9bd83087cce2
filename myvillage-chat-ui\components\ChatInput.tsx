'use client'

import { KeyboardEvent } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Send, Loader2, LucideIcon } from 'lucide-react'
import { cn } from '@/lib/utils'

interface QuickAction {
  icon: LucideIcon
  label: string
  message: string
  description: string
}

interface ChatInputProps {
  onSend: (message: string) => void
  isLoading: boolean
  quickActions?: QuickAction[]
  input: string
  setInput: (value: string) => void
}

export default function ChatInput({ onSend, isLoading, quickActions = [], input, setInput }: ChatInputProps) {
  const handleSend = () => {
    if (input.trim() && !isLoading) {
      onSend(input)
      setInput('')
    }
  }

  const handleKeyPress = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  const handleQuickAction = (message: string) => {
    setInput(message)
  }

  return (
    <div className="relative border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container max-w-4xl mx-auto p-4">
        <div className="flex items-end gap-2">
          <div className="flex-1 relative">
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Send a message..."
              disabled={isLoading}
              rows={1}
              className={cn(
                "w-full bg-muted/50 text-foreground rounded-lg px-4 py-3 pr-12",
                "resize-none focus:outline-none focus:ring-2 focus:ring-ring",
                "disabled:opacity-50 disabled:cursor-not-allowed",
                "placeholder:text-muted-foreground",
                "transition-all duration-200"
              )}
              style={{ minHeight: '52px', maxHeight: '200px' }}
            />
          </div>

          <Button
            onClick={handleSend}
            disabled={!input.trim() || isLoading}
            size="lg"
            className="h-[52px] px-6"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                Sending
              </>
            ) : (
              <>
                <Send className="w-5 h-5 mr-2" />
                Send
              </>
            )}
          </Button>
        </div>

        {/* Quick Actions - Positioned below the input */}
        {!input && quickActions.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-3">
            {quickActions.map((action) => (
              <Button
                key={action.label}
                variant="outline"
                size="sm"
                onClick={() => handleQuickAction(action.message)}
                className="h-7 text-xs px-3 bg-background/50 hover:bg-background/80 border-muted-foreground/20 hover:border-primary transition-all"
              >
                <action.icon className="w-3 h-3 mr-1.5" />
                {action.label}
              </Button>
            ))}
          </div>
        )}

        <p className="text-[10px] text-muted-foreground mt-2 text-center opacity-70">
          Press Enter to send, Shift+Enter for new line
        </p>
      </div>
    </div>
  )
}
