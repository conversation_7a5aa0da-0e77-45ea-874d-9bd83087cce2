"""
Security utilities for the application.
This version expects a user ID in the Authorization header.
Example: Authorization: Bearer user-123
"""
from fastapi import Depends, HTTPException, status, Header
from typing import Optional

async def get_current_user(authorization: str = Header(..., description="Format: Bearer user-id")):
    """
    Get current user from Authorization header.
    Expected format: 'Bearer user-id'
    """
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authorization header is missing",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        # Expecting format: "Bearer user-id"
        scheme, user_id = authorization.split()
        if scheme.lower() != 'bearer':
            raise ValueError("Invalid authentication scheme")
            
        # Here you could add additional validation of the user_id
        # For example, check if the user exists in your database
        
        return {"user_id": user_id}
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authorization header format. Expected 'Bearer user-id'",
            headers={"WWW-Authenticate": "Bearer"},
        )
