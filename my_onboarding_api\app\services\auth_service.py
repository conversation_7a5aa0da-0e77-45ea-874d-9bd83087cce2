"""
Authentication service for external API integration.

This module handles authentication with external services.
"""

from typing import Dict, Any, Optional
import requests
from requests.exceptions import RequestException, Timeout

from ..core.config import settings
from ..core.logging import get_logger
from ..core.exceptions import AuthenticationError, ExternalServiceError
from ..models.requests import LoginRequest, SignupRequest
from .city_service import get_city_service

logger = get_logger(__name__)


class AuthenticationService:
    """Service for handling authentication with external APIs."""
    
    def __init__(self):
        """Initialize the authentication service."""
        self.auth_url = settings.auth_api_url
        self.signup_url = settings.signup_api_url
        self.timeout = settings.auth_api_timeout
        self.bearer_token = settings.api_bearer_token
    
    def login(self, login_data: LoginRequest) -> Dict[str, Any]:
        """
        Authenticate user with external API.
        
        Args:
            login_data: Login request data
            
        Returns:
            Authentication response
            
        Raises:
            AuthenticationError: If authentication fails
            ExternalServiceError: If the API call fails
        """
        try:
            logger.info(f"Attempting login for user: {login_data.email}")
            
            # Prepare request data
            request_data = {
                "email": login_data.email,
                "password": login_data.password
            }
            
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "OnboardingAPI/1.0"
            }
            
            # Make the API call
            response = requests.post(
                self.auth_url,
                json=request_data,
                headers=headers,
                timeout=self.timeout
            )
            
            # Parse response
            try:
                response_data = response.json()
            except ValueError as e:
                logger.error(f"Invalid JSON response from auth API: {str(e)}")
                raise ExternalServiceError(
                    service_name="authentication",
                    message="Invalid response format from authentication service",
                    status_code=response.status_code
                )
            
            # Check for successful authentication
            if response.status_code == 200 and isinstance(response_data, dict):
                if response_data.get('success') is True:
                    logger.info(f"Login successful for user: {login_data.email}")
                    
                    # Extract user data from response
                    user_data = response_data.get('data', {})
                    
                    # Extract username from response or email
                    username = (
                        user_data.get('name') or 
                        user_data.get('firstName') or 
                        login_data.email.split('@')[0] if '@' in login_data.email else "User"
                    )
                    
                    # Extract user stats from response or use defaults
                    mvp_tokens = user_data.get('mvpPoints', user_data.get('mvp_points', 250))
                    impact_score = user_data.get('impactScore', user_data.get('impact_score', 87))
                    activities_completed = user_data.get('activitiesCompleted', user_data.get('activities_completed', 15))
                    pending_activities = user_data.get('pendingActivities', user_data.get('pending_activities', 3))
                    
                    # Create a detailed success message with user stats
                    success_message = (
                        f"✅ Login successful!\n\n"
                        f"Loading your dashboard...\n\n"
                        f"🎉 Welcome back, {username}!\n\n"
                        "Here's your current status:\n\n"
                        f"🪙 MVP Tokens: {mvp_tokens}\n"
                        f"📊 Impact Score: {impact_score}%\n"
                        f"✅ Activities Completed: {activities_completed}\n"
                        f"⏳ Pending Activities: {pending_activities}"
                    )
                    
                    # Prepare enhanced data with user information
                    enhanced_data = {
                        "user_id": user_data.get('id') or user_data.get('_id') or user_data.get('userId'),
                        "email": user_data.get('email', login_data.email),
                        "name": username,
                        "role": user_data.get('role') or user_data.get('assignedRole') or 'user',
                        "mvp_points": mvp_tokens,
                        "impact_score": impact_score,
                        "activities_completed": activities_completed,
                        "pending_activities": pending_activities,
                        "activities": user_data.get('activities', []),
                        "is_stakeholder": user_data.get('isStakeholder', False),
                    }
                    
                    return {
                        "status": "success",
                        "message": success_message,
                        "data": enhanced_data
                    }
                
                # Authentication failed - raise with exact response body and status
                error_msg = response_data.get('message', response_data)
                logger.warning(f"Login failed for user {login_data.email}: {error_msg}")

                # Attach the full response_data so callers can inspect or forward it
                raise AuthenticationError(
                    message=str(error_msg),
                    details={
                        "email": login_data.email,
                        "response_data": response_data
                    },
                    status_code=response.status_code
                )
            
            # Unexpected response - include response body and status to help callers
            logger.error(f"Unexpected response from auth API: {response.status_code}")
            raise ExternalServiceError(
                service_name="authentication",
                message="Unexpected response from authentication service",
                status_code=response.status_code,
                details={"response_data": response_data}
            )
            
        except Timeout:
            logger.error(f"Authentication API timeout for user: {login_data.email}")
            raise ExternalServiceError(
                service_name="authentication",
                message="Authentication service timeout",
                details={"timeout": self.timeout}
            )
            
        except RequestException as e:
            logger.error(f"Authentication API request failed: {str(e)}")
            raise ExternalServiceError(
                service_name="authentication",
                message=f"Authentication service error: {str(e)}"
            )
    
    def signup(self, signup_data: SignupRequest) -> Dict[str, Any]:
        """
        Register a new user with external API.

        Args:
            signup_data: Signup request data

        Returns:
            Signup response

        Raises:
            AuthenticationError: If signup fails
            ExternalServiceError: If the API call fails
        """
        try:
            logger.info(f"Attempting signup for user: {signup_data.email}")

            # Handle city name to cityId conversion as fallback
            city_id = signup_data.cityId
            cities_array = signup_data.citiesArray

            # If cityId is not provided but cityName is, convert it
            if not city_id and hasattr(signup_data, 'cityName') and signup_data.cityName:
                city_service = get_city_service()
                city_id = city_service.validate_city_name_and_get_id(signup_data.cityName)
                if not city_id:
                    raise AuthenticationError(f"Invalid city name: {signup_data.cityName}")
                logger.info(f"Converted city name '{signup_data.cityName}' to cityId: {city_id}")

            # If citiesArray is not provided but cityNamesArray is, convert it
            if not cities_array and hasattr(signup_data, 'cityNamesArray') and signup_data.cityNamesArray:
                city_service = get_city_service()
                validation_results = city_service.validate_city_names_and_get_ids(signup_data.cityNamesArray)
                invalid_cities = [name for name, city_id in validation_results.items() if city_id is None]
                if invalid_cities:
                    raise AuthenticationError(f"Invalid city names: {', '.join(invalid_cities)}")
                cities_array = [city_id for city_id in validation_results.values() if city_id is not None]
                logger.info(f"Converted city names {signup_data.cityNamesArray} to cityIds: {cities_array}")

            # Prepare request data with all the parameters
            request_data = {
                "cityId": city_id,
                "firstName": signup_data.firstName,
                "lastName": signup_data.lastName,
                "email": signup_data.email,
                "phoneNumber": signup_data.phoneNumber,
                "name": signup_data.name,
                "role": signup_data.role,
                "assignedRole": signup_data.assignedRole,
                "registeredFrom": signup_data.registeredFrom,
                "isStakeholder": signup_data.isStakeholder,
                "userAddedFrom": signup_data.userAddedFrom,
                "citiesArray": cities_array,
                "isAssociated": signup_data.isAssociated,
                "gender": signup_data.gender,
                "streetAddressOne": signup_data.streetAddressOne,
                "streetAddressTwo": signup_data.streetAddressTwo,
                "birthday": signup_data.birthday,
                "city": signup_data.city,
                "state": signup_data.state,
                "zipCode": signup_data.zipCode,
                "type": signup_data.type,
                "createdBy": signup_data.createdBy,
                "userType": signup_data.userType,
                "password": signup_data.password
            }

            # Set default values for required fields if they're None
            if request_data.get('role') is None:
                request_data['role'] = 'MEMBER'
            if request_data.get('assignedRole') is None:
                request_data['assignedRole'] = 'MEMBER'
            if request_data.get('userType') is None:
                request_data['userType'] = 'loginUser'
                
            # Remove None values for other fields, but keep the ones we just set
            request_data = {k: v for k, v in request_data.items() if v is not None or k in ['role', 'assignedRole', 'userType']}

            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "OnboardingAPI/1.0"
            }

            # Add Authorization header if bearer token is available
            if self.bearer_token:
                headers["Authorization"] = f"Bearer {self.bearer_token}"

            # Log the request payload for debugging
            logger.info(
                "Sending signup request to API. "
                f"URL: {self.signup_url}\n"
                "Headers: " + ", ".join(f"{k}: {v}" for k, v in headers.items()) + "\n"
                f"Payload: {request_data}"
            )

            # Make the API call
            response = requests.post(
                self.signup_url,
                json=request_data,
                headers=headers,
                timeout=self.timeout
            )

            # Parse response
            try:
                response_data = response.json()
                logger.debug(f"Signup API response: {response.status_code} - {response_data}")
            except ValueError:
                error_msg = f"Invalid JSON response from signup API: {response.text}"
                logger.error(error_msg)
                response_data = {"error": "Invalid response format from server"}
                raise ExternalServiceError(
                    service_name="signup",
                    message=error_msg,
                    status_code=response.status_code,
                    details={"response_text": response.text}
                )

            # Check for successful signup
            if response.status_code == 200 and isinstance(response_data, dict):
                if response_data.get('success') is True or response.status_code == 200:
                    logger.info(f"Signup successful for user: {signup_data.email}")

                    # Extract user data from response (don't include password)
                    user_data = {
                        "name": signup_data.name,
                        "firstName": signup_data.firstName,
                        "lastName": signup_data.lastName,
                        "email": signup_data.email,
                        "phoneNumber": signup_data.phoneNumber,
                        "cityId": signup_data.cityId,
                        "city": signup_data.city,
                        "role": signup_data.role,
                    }

                    # Remove None values from user data
                    user_data = {k: v for k, v in user_data.items() if v is not None}

                    return {
                        "status": "success",
                        "message": "Signup completed successfully",
                        "user": user_data,
                        "data": response_data
                    }

                # Signup failed - extract error message from different possible response formats
                error_msg = (
                    response_data.get('message') or 
                    response_data.get('error') or 
                    response_data.get('detail') or 
                    'Signup failed. Please check your information and try again.'
                )
                
                # Log detailed error information
                logger.warning(
                    f"Signup failed for user {signup_data.email}. "
                    f"Status: {response.status_code}, "
                    f"Response: {response_data}"
                )

                raise AuthenticationError(
                    message=error_msg,
                    details={
                        "email": signup_data.email,
                        "status_code": response.status_code,
                        "response_data": response_data
                    }
                )

            # Handle non-200 responses
            error_msg = response_data.get('message') or response_data.get('error') or f"Unexpected status code: {response.status_code}"
            logger.error(
                f"Unexpected response from signup API. "
                f"Status: {response.status_code}, "
                f"Response: {response_data}"
            )
            
            raise ExternalServiceError(
                service_name="signup",
                message=error_msg,
                status_code=response.status_code,
                details={"response_data": response_data}
            )

        except Timeout:
            logger.error(f"Signup API timeout for user: {signup_data.email}")
            raise ExternalServiceError(
                service_name="signup",
                message="Signup service timeout",
                details={"timeout": self.timeout}
            )

        except RequestException as e:
            logger.error(f"Signup API request failed: {str(e)}")
            raise ExternalServiceError(
                service_name="signup",
                message=f"Signup service error: {str(e)}"
            )
    
    def validate_credentials(self, email: str, password: str) -> bool:
        """
        Validate user credentials format.
        
        Args:
            email: User email
            password: User password
            
        Returns:
            True if credentials format is valid
        """
        if not email or "@" not in email:
            return False
        
        if not password or len(password.strip()) == 0:
            return False
        
        return True


# Global service instance
auth_service = AuthenticationService()
