"""
Intent classification service using Hugging Face transformers.

This module provides intent classification functionality.
"""

import os
from typing import List, Optional
from transformers import pipeline, Pipeline

from ..core.config import settings
from ..core.logging import get_logger
from ..core.exceptions import IntentClassificationError, ConfigurationError
from ..models.responses import IntentClassificationResult

logger = get_logger(__name__)


class IntentClassificationService:
    """Service for intent classification using Hugging Face models."""
    
    def __init__(self):
        """Initialize the intent classification service."""
        self._pipeline: Optional[Pipeline] = None
        self._initialize_pipeline()
    
    def _initialize_pipeline(self) -> None:
        """Initialize the Hugging Face pipeline."""
        try:
            # Set the Hugging Face token
            os.environ["HUGGINGFACEHUB_API_TOKEN"] = settings.hf_token
            
            logger.info(f"Initializing intent classification pipeline with model: {settings.model_name}")
            
            # Create the pipeline
            self._pipeline = pipeline(
                "text-classification",
                model=settings.model_name,
                top_k=None,  # Return all scores (replaces deprecated return_all_scores=True)
                device=-1  # Use CPU to avoid GPU issues
            )
            
            logger.info("Intent classification pipeline initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize intent classification pipeline: {str(e)}")
            raise ConfigurationError(
                f"Failed to initialize intent classification: {str(e)}",
                details={"model_name": settings.model_name}
            )
    
    def classify_intent(self, text: str) -> List[IntentClassificationResult]:
        """
        Classify the intent of the given text.
        
        Args:
            text: Input text to classify
            
        Returns:
            List of intent classification results
            
        Raises:
            IntentClassificationError: If classification fails
        """
        logger.info("[INTENT] Starting intent classification")
        
        if not self._pipeline:
            error_msg = "[ERROR] Intent classification pipeline not initialized"
            logger.error(error_msg)
            raise IntentClassificationError(error_msg)
        
        if not text or not text.strip():
            error_msg = "[ERROR] Input text cannot be empty"
            logger.error(error_msg)
            raise IntentClassificationError(error_msg)
        
        try:
            logger.info(f"[INTENT] Classifying intent for text: {text[:100]}{'...' if len(text) > 100 else ''}")
            logger.debug(f"[INTENT] Full text length: {len(text)} characters")

            # Get predictions
            logger.debug("[INTENT] Calling pipeline with text")
            raw_results = self._pipeline(text.strip())
            logger.info("[INTENT] Pipeline call completed")

            logger.debug(f"[INTENT] Raw pipeline output type: {type(raw_results)}")
            logger.debug(f"[INTENT] Raw pipeline output: {raw_results}")

            # Handle different output formats from Hugging Face pipeline
            if isinstance(raw_results, list):
                logger.debug("[INTENT] Processing list output from pipeline")
                # Handle double-nested lists from batch processing
                if len(raw_results) > 0 and isinstance(raw_results[0], list):
                    results = raw_results[0]  # Take first batch result
                    logger.info(f"[INTENT] Using nested batch format. Results count: {len(results)}")
                    logger.info(f"[INTENT] Batch results: {results}")
                elif len(raw_results) > 0 and (isinstance(raw_results[0], dict) or (isinstance(raw_results[0], list) and len(raw_results[0]) > 0 and isinstance(raw_results[0][0], dict))):
                    # Handle either direct list of dicts or single-item nested list
                    results = raw_results[0] if isinstance(raw_results[0], list) else raw_results
                    logger.info("[INTENT] Using direct result format")
                    logger.debug(f"[INTENT] Result count: {len(results)}")
                else:
                    # Empty results or unexpected format
                    if len(raw_results) == 0:
                        warning_msg = "[WARNING] Pipeline returned empty results"
                        logger.warning(warning_msg)
                        # Return a default "unknown" result
                        return [IntentClassificationResult(label="unknown", score=0.0)]
                    else:
                        error_msg = f"[ERROR] Unexpected result item type: {type(raw_results[0])}"
                        logger.error(error_msg)
                        raise IntentClassificationError(error_msg)
            else:
                # Unexpected format
                logger.error(f"Unexpected pipeline output format: {type(raw_results)}")
                raise IntentClassificationError(f"Unexpected pipeline output format: {type(raw_results)}")

            # Validate that results is a list of dictionaries
            if not isinstance(results, list):
                raise IntentClassificationError(f"Expected list of results, got {type(results)}")

            # Convert to our response model
            intent_results = []
            for i, result in enumerate(results):
                if not isinstance(result, dict):
                    logger.error(f"Result {i} is not a dictionary: {type(result)} - {result}")
                    raise IntentClassificationError(f"Expected dictionary result, got {type(result)}")

                if "label" not in result or "score" not in result:
                    logger.error(f"Result {i} missing required keys: {result}")
                    raise IntentClassificationError(f"Result missing required keys 'label' or 'score': {result}")

                intent_results.append(
                    IntentClassificationResult(
                        label=result["label"],
                        score=result["score"]
                    )
                )

            # Sort by confidence score (highest first)
            intent_results.sort(key=lambda x: x.score, reverse=True)

            logger.debug(f"Intent classification completed. Top intent: {intent_results[0].label} ({intent_results[0].score:.3f})")

            return intent_results
            
        except Exception as e:
            logger.error(f"Intent classification failed: {str(e)}")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise IntentClassificationError(
                f"Failed to classify intent: {str(e)}",
                details={
                    "input_text": text[:100],
                    "exception_type": str(type(e)),
                    "traceback": traceback.format_exc()
                }
            )
    
    def get_top_intent(self, text: str, threshold: Optional[float] = None) -> str:
        """
        Get the top intent for the given text. If onboarding intent is unknown,
        falls back to activity intent classification.
        
        Args:
            text: Input text to classify
            threshold: Confidence threshold (uses settings default if not provided)
            
        Returns:
            Top intent label or "unknown" if below all thresholds
        """
        base_threshold = threshold or settings.confidence_threshold
        
        # First try onboarding intents
        results = self.classify_intent(text)
        logger.debug(f"Results: get_top_intent: {results}")
        
        if not results:
            return "unknown"
        
        top_result = results[0]

        # Special handling for signup intent - require moderately higher confidence
        if top_result.label == "signup":
            signup_threshold = max(0.90, base_threshold)  # At least 0.90 confidence for signup
            logger.debug(f"Signup intent detected, using threshold: {signup_threshold}")
            
            if top_result.score >= signup_threshold:
                return top_result.label
            else:
                logger.debug(f"Signup intent confidence {top_result.score:.3f} below required threshold {signup_threshold}")
                # If signup is the top intent but below threshold, check next best intent
                if len(results) > 1 and results[1].score >= base_threshold:
                    return results[1].label
        
        # For all other onboarding intents, use the base threshold
        if top_result.score >= base_threshold:
            return top_result.label
            
        # If we reach here, onboarding intent was not identified with sufficient confidence
        # Try activity intent classification
        logger.debug("Onboarding intent unknown, trying activity intent classification")
        from .activity_service import get_activity_service
        
        try:
            activity_service = get_activity_service()
            # Get all activity intents with their scores
            activity_results = activity_service.classify_intent(text)
            
            if activity_results and activity_results[0].score >= (threshold or settings.confidence_threshold):
                activity_intent = activity_results[0].label
                logger.info(f"Activity intent identified with sufficient confidence: {activity_intent} ({activity_results[0].score:.3f})")
                return f"activity_{activity_intent}"  # Prefix to distinguish from onboarding intents
            else:
                logger.debug(f"No activity intent met the confidence threshold. Top intent: {activity_results[0].label if activity_results else 'none'} (score: {activity_results[0].score if activity_results else 0:.3f})")
            
        except ImportError:
            logger.warning("Activity service not available, skipping activity intent classification")
        except Exception as e:
            logger.error(f"Activity intent classification failed: {str(e)}")
            # Fall through to return unknown
            
        return "unknown"
    
    def is_pipeline_ready(self) -> bool:
        """Check if the pipeline is ready for use."""
        return self._pipeline is not None

    def test_pipeline_output(self, text: str) -> dict:
        """
        Test the raw pipeline output for debugging.

        Args:
            text: Input text to test

        Returns:
            Dictionary with debug information
        """
        if not self._pipeline:
            return {"error": "Pipeline not initialized"}

        try:
            raw_results = self._pipeline(text.strip())
            return {
                "input": text,
                "raw_results_type": str(type(raw_results)),
                "raw_results": raw_results,
                "success": True
            }
        except Exception as e:
            return {
                "input": text,
                "error": str(e),
                "success": False
            }


# Global service instance
intent_service = IntentClassificationService()
