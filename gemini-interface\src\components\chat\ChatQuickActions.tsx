'use client';

interface ChatQuickActionsProps {
  onAction: (action: string) => void;
  isLoggingIn?: boolean;
  key?: string;
}

export default function ChatQuickActions({ onAction, isLoggingIn = false }: ChatQuickActionsProps) {
  const quickActions = [
    {
      emoji: '🔑',
      label: 'Login',
      prompt: 'I want to log into my account.'
    },
    {
      emoji: '📝',
      label: 'Sign Up',
      prompt: 'I want to create a new account.'
    },
    {
      emoji: '❓',
      label: 'Help',
      prompt: 'What can you help me with?'
    }
  ];

  // Don't show quick actions during login flow
  if (isLoggingIn) {
    return null;
  }

  return (
    <div className="w-full max-w-3xl mx-auto px-4 mb-4">
      <div className="flex flex-wrap justify-center gap-3">
        {quickActions.map((action, index) => (
          <button
            key={index}
            onClick={() => onAction(action.prompt)}
            className="flex items-center gap-2 px-4 py-2 bg-card border border-border rounded-full text-sm font-medium text-foreground hover:bg-muted transition-colors duration-200 shadow-sm whitespace-nowrap"
          >
            <span className="text-base">{action.emoji}</span>
            <span>{action.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
}
