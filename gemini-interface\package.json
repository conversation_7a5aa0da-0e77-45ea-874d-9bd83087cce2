{"name": "gemini-interface", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-toast": "^1.2.15", "@tanstack/react-query": "^5.90.7", "@tanstack/react-query-devtools": "^5.90.2", "@types/axios": "^0.9.36", "axios": "^1.13.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.545.0", "next": "15.5.5", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.24", "@types/react": "^19.2.2", "@types/react-dom": "^19.2.2", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.5.5", "postcss": "^8.5.6", "tailwindcss": "^4.1.14", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}