"""
Main FastAPI application.

This module creates and configures the FastAPI application with all middleware,
routes, and error handlers.
"""

from contextlib import asynccontextmanager
from fastapi import Fast<PERSON><PERSON>, HTTPException, Request, Depends
from fastapi import status  # Moved to separate import for clarity
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from .core.config import settings
from .core.logging import setup_logging, get_logger
from .core.exceptions import OnboardingAPIException
from .schemas import ApiResponse
from .api import app as api_app
from .api.routes import router as api_router
from .api.endpoints.activities import router as activities_router
from .api.endpoints.submissions import router as submissions_router
from .middleware.logging_middleware import LoggingMiddleware
from .middleware.security_middleware import RateLimitMiddleware, SecurityHeadersMiddleware
from .services.intent_service import intent_service
from .services.gemini_service import gemini_service

# Set up logging
setup_logging()
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager.
    
    Handles startup and shutdown events.
    """
    # Startup
    logger.info("Starting Onboarding API application")
    logger.info(f"Configuration: Debug={settings.debug}, Log Level={settings.log_level}")
    
    try:
        # Initialize services
        logger.info("Initializing services...")
        
        # Verify services are ready
        from .services.intent_service import intent_service
        from .services.gemini_service import gemini_service
        
        if not intent_service.is_pipeline_ready():
            logger.error("Intent classification service not ready")
            raise RuntimeError("Intent classification service initialization failed")
        
        if not gemini_service.is_service_ready():
            logger.error("Gemini service not ready")
            raise RuntimeError("Gemini service initialization failed")
        
        logger.info("All services initialized successfully")
        
        yield  # Application runs here
        
    except Exception as e:
        logger.error(f"Error during application startup: {str(e)}", exc_info=True)
        raise
    finally:
        # Shutdown
        logger.info("Shutting down Onboarding API application")
        # Clean up resources if needed


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        Configured FastAPI application
    """
    # Create FastAPI app
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="API for onboarding intent classification and Gemini chat functionality",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        lifespan=lifespan
    )
    
    # Add CORS middleware with enhanced configuration
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=settings.cors_allow_credentials,
        allow_methods=["*"],  # Allow all methods
        allow_headers=["*"],  # Allow all headers
        expose_headers=["*"],  # Expose all headers
        max_age=600,  # Cache preflight requests for 10 minutes
    )
    
    # Add error handling middleware for better error reporting
    @app.middleware("http")
    async def catch_exceptions_middleware(request: Request, call_next):
        try:
            return await call_next(request)
        except Exception as e:
            import traceback
            logger.error(f"Unhandled exception: {str(e)}\n{traceback.format_exc()}")
            return JSONResponse(
                status_code=500,
                content={"detail": "Internal server error", "error": str(e), "traceback": traceback.format_exc() if settings.debug else None}
            )
    
    # Add debug logging for CORS
    @app.middleware("http")
    async def log_cors_headers(request: Request, call_next):
        # Log request details
        logger.debug(f"Incoming request: {request.method} {request.url}")
        logger.debug(f"Request headers: {dict(request.headers)}")
        
        # Process the request
        response = await call_next(request)
        
        # Log response headers
        logger.debug(f"Response headers: {dict(response.headers)}")
        return response
    
    # Add custom middleware (order matters!)
    app.add_middleware(SecurityHeadersMiddleware)
    app.add_middleware(RateLimitMiddleware)
    app.add_middleware(LoggingMiddleware)
    
    # Include API routes
    app.include_router(api_router, prefix=settings.API_V1_STR, tags=["onboarding"])
    app.include_router(activities_router, prefix=f"{settings.API_V1_STR}/activities", tags=["activities"])
    app.include_router(submissions_router, prefix=f"{settings.API_V1_STR}/submissions", tags=["submissions"])
    
    # Add exception handlers
    add_exception_handlers(app)
    
    # Add health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint with service status."""
        # Check DynamoDB connection
        db_status = "ok"
        try:
            # Test DynamoDB connection by listing tables
            from .core.database import get_db
            db = get_db()
            db.tables.all()
        except Exception as e:
            logger.error(f"DynamoDB health check failed: {str(e)}")
            db_status = f"error: {str(e)}"
        
        # Test intent service with a simple message
        intent_status = {
            "ready": intent_service.is_pipeline_ready(),
            "test_result": "not_tested",
            "error": None
        }

        if intent_status["ready"]:
            try:
                test_results = intent_service.classify_intent("hello")
                intent_status["test_result"] = "working" if test_results else "failed"
            except Exception as e:
                intent_status["error"] = str(e)
                intent_status["test_result"] = "error"

        return {
            "status": "healthy",
            "version": settings.app_version,
            "services": {
                "intent_classification": intent_status,
                "gemini": gemini_service.is_service_ready(),
                "dynamodb": db_status
            }
        }
    
    return app


def add_exception_handlers(app: FastAPI) -> None:
    """
    Add custom exception handlers to the application.
    
    Args:
        app: FastAPI application
    """
    
    @app.exception_handler(OnboardingAPIException)
    async def onboarding_exception_handler(request: Request, exc: OnboardingAPIException):
        """Handle custom application exceptions."""
        logger.error(f"Application exception: {str(exc)}")
        response = ApiResponse.error(
            message=str(exc),
            code=exc.code if hasattr(exc, 'code') else "application_error",
            details=exc.details if hasattr(exc, 'details') else {}
        )
        return JSONResponse(
            status_code=getattr(exc, 'status_code', 500),
            content=response.model_dump()
        )
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        """Handle request validation errors."""
        logger.warning(f"Validation error: {str(exc)}")
        errors = {}
        for error in exc.errors():
            field = ".".join(str(loc) for loc in error['loc'])
            errors[field] = error['msg']
        
        response = ApiResponse.error(
            message="Validation error",
            code="validation_error",
            details=errors
        )
        return JSONResponse(
            status_code=422,
            content=response.model_dump()
        )
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """Handle HTTP exceptions with proper formatting."""
        logger.warning(f"HTTP exception: {exc.status_code} - {exc.detail}")
        
        # Ensure we have a valid status code
        status_code = exc.status_code if exc.status_code is not None else status.HTTP_500_INTERNAL_SERVER_ERROR
        
        if isinstance(exc.detail, dict):
            error_detail = exc.detail
            # Ensure the status code in the detail matches the response status code
            error_detail["status_code"] = status_code
        else:
            error_detail = {
                "error": exc.__class__.__name__,
                "message": str(exc.detail) if exc.detail else "An error occurred",
                "status_code": status_code
            }
            
        return JSONResponse(
            status_code=status_code,
            content=error_detail
        )
    
    @app.exception_handler(ValueError)
    async def value_error_handler(request: Request, exc: ValueError):
        """Handle ValueError exceptions."""
        logger.warning(f"Value error: {str(exc)}")
        response = ApiResponse.error(
            message=str(exc),
            code="value_error",
            details={"type": "ValueError"}
        )
        return JSONResponse(
            status_code=400,
            content=response.model_dump()
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle unexpected exceptions."""
        logger.error(f"Unexpected exception: {str(exc)}", exc_info=True)
        response = ApiResponse.error(
            message=str(exc) if settings.debug else "An unexpected error occurred",
            code="internal_server_error",
            details={"type": exc.__class__.__name__} if settings.debug else {}
        )
        return JSONResponse(
            status_code=500,
            content=response.model_dump()
        )


# Create the application instance
app = create_app()

# For backward compatibility, keep the original variable name
# This ensures existing deployment scripts continue to work
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
