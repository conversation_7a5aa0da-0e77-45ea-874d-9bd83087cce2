"""Configuration for Activities MCP."""
from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    """Application settings."""
    
    # Service
    service_name: str = "activities-mcp"
    service_version: str = "1.0.0"
    port: int = 8002
    
    # Database
    database_url: str = os.getenv("DATABASE_URL", "")
    aws_region: str = os.getenv("AWS_REGION", "us-east-1")
    activities_table: str = os.getenv("DYNAMODB_ACTIVITIES_TABLE", "activities")
    submissions_table: str = os.getenv("DYNAMODB_SUBMISSIONS_TABLE", "submissions")
    
    # Logging
    log_level: str = "INFO"
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"  # Ignore extra fields from .env


settings = Settings()
