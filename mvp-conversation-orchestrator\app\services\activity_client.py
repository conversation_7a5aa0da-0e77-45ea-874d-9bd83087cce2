"""Activity MCP client - handles activity-related queries."""

import logging
import httpx
from typing import Dict, Any
from ..core.config import settings

logger = logging.getLogger(__name__)


class ActivityClient:
    """Client for ActivityMCP service - calls real activities API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.timeout = 30.0
        logger.info(f"[ActivityClient] Initialized with base_url: {base_url}")
    
    async def process_message(
        self, 
        user_id: str, 
        text: str, 
        session_id: str = None
    ) -> Dict[str, Any]:
        """
        Process activity-related message.
        
        Args:
            user_id: User identifier
            text: User message
            session_id: Optional session ID
            
        Returns:
            Response with activity data from API
        """
        logger.info(f"[ActivityClient] Processing message for user: {user_id}")
        logger.debug(f"[ActivityClient] Message text: '{text}'")
        logger.debug(f"[ActivityClient] Session ID: {session_id}")
        
        text_lower = text.lower()
        
        # Check if user wants to list/show activities
        if 'list' in text_lower or 'show' in text_lower or 'what' in text_lower or 'activities' in text_lower:
            logger.info("[ActivityClient] Fetching activities from API")
            return await self._fetch_activities()
        
        elif 'create' in text_lower or 'submit' in text_lower:
            logger.info("[ActivityClient] Returning CREATE activity response")
            return {
                "success": True,
                "type": "create_activity",
                "message": "I can help you create a new activity! What type of activity would you like to add?",
                "data": {
                    "action": "create",
                    "activity_types": ["sports", "arts", "education", "community"],
                    "next_step": "select_type"
                }
            }
        
        else:
            logger.info("[ActivityClient] Returning GENERAL activity response")
            return {
                "success": True,
                "type": "activity_info",
                "message": "I can help you explore activities, create new ones, or submit to existing activities. What would you like to do?",
                "data": {
                    "options": ["list_activities", "create_activity", "submit_to_activity"]
                }
            }
    
    async def _fetch_activities(self) -> Dict[str, Any]:
        """
        Fetch activities from the onboarding API's activity service.
        Uses the gemini-chat-with-intent endpoint which has access to ActivityService.
        
        Returns:
            Response with activities list from DynamoDB
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                # Call the gemini-chat-with-intent endpoint with an activity query
                # This will trigger the activity intent and fetch from DynamoDB
                url = f"{self.base_url}/api/v1/gemini-chat-with-intent"
                headers = {
                    "Content-Type": "application/json",
                    "session_id": "activity-query-session"
                }
                payload = {
                    "message": "show me all activities"
                }
                
                logger.info(f"[ActivityClient] Calling activities via gemini-chat at {url}")
                
                response = await client.post(url, json=payload, headers=headers)
                response.raise_for_status()
                
                data = response.json()
                logger.info(f"[ActivityClient] Received response from API")
                logger.debug(f"[ActivityClient] Response data: {data}")
                
                # Check if response contains activities data
                if data.get("activities"):
                    activities = data["activities"]
                    logger.info(f"[ActivityClient] Found {len(activities)} activities in response")
                    
                    # Format activities for display
                    formatted_activities = []
                    for activity in activities:
                        formatted_activities.append({
                            "id": activity.get("id", ""),
                            "title": activity.get("title", "Untitled Activity"),
                            "type": activity.get("type", activity.get("activity_type", "general")),
                            "date": activity.get("due_date", activity.get("date", "TBD")),
                            "description": activity.get("description", ""),
                            "location": activity.get("metadata", {}).get("location", ""),
                            "organizer": activity.get("created_by", "")
                        })
                    
                    return {
                        "success": True,
                        "type": "list_activities",
                        "message": "Here are the activities available in your area:",
                        "data": {
                            "activities": formatted_activities,
                            "total": len(formatted_activities)
                        }
                    }
                
                # If no activities in response, return the gemini response
                gemini_response = data.get("gemini_response", "")
                return {
                    "success": True,
                    "message": gemini_response or "No activities found at the moment.",
                    "data": {
                        "activities": [],
                        "total": 0
                    }
                }
                
        except httpx.HTTPError as e:
            logger.error(f"[ActivityClient] HTTP error fetching activities: {str(e)}")
            return {
                "success": False,
                "message": "Sorry, I couldn't fetch the activities right now. Please try again later.",
                "data": {
                    "error": str(e),
                    "activities": [],
                    "total": 0
                }
            }
        except Exception as e:
            logger.error(f"[ActivityClient] Unexpected error fetching activities: {str(e)}", exc_info=True)
            return {
                "success": False,
                "message": "An unexpected error occurred while fetching activities.",
                "data": {
                    "error": str(e),
                    "activities": [],
                    "total": 0
                }
            }


# Singleton instance
activity_client = ActivityClient(base_url=settings.onboarding_mcp_url)
