# MCP Implementation Reference Guide
## Quick Lookup for Common Tasks

---

## 📚 Document Lookup Table

### By Role

| Role | Start Here | Then Read | Reference |
|------|-----------|-----------|-----------|
| **Project Manager** | README_MODULARIZATION.md | ARCHITECTURE_DIAGRAMS.md § 1-2 | Timeline section |
| **Architect** | MVP_MODULARIZATION_PLAN.md | ARCHITECTURE_DIAGRAMS.md | All diagrams |
| **Senior Dev** | IMPLEMENTATION_QUICK_START.md | CODE_EXTRACTION_GUIDE.md | MVP_MODULARIZATION_PLAN.md § 4-7 |
| **Junior Dev** | IMPLEMENTATION_QUICK_START.md | Step-by-step code | ARCHITECTURE_DIAGRAMS.md § 3 |
| **Frontend Dev** | IMPLEMENTATION_QUICK_START.md § 9 | ARCHITECTURE_DIAGRAMS.md § 3 | MVP_MODULARIZATION_PLAN.md § 9 |
| **DevOps** | IMPLEMENTATION_QUICK_START.md § 6-7 | MVP_MODULARIZATION_PLAN.md § 10 | docker-compose.dev.yml |

### By Task

| Task | Document | Section |
|------|----------|---------|
| Understand current monolith | MVP_MODULARIZATION_PLAN.md | § 2 |
| Design MCPs | MVP_MODULARIZATION_PLAN.md | § 3-6 |
| Extract services | CODE_EXTRACTION_GUIDE.md | § 2-4 |
| Build Orchestrator | MVP_MODULARIZATION_PLAN.md | § 7 |
| Run locally | IMPLEMENTATION_QUICK_START.md | § 6 |
| Write tests | IMPLEMENTATION_QUICK_START.md | § 8 |
| Integrate frontend | IMPLEMENTATION_QUICK_START.md | § 9 |
| Deploy to production | MVP_MODULARIZATION_PLAN.md | § 10 |
| Monitor/debug | ARCHITECTURE_DIAGRAMS.md | § 10 |

---

## 🏗️ Architecture Quick Reference

### Service Mapping

```
MONOLITH COMPONENT          →  MCP SERVICE          PORT
────────────────────────────────────────────────────────
auth_service.py             →  OnboardingMCP        8001
session_service.py          →  OnboardingMCP        8001
intent_service.py           →  ChatMCP              8003
gemini_service.py           →  ChatMCP              8003
activity_service.py         →  ActivityMCP          8002
submission_service.py       →  ActivityMCP          8002
Main router logic           →  Orchestrator         8000
```

### MCP Responsibilities

**OnboardingMCP (Port 8001)**
- User signup flows
- User login flows
- Session management for onboarding
- Calls external auth API

**ActivityMCP (Port 8002)**
- Activity CRUD operations
- Activity submissions
- Activity intent classification
- Calls city service API

**ChatMCP (Port 8003)** [Optional in MVP]
- Gemini AI chat responses
- General intent classification
- Conversation history
- Calls Gemini API

**Orchestrator (Port 8000)**
- Receives messages from frontend
- Detects user intent
- Routes to appropriate MCP
- Manages session context
- Frontend talks to this

---

## 🚀 Setup Commands

### Create Directory Structure
```bash
mkdir -p my_onboarding_api/mcp/{onboarding_mcp,activity_mcp,chat_mcp,orchestrator}/{api,services,schemas,core}
mkdir -p shared/{models,exceptions,logging,utils,database}
mkdir -p tests/{unit,integration,e2e}/{onboarding_mcp,activity_mcp,orchestrator}
```

### Install Dependencies
```bash
# For each MCP, create requirements.txt with:
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0
httpx==0.25.0
python-dotenv==1.0.0

# Then:
pip install -r requirements.txt
```

### Run Locally (Option 1: Docker Compose)
```bash
# In project root
docker-compose -f docker-compose.dev.yml up

# Test
curl -X POST http://localhost:8000/message \
  -H "Content-Type: application/json" \
  -H "session_id: test-123" \
  -d '{"message": "I want to sign up"}'
```

### Run Locally (Option 2: Manual)
```bash
# Terminal 1: OnboardingMCP
cd my_onboarding_api/mcp/onboarding_mcp
python -m uvicorn main:app --port 8001

# Terminal 2: ActivityMCP
cd my_onboarding_api/mcp/activity_mcp
python -m uvicorn main:app --port 8002

# Terminal 3: ChatMCP
cd my_onboarding_api/mcp/chat_mcp
python -m uvicorn main:app --port 8003

# Terminal 4: Orchestrator
cd my_onboarding_api/mcp/orchestrator
python -m uvicorn main:app --port 8000

# Terminal 5: Test
curl -X POST http://localhost:8000/message ...
```

---

## 📝 Code Templates

### MCP Main.py Template
```python
from fastapi import FastAPI
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    print("Starting MCP service")
    yield
    # Shutdown
    print("Shutting down MCP service")

app = FastAPI(
    title="ServiceMCP",
    version="1.0.0",
    lifespan=lifespan
)

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "service_mcp"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

### MCP Routes Template
```python
from fastapi import APIRouter

router = APIRouter()

@router.post("/service/message")
async def process_message(request: dict):
    """Process message."""
    session_id = request.get("session_id")
    message = request.get("message")
    
    # Business logic here
    
    return {
        "success": True,
        "type": "response_type",
        "data": { ... }
    }

@router.get("/health")
async def health_check():
    return {"status": "healthy"}
```

### MCP Service Template
```python
class MyService:
    def __init__(self):
        """Initialize service."""
        self._client = None
    
    async def initialize(self):
        """Initialize async resources."""
        self._client = httpx.AsyncClient()
    
    async def cleanup(self):
        """Cleanup resources."""
        if self._client:
            await self._client.aclose()
    
    async def do_something(self, data: dict):
        """Do something."""
        if not self._client:
            raise RuntimeError("Service not initialized")
        
        # Implementation
        return result

my_service = MyService()
```

### Frontend Integration Template
```typescript
// Next.js - src/services/orchestrator.ts
const ORCHESTRATOR_URL = process.env.NEXT_PUBLIC_ORCHESTRATOR_URL || "http://localhost:8000";

export async function sendMessage(message: string, sessionId?: string) {
  const response = await fetch(`${ORCHESTRATOR_URL}/message`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      ...(sessionId && { "session_id": sessionId })
    },
    body: JSON.stringify({ message })
  });

  if (!response.ok) {
    throw new Error(`API error: ${response.statusText}`);
  }

  return response.json();
}
```

---

## 🧪 Testing Templates

### Unit Test Template
```python
import pytest
from mcp.onboarding_mcp.services.session_service import session_service

def test_start_flow():
    session = session_service.start_flow("test-id", "signup")
    assert session.flow_type == "signup"

@pytest.mark.asyncio
async def test_async_operation():
    result = await my_service.async_method()
    assert result.success == True
```

### Integration Test Template
```python
import pytest
import httpx

@pytest.mark.asyncio
async def test_mcp_endpoint():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8001/mcp/onboarding/message",
            json={
                "session_id": "test-123",
                "message": "I want to sign up",
                "detected_intent": "signup"
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] == True
```

---

## 📊 API Quick Reference

### Request/Response Format

All MCPs follow this contract:

**Request:**
```json
{
  "session_id": "uuid-string",
  "message": "user input",
  "detected_intent": "optional-intent",
  "context": {
    "flow_type": "signup|login|null",
    "flow_step": "step-name",
    "previous_responses": []
  }
}
```

**Success Response:**
```json
{
  "success": true,
  "type": "response_type",
  "data": {
    "message": "user-facing message",
    "field1": "value1"
  }
}
```

**Error Response:**
```json
{
  "success": false,
  "type": "error",
  "error": {
    "code": "error_code",
    "message": "Human readable error"
  }
}
```

### HTTP Status Codes
- `200`: Success
- `400`: Bad request
- `401`: Unauthorized
- `500`: Server error

---

## 🛠️ Common Tasks

### How to Extract a Service

```bash
# 1. Copy original file
cp app/services/my_service.py mcp/my_mcp/services/my_service.py

# 2. Update imports (app → shared)
sed -i 's/from \.\.core/from ...shared/g' mcp/my_mcp/services/my_service.py
sed -i 's/from \.\.models/from ...shared.models/g' mcp/my_mcp/services/my_service.py

# 3. Convert to async (if needed)
# See CODE_EXTRACTION_GUIDE.md for detailed instructions

# 4. Create routes.py to expose via HTTP
# See IMPLEMENTATION_QUICK_START.md for template

# 5. Test
pytest tests/unit/test_my_mcp.py
```

### How to Add a New Endpoint to an MCP

```python
# In mcp/my_mcp/api/routes.py

@router.post("/new-endpoint")
async def new_endpoint(request: dict):
    """New endpoint description."""
    try:
        # Process request
        result = await my_service.do_something(request)
        
        # Return response
        return {
            "success": True,
            "type": "operation_complete",
            "data": result
        }
    
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return {
            "success": False,
            "type": "error",
            "error": {
                "code": "operation_error",
                "message": str(e)
            }
        }
```

### How to Debug a Service

```python
# 1. Add logging
logger.debug(f"Input data: {request}")
logger.info(f"Processing started for {session_id}")
logger.error(f"Error occurred: {str(e)}", exc_info=True)

# 2. Check service health
curl http://localhost:8001/health

# 3. Check logs
docker logs service_container_name

# 4. Check MCP communication
curl -X POST http://localhost:8000/message \
  -H "Content-Type: application/json" \
  -d '{"session_id":"test","message":"debug test"}'
```

---

## 🚨 Troubleshooting

### MCPs can't communicate
```
Check:
1. Docker network: docker network ls
2. Port mapping: docker ps
3. Firewall: sudo ufw status
4. DNS resolution: docker exec container_name nslookup service_name
```

### High latency
```
Check:
1. External API calls: Add timing logs
2. Database queries: Enable query logging
3. Network: Use --network host (Docker)
4. CPU/Memory: docker stats
```

### Service keeps crashing
```
Check:
1. Log output: docker logs -f container_name
2. Health endpoint: curl http://localhost:port/health
3. Dependencies: Are required services running?
4. Environment variables: Are they set correctly?
```

### Session state inconsistency
```
Check:
1. Are all MCPs using same session store?
2. Is session_id being passed correctly?
3. Check session TTL settings
4. Verify database connectivity
```

---

## 📈 Performance Tuning

### Optimize Orchestrator Routing
```python
# Add caching for frequently used intents
from functools import lru_cache

@lru_cache(maxsize=1000)
def cached_intent_detection(message: str) -> str:
    return detect_intent(message)
```

### Optimize External API Calls
```python
# Use connection pooling
client = httpx.AsyncClient(
    limits=httpx.Limits(max_connections=100)
)

# Add retries
from tenacity import retry, stop_after_attempt

@retry(stop=stop_after_attempt(3))
async def call_external_api():
    ...
```

### Optimize Database Queries
```python
# Add indexes to DynamoDB
# See AWS documentation for GSI configuration

# Use batch operations
dynamodb.batch_write_item(
    RequestItems={
        'table': [
            {'PutRequest': {'Item': item1}},
            {'PutRequest': {'Item': item2}}
        ]
    }
)
```

---

## 🔐 Security Checklist

- [ ] Input validation on all endpoints
- [ ] Rate limiting (use FastAPI middleware)
- [ ] CORS configuration (check allowed origins)
- [ ] JWT token validation for protected endpoints
- [ ] Environment variables for secrets (not in code)
- [ ] HTTPS in production
- [ ] Request signing (if calling external APIs)
- [ ] Sanitize logs (no sensitive data)

---

## 📚 Key Formulas & Calculations

### Load Calculation
```
Per-service capacity = requests_per_sec × avg_response_time
Total throughput = sum of all service capacities
Orchestrator capacity = messages_per_sec (just routing)
```

### Scaling Decision
```
If P95 latency > threshold:
  - Profile service (where is time spent?)
  - If external API: Add caching/batching
  - If database: Add indexes/optimization
  - If compute: Increase replicas

If error rate > threshold:
  - Check logs for error type
  - Add retry logic if transient
  - Improve validation if bad input
  - Add circuit breaker if external API
```

---

## 🎯 Success Criteria Checklist

### Phase 1 Complete (3 weeks)
- [ ] OnboardingMCP working (signup/login)
- [ ] ActivityMCP working (CRUD)
- [ ] All services have tests (>80% coverage)
- [ ] Docker setup working locally
- [ ] Services can be run independently

### Phase 2 Complete (2 weeks)
- [ ] Orchestrator routes correctly
- [ ] End-to-end signup flow works
- [ ] End-to-end activity flow works
- [ ] Session management consistent
- [ ] Load tested (100+ concurrent users)

### Production Ready
- [ ] Monitoring/logging in place
- [ ] Error handling complete
- [ ] Documentation complete
- [ ] Team trained on new architecture
- [ ] Gradual rollout plan ready

---

## 🔗 Quick Links

| Resource | Location |
|----------|----------|
| Main architecture doc | MVP_MODULARIZATION_PLAN.md |
| Implementation guide | IMPLEMENTATION_QUICK_START.md |
| Detailed extraction | CODE_EXTRACTION_GUIDE.md |
| Visual diagrams | ARCHITECTURE_DIAGRAMS.md |
| This reference | MCP_IMPLEMENTATION_REFERENCE.md |

---

## 💡 Pro Tips

1. **Start small**: Implement OnboardingMCP first (simpler than Activity)
2. **Test early**: Write tests as you extract, not after
3. **Use containers**: Docker makes service isolation easy
4. **Monitor from day 1**: Add logging/metrics early
5. **Document as you go**: Update README as architecture evolves
6. **Ask questions**: If confused, check the documents first
7. **Git branches**: One branch per MCP extraction
8. **Backward compat**: Keep monolith proxy layer during transition

---

## 📞 Getting Help

- **Architecture question?** → MVP_MODULARIZATION_PLAN.md
- **"How do I...?" question?** → This document (section above)
- **Code example needed?** → IMPLEMENTATION_QUICK_START.md or CODE_EXTRACTION_GUIDE.md
- **Visual explanation?** → ARCHITECTURE_DIAGRAMS.md
- **Timeline/planning?** → README_MODULARIZATION.md

---

**Last Updated:** November 2025  
**Status:** Ready for Development  
**Estimated Implementation Time:** 5 weeks (Phase 1 & 2)
