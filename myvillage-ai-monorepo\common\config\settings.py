"""Shared configuration settings."""
from pydantic_settings import BaseSettings
from typing import List


class CommonSettings(BaseSettings):
    """Common settings for all services."""
    
    # Database
    database_url: str = "postgresql://user:password@localhost:5432/myvillage"
    
    # Logging
    log_level: str = "INFO"
    
    # Security
    jwt_secret: str
    jwt_algorithm: str = "HS256"
    jwt_expiration: int = 3600
    
    # CORS
    cors_origins: List[str] = ["*"]
    cors_allow_credentials: bool = True
    cors_allow_methods: List[str] = ["*"]
    cors_allow_headers: List[str] = ["*"]
    
    class Config:
        env_file = ".env"
        case_sensitive = False
