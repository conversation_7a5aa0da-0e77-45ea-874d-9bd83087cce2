# 🚀 Quick Reference - MyVillage AI Monorepo

## 📁 Project Location
```
D:\Projects\myvillage\Git_Repo\myvillageai\myvillage-ai-monorepo\
```

## 🎯 Services & Ports

| Service | Port | Status | Purpose |
|---------|------|--------|---------|
| Orchestrator | 8100 | 🟡 Skeleton | Intent detection & routing |
| MCP Onboarding | 8001 | 🟡 Skeleton | User auth & profiles |
| MCP Activities | 8002 | 🟡 Skeleton | Activity management |
| MCP Rewards | 8003 | 🟡 Skeleton | Rewards tracking |
| MCP Approval | 8004 | 🟡 Skeleton | Approval workflows |

🟡 = Structure ready, implementation pending

## 🔧 Common Library Usage

### Import Models
```python
from common.models.base import BaseResponse, HealthResponse
```

### Import Validation
```python
from common.utils.validation import validate_email, validate_phone, sanitize_input
```

### Import Security
```python
from common.utils.security import hash_password, verify_password, generate_token
```

### Import Config
```python
from common.config.settings import CommonSettings
```

### Import Constants
```python
from common.constants import PORT_ORCHESTRATOR, SERVICE_ONBOARDING
```

## 🐳 Docker Commands

### Start All Services
```bash
cd myvillage-ai-monorepo
docker-compose up -d
```

### View Logs
```bash
docker-compose logs -f
docker-compose logs -f orchestrator  # Specific service
```

### Stop All Services
```bash
docker-compose down
```

### Rebuild Services
```bash
docker-compose up -d --build
```

### Check Status
```bash
docker-compose ps
```

## 📝 Git Commands

### Initial Commit
```bash
cd myvillage-ai-monorepo
git add .
git commit -m "feat: Phase 1 - Setup monorepo foundation"
```

### Check Status
```bash
git status
```

### View Structure
```bash
git ls-tree -r HEAD --name-only
```

## 🧪 Testing Common Library

```bash
cd myvillage-ai-monorepo/common

# Test models
python -c "from models.base import BaseResponse; print('✅ Models OK')"

# Test validation
python -c "from utils.validation import validate_email; print(validate_email('<EMAIL>'))"

# Test security
python -c "from utils.security import hash_password; h, s = hash_password('test123'); print('✅ Security OK')"

# Test config
python -c "from config.settings import CommonSettings; print('✅ Config OK')"
```

## 📂 File Locations

### Configuration Files
- Environment: `.env.example` → copy to `.env`
- Docker: `docker-compose.yml`
- Git: `.gitignore`

### Common Library
- Models: `common/models/base.py`
- Validation: `common/utils/validation.py`
- Security: `common/utils/security.py`
- Config: `common/config/settings.py`
- Constants: `common/constants.py`

### Service Files
Each service has:
- `app/` - Application code (empty for now)
- `tests/` - Test files (empty for now)
- `Dockerfile` - Container configuration
- `requirements.txt` - Python dependencies
- `README.md` - Service documentation

## 🔄 Next Phase Checklist

### Phase 2: Extract Onboarding MCP
- [ ] Create `mcp-onboarding/app/main.py`
- [ ] Copy auth services from `my_onboarding_api`
- [ ] Create tool endpoints
- [ ] Create MCP manifest
- [ ] Test service

### Files to Copy
```
my_onboarding_api/app/services/auth_service.py
my_onboarding_api/app/services/session_service.py
my_onboarding_api/app/models/user.py
my_onboarding_api/app/api/auth.py
my_onboarding_api/app/core/database.py
```

## 🌐 Service URLs (Local)

```
Orchestrator:     http://localhost:8100
Onboarding MCP:   http://localhost:8001
Activities MCP:   http://localhost:8002
Rewards MCP:      http://localhost:8003
Approval MCP:     http://localhost:8004
```

## 🔍 Health Check Endpoints

```bash
curl http://localhost:8100/health  # Orchestrator
curl http://localhost:8001/health  # Onboarding
curl http://localhost:8002/health  # Activities
curl http://localhost:8003/health  # Rewards
curl http://localhost:8004/health  # Approval
```

## 📊 Project Statistics

- **Total Services:** 5
- **Common Library Files:** 8
- **Docker Files:** 6
- **Configuration Files:** 3
- **Total Files Created:** 30+
- **Lines of Code:** ~500+

## 🎯 Phase 1 Status

✅ **COMPLETE**

All foundation work is done:
- ✅ Monorepo structure
- ✅ Common library
- ✅ Docker configuration
- ✅ Git repository
- ✅ Documentation

## 📚 Documentation

- `README.md` - Main project documentation
- `PHASE1_COMPLETE.md` - Phase 1 completion details
- `MONOREPO_MIGRATION_GUIDE.md` - Full migration guide
- `QUICK_START_SUMMARY.md` - Quick start guide
- `QUICK_REFERENCE.md` - This file

## 🆘 Troubleshooting

### Import Errors
```python
# Add common to path
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "common"))
```

### Docker Network Issues
```bash
docker network ls
docker network inspect myvillage-network
```

### Port Conflicts
```bash
# Check what's using a port (Windows)
netstat -ano | findstr :8100

# Kill process (Windows)
taskkill /PID <pid> /F
```

## 💡 Tips

1. **Always work from monorepo root** for Docker commands
2. **Use relative imports** within services
3. **Import from common** for shared functionality
4. **Test locally** before Docker
5. **Commit often** with descriptive messages

## 🔗 Related Files

- Migration Guide: `../MONOREPO_MIGRATION_GUIDE.md`
- Quick Start: `../QUICK_START_SUMMARY.md`
- Architecture: `../my_onboarding_api/architecture.md`

---

**Last Updated:** November 18, 2025  
**Phase:** 1 of 6 Complete  
**Status:** ✅ Ready for Phase 2
