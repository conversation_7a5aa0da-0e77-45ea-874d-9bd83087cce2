"""Tool: Create Activity."""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "common"))

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
import logging

from common.models.base import BaseResponse
from ..services.activity_service import ActivityService
from ..models.activity import ActivityCreate, ActivityType

logger = logging.getLogger(__name__)
router = APIRouter()
activity_service = ActivityService()


class CreateActivityRequest(BaseModel):
    """Create activity request model."""
    title: str = Field(..., min_length=1, max_length=200, description="Activity title")
    description: str = Field(..., description="Activity description")
    activity_type: str = Field(..., description="Activity type (assignment, event, survey, discussion, volunteer)")
    created_by: str = Field(..., description="User ID of creator")
    due_date: Optional[datetime] = Field(None, description="Due date (ISO format)")
    points: Optional[int] = Field(None, ge=0, description="Points for completion")
    tags: Optional[List[str]] = Field([], description="Activity tags")


@router.post("/create_activity", response_model=BaseResponse)
async def create_activity(request: CreateActivityRequest):
    """
    Create a new activity.
    
    This tool creates a new activity in the system with the provided information.
    
    Args:
        request: Activity creation request
        
    Returns:
        BaseResponse with created activity data
        
    Raises:
        HTTPException: If activity creation fails
    """
    try:
        logger.info(f"Creating activity: {request.title}")
        
        # Validate activity type
        try:
            activity_type = ActivityType(request.activity_type)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid activity type. Must be one of: {', '.join([t.value for t in ActivityType])}"
            )
        
        # Create activity
        activity_create = ActivityCreate(
            title=request.title,
            description=request.description,
            activity_type=activity_type,
            created_by=request.created_by,
            due_date=request.due_date,
            points=request.points,
            tags=request.tags
        )
        
        activity = await activity_service.create_activity(activity_create)
        
        logger.info(f"Activity created successfully: {activity.id}")
        
        return BaseResponse(
            success=True,
            message="Activity created successfully",
            data={
                "activity_id": activity.id,
                "title": activity.title,
                "activity_type": activity.activity_type.value,
                "status": activity.status.value,
                "created_at": activity.created_at.isoformat()
            }
        )
    except HTTPException:
        raise
    except ValueError as e:
        logger.warning(f"Activity creation failed: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error creating activity: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create activity: {str(e)}"
        )
