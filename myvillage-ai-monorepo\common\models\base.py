"""Base models shared across all services."""
from pydantic import BaseModel, <PERSON>
from typing import Optional, Any, Dict
from datetime import datetime


class BaseResponse(BaseModel):
    """Standard response format for all services."""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class HealthResponse(BaseModel):
    """Health check response."""
    status: str
    service: str
    version: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
