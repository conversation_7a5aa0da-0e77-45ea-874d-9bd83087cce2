# NLU Service Architecture & Flow

## System Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                         User Input                               │
│              "Show me quizzes in Mumbai due this week"           │
└────────────────────────────┬────────────────────────────────────┘
                             │
                             ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Intent Detector                               │
│                  (intent_detector.py)                            │
│                                                                   │
│  ┌──────────────────────────────────────────────────────────┐  │
│  │  detect_advanced(message, context)                        │  │
│  └──────────────────────────────────────────────────────────┘  │
└────────────────────────────┬────────────────────────────────────┘
                             │
                             ▼
┌─────────────────────────────────────────────────────────────────┐
│                      NLU Service                                 │
│                    (nlu_service.py)                              │
│                                                                   │
│  ┌────────────────┐  ┌────────────────┐  ┌────────────────┐   │
│  │ Intent         │  │ Entity         │  │ Sentiment      │   │
│  │ Detection      │  │ Extraction     │  │ Analysis       │   │
│  └────────────────┘  └────────────────┘  └────────────────┘   │
│                                                                   │
│  ┌────────────────┐  ┌────────────────┐  ┌────────────────┐   │
│  │ Confidence     │  │ Context        │  │ Multi-Intent   │   │
│  │ Scoring        │  │ Application    │  │ Detection      │   │
│  └────────────────┘  └────────────────┘  └────────────────┘   │
└────────────────────────────┬────────────────────────────────────┘
                             │
                             ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Analysis Result                             │
│                                                                   │
│  Intent: activity_list                                           │
│  Confidence: 0.95                                                │
│  Entities:                                                       │
│    - activity_type: quiz                                         │
│    - city: Mumbai                                                │
│    - date_range: this week (2025-12-02 to 2025-12-08)           │
│  Sentiment: neutral                                              │
│  Action: list                                                    │
└────────────────────────────┬────────────────────────────────────┘
                             │
                             ▼
┌─────────────────────────────────────────────────────────────────┐
│                   Filter Extraction                              │
│              extract_filters_from_entities()                     │
│                                                                   │
│  Filters:                                                        │
│    - activity_type: quiz                                         │
│    - city: Mumbai                                                │
│    - start_date: 2025-12-02                                      │
│    - end_date: 2025-12-08                                        │
└────────────────────────────┬────────────────────────────────────┘
                             │
                             ▼
┌─────────────────────────────────────────────────────────────────┐
│                    MCP Service Router                            │
│                  get_mcp_service(intent)                         │
│                                                                   │
│  Intent: activity_list → Service: activities                     │
└────────────────────────────┬────────────────────────────────────┘
                             │
                             ▼
┌─────────────────────────────────────────────────────────────────┐
│                   Activities MCP Service                         │
│                                                                   │
│  list_activities(                                                │
│    activity_type="quiz",                                         │
│    city="Mumbai",                                                │
│    start_date="2025-12-02",                                      │
│    end_date="2025-12-08"                                         │
│  )                                                               │
└────────────────────────────┬────────────────────────────────────┘
                             │
                             ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Response to User                            │
│                                                                   │
│  "Here are the quizzes in Mumbai due this week:                  │
│   1. Math Quiz - Due Dec 5                                       │
│   2. Science Quiz - Due Dec 7                                    │
│   ..."                                                           │
└─────────────────────────────────────────────────────────────────┘
```

## Entity Extraction Pipeline

```
User Input: "Show submitted assignments for math class in Bangalore"
     │
     ▼
┌─────────────────────────────────────────────────────────────────┐
│                   Text Normalization                             │
│  "show submitted assignments for math class in bangalore"        │
└────────────────────────────┬────────────────────────────────────┘
                             │
                             ▼
┌─────────────────────────────────────────────────────────────────┐
│                   Parallel Entity Extraction                     │
│                                                                   │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐          │
│  │ Activity     │  │ Status       │  │ Subject      │          │
│  │ Type         │  │ Extraction   │  │ Extraction   │          │
│  │ Extraction   │  │              │  │              │          │
│  │              │  │ Pattern:     │  │ Pattern:     │          │
│  │ Pattern:     │  │ "submitted"  │  │ "math"       │          │
│  │ "assignment" │  │              │  │              │          │
│  │              │  │ Result:      │  │ Result:      │          │
│  │ Result:      │  │ submitted    │  │ Math         │          │
│  │ assignment   │  │              │  │              │          │
│  └──────────────┘  └──────────────┘  └──────────────┘          │
│                                                                   │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐          │
│  │ City         │  │ Date         │  │ Action       │          │
│  │ Extraction   │  │ Extraction   │  │ Extraction   │          │
│  │              │  │              │  │              │          │
│  │ Pattern:     │  │ Pattern:     │  │ Pattern:     │          │
│  │ "bangalore"  │  │ (none)       │  │ "show"       │          │
│  │              │  │              │  │              │          │
│  │ Result:      │  │ Result:      │  │ Result:      │          │
│  │ Bangalore    │  │ null         │  │ list         │          │
│  └──────────────┘  └──────────────┘  └──────────────┘          │
└────────────────────────────┬────────────────────────────────────┘
                             │
                             ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Extracted Entities                            │
│                                                                   │
│  {                                                               │
│    "activity_type": "assignment",                                │
│    "status": "submitted",                                        │
│    "subject": "Math",                                            │
│    "city": "Bangalore",                                          │
│    "action": "list"                                              │
│  }                                                               │
└─────────────────────────────────────────────────────────────────┘
```

## Intent Detection Flow

```
User Input → Normalize → Pattern Matching → Confidence Scoring → Result

Example: "I want to submit my homework"

Step 1: Normalize
  "i want to submit my homework"

Step 2: Pattern Matching
  ┌─────────────────────────────────────────┐
  │ Check all intent patterns:              │
  │                                         │
  │ ✗ signup: no match                      │
  │ ✗ login: no match                       │
  │ ✗ activity_list: no match               │
  │ ✗ activity_create: no match             │
  │ ✓ activity_submit: "submit" found       │
  │   Base confidence: 0.90                 │
  └─────────────────────────────────────────┘

Step 3: Entity-Based Confidence Boost
  ┌─────────────────────────────────────────┐
  │ Entities found:                         │
  │ - activity_type: assignment (homework)  │
  │                                         │
  │ Confidence boost: +0.05                 │
  │ Final confidence: 0.95                  │
  └─────────────────────────────────────────┘

Step 4: Result
  {
    "intent": "activity_submit",
    "confidence": 0.95
  }
```

## Context-Aware Disambiguation

```
Scenario: User has ongoing conversation

Message 1: "Show me activities"
  → Intent: activity_list
  → Context saved: {last_intent: "activity_list"}

Message 2: "in Mumbai"
  → Without context: intent="general", confidence=0.5
  → With context: intent="activity_list", confidence=0.85
  
  Flow:
  ┌──────────────────────────────────────────────────────┐
  │ 1. Detect vague query                                │
  │    "in Mumbai" → low confidence                      │
  │                                                      │
  │ 2. Check context                                     │
  │    last_intent = "activity_list"                     │
  │                                                      │
  │ 3. Extract entities                                  │
  │    city = "Mumbai"                                   │
  │                                                      │
  │ 4. Apply context                                     │
  │    User wants to continue activity_list              │
  │    with city filter                                  │
  │                                                      │
  │ 5. Boost confidence                                  │
  │    0.5 → 0.85                                        │
  └──────────────────────────────────────────────────────┘
```

## Sentiment Analysis Flow

```
User Input: "Great! Show me my rewards"

Step 1: Tokenize and check keywords
  ┌────────────────────────────────────┐
  │ Positive keywords: great ✓         │
  │ Negative keywords: none            │
  │                                    │
  │ Positive count: 1                  │
  │ Negative count: 0                  │
  └────────────────────────────────────┘

Step 2: Calculate sentiment
  ┌────────────────────────────────────┐
  │ if positive > negative:            │
  │   sentiment = "positive"           │
  │ elif negative > positive:          │
  │   sentiment = "negative"           │
  │ else:                              │
  │   sentiment = "neutral"            │
  └────────────────────────────────────┘

Step 3: Result
  sentiment = "positive"
  
  → System can respond with enthusiastic tone
```

## Multi-Intent Detection

```
User Input: "Show me quizzes and my submissions"

Step 1: Detect compound query
  ┌────────────────────────────────────┐
  │ Contains "and" → check for         │
  │ multiple intents                   │
  └────────────────────────────────────┘

Step 2: Pattern matching
  ┌────────────────────────────────────┐
  │ "quizzes" → activity_list          │
  │ "submissions" → submission_list    │
  └────────────────────────────────────┘

Step 3: Result
  {
    "primary_intent": "activity_list",
    "secondary_intents": ["submission_list"],
    "entities": {
      "activity_type": "quiz"
    }
  }
  
  → System can handle both requests or ask for clarification
```

## Date Extraction Examples

```
Input: "Show activities this week"

Step 1: Detect date pattern
  "this week" → matches date_range pattern

Step 2: Calculate range
  ┌────────────────────────────────────┐
  │ Current date: 2025-12-02 (Tuesday) │
  │                                    │
  │ Week start (Monday):               │
  │   2025-12-02 - 1 day = 2025-12-01  │
  │                                    │
  │ Week end (Sunday):                 │
  │   2025-12-01 + 6 days = 2025-12-07 │
  └────────────────────────────────────┘

Step 3: Result
  {
    "date_range": {
      "start": "2025-12-01",
      "end": "2025-12-07",
      "description": "this week"
    }
  }
```

## Complete Example Flow

```
User: "Show me all submitted assignments for math class in Bangalore due this week"

┌─────────────────────────────────────────────────────────────────┐
│ 1. INTENT DETECTION                                             │
│    Pattern: "show" + "assignments" → activity_list              │
│    Confidence: 0.85                                             │
└─────────────────────────────────────────────────────────────────┘
                             ↓
┌─────────────────────────────────────────────────────────────────┐
│ 2. ENTITY EXTRACTION                                            │
│    ✓ activity_type: assignment                                  │
│    ✓ status: submitted                                          │
│    ✓ subject: Math                                              │
│    ✓ city: Bangalore                                            │
│    ✓ date_range: this week (2025-12-01 to 2025-12-07)          │
└─────────────────────────────────────────────────────────────────┘
                             ↓
┌─────────────────────────────────────────────────────────────────┐
│ 3. SENTIMENT ANALYSIS                                           │
│    No positive/negative keywords → neutral                      │
└─────────────────────────────────────────────────────────────────┘
                             ↓
┌─────────────────────────────────────────────────────────────────┐
│ 4. FILTER EXTRACTION                                            │
│    activity_type: assignment                                    │
│    status: submitted                                            │
│    subject: Math                                                │
│    city: Bangalore                                              │
│    start_date: 2025-12-01                                       │
│    end_date: 2025-12-07                                         │
└─────────────────────────────────────────────────────────────────┘
                             ↓
┌─────────────────────────────────────────────────────────────────┐
│ 5. API CALL                                                     │
│    activities_client.list_activities(**filters)                 │
└─────────────────────────────────────────────────────────────────┘
                             ↓
┌─────────────────────────────────────────────────────────────────┐
│ 6. RESPONSE                                                     │
│    "Here are the submitted math assignments in Bangalore        │
│     due this week: ..."                                         │
└─────────────────────────────────────────────────────────────────┘
```

---

**Note**: All diagrams are text-based for easy viewing in any environment.
