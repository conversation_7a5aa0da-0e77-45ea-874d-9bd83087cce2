# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1

# External Auth APIs (same as my_onboarding_api)
AUTH_API_URL=https://6s0bso8sri.execute-api.us-east-1.amazonaws.com/amplifydev/setAuthToken
SIGNUP_API_URL=https://6s0bso8sri.execute-api.us-east-1.amazonaws.com/amplifydev/userCreatev2
AUTH_API_TIMEOUT=10
API_BEARER_TOKEN=your_bearer_token_here

# Security
JWT_SECRET=change-this-secret-key-in-production

# Gemini API (for intent detection)
GEMINI_API_KEY=your_gemini_api_key_here

# DynamoDB Tables (for activities and rewards)
DYNAMODB_CITIES_TABLE=cities
DYNAMODB_ACTIVITIES_TABLE=activities
DYNAMODB_SUBMISSIONS_TABLE=submissions
DYNAMODB_REWARDS_TABLE=rewards

# Service Ports
ORCHESTRATOR_PORT=8100
ONBOARDING_MCP_PORT=8001
ACTIVITIES_MCP_PORT=8002
REWARDS_MCP_PORT=8003

# Logging
LOG_LEVEL=INFO
