export interface User {
  id: string;
  name: string;
  avatar?: string;
  role: 'user' | 'assistant' | 'system';
}

export interface Message {
  id: string;
  content: string;
  sender: User;
  timestamp: Date;
  status?: 'sending' | 'sent' | 'error';
  type?: 'text' | 'image' | 'file' | 'system';
}

export interface ChatSession {
  id: string;
  title: string;
  messages: Message[];
  createdAt: Date;
  updatedAt: Date;
  isPinned?: boolean;
}
