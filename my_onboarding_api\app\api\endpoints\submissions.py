from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Query
from typing import List, Optional, Dict, Any

from ...models.submission import (
    Submission as SubmissionModel,
    SubmissionCreate,
    SubmissionUpdate,
    SubmissionListResponse,
    SubmissionStatus
)
from ...services.submission_service import get_submission_service
from ...core.security import get_current_user

router = APIRouter()

@router.post("/", response_model=SubmissionModel, status_code=status.HTTP_201_CREATED)
async def create_submission(
    activity_id: str = Query(..., description="ID of the activity to submit to"),
    title: str = Query(..., description="Title of the submission"),
    description: Optional[str] = Query(None, description="Description of the submission"),
    is_public: bool = Query(True, description="Whether the submission is public"),
    files: List[UploadFile] = File(None, description="Media files to upload"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Create a new submission for an activity.
    """
    submission_service = get_submission_service()
    try:
        # Create submission data
        submission_data = {
            "activity_id": activity_id,
            "title": title,
            "description": description,
            "is_public": is_public,
            "created_by": str(current_user.id)
        }
        
        # Create the submission
        submission = await submission_service.create_submission(
            submission=SubmissionCreate(**submission_data),
            files=files
        )
        
        return submission
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.get("/{submission_id}", response_model=SubmissionModel)
async def get_submission(
    submission_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get a submission by ID.
    """
    submission_service = get_submission_service()
    try:
        submission = await submission_service.get_submission(submission_id)
        
        # Check if user has permission to view the submission
        if not submission.is_public and str(submission.user_id) != current_user["user_id"] and not current_user.get("is_admin", False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to view this submission"
            )
            
        return submission
    except HTTPException:
        raise
    except Exception as e:
        if isinstance(e, HTTPException):
            raise
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.put("/{submission_id}", response_model=SubmissionModel)
async def update_submission(
    submission_id: str,
    submission: SubmissionUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Update a submission.
    """
    submission_service = get_submission_service()
    try:
        # Check if submission exists and user has permission
        existing_submission = await submission_service.get_submission(submission_id)
        
        # Only allow creator to update
        if str(existing_submission.user_id) != current_user["user_id"] and not current_user.get("is_admin", False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to update this submission"
            )
            
        return await submission_service.update_submission(submission_id, submission, str(current_user["user_id"]))
    except HTTPException:
        raise
    except Exception as e:
        if isinstance(e, HTTPException):
            raise
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.delete("/{submission_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_submission(
    submission_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Delete a submission.
    """
    submission_service = get_submission_service()
    try:
        # Check if submission exists and user has permission
        existing_submission = await submission_service.get_submission(submission_id)
        
        # Only allow creator to delete
        if str(existing_submission.user_id) != current_user["user_id"] and not current_user.get("is_admin", False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to delete this submission"
            )
            
        await submission_service.delete_submission(submission_id)
        return None
    except HTTPException:
        raise
    except Exception as e:
        if isinstance(e, HTTPException):
            raise
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.get("/user/{user_id}", response_model=SubmissionListResponse)
async def get_user_submissions(
    user_id: str,
    activity_id: Optional[str] = Query(None, description="Filter by activity ID"),
    status: Optional[str] = Query(None, description="Filter by status"),
    limit: int = Query(10, ge=1, le=100, description="Number of items per page"),
    offset: int = Query(0, ge=0, description="Page number"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get submissions for a user.
    """
    # Check if user is authorized to view submissions for this user
    if user_id != current_user["user_id"] and not current_user.get("is_admin", False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to view these submissions"
        )
    
    submission_service = get_submission_service()
    try:
        result = await submission_service.list_submissions(
            created_by=user_id,
            activity_id=activity_id,
            status=status,
            skip=offset,
            limit=limit
        )
        return result
    except Exception as e:
        if isinstance(e, HTTPException):
            raise
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve submissions"
        )

@router.post("/{submission_id}/grade", status_code=status.HTTP_200_OK)
async def grade_submission(
    submission_id: str,
    grade: float = Query(..., ge=0, le=100, description="Grade value (0-100)"),
    feedback: str = Query(..., description="Feedback for the submission"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Grade a submission.
    """
    submission_service = get_submission_service()
    try:
        # Check if submission exists
        submission = await submission_service.get_submission(submission_id)
        
        # TODO: Check if current user is authorized to grade this submission
        # This would typically check if the user is an instructor or has grading permissions
        
        # Update the submission with grade and feedback
        submission_update = SubmissionUpdate(
            grade=grade,
            feedback=feedback,
            status=SubmissionStatus.GRADED
        )
        
        return await submission_service.update_submission(
            submission_id=submission_id,
            submission=submission_update,
            updated_by=str(current_user.id)
        )
    except HTTPException:
        raise
    except Exception as e:
        if isinstance(e, HTTPException):
            raise
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.post("/{submission_id}/like", response_model=SubmissionModel)
async def like_submission(
    submission_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Like a submission.
    """
    submission_service = get_submission_service()
    try:
        # Check if user has already liked this submission
        # This would require a separate table to track likes
        # For now, we'll just increment the like count
        submission = await submission_service.get_submission(submission_id)
        if not submission.is_public and submission.user_id != current_user["user_id"] and not current_user.get("is_admin", False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to like this submission"
            )
            
        # In a real implementation, you would check if the user has already liked this submission
        # and handle the like/unlike logic accordingly
        
        return await submission_service.increment_like_count(submission_id)
        
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
