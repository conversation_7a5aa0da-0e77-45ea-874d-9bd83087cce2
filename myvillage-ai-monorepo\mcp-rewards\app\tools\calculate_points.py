"""Tool: Calculate Points."""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "common"))

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
import logging

from common.models.base import BaseResponse
from ..services.rewards_service import RewardsService

logger = logging.getLogger(__name__)
router = APIRouter()
rewards_service = RewardsService()


class CalculatePointsRequest(BaseModel):
    """Calculate points request model."""
    activity_id: str = Field(..., description="Activity ID")
    user_id: str = Field(..., description="User ID")
    points: int = Field(50, ge=0, description="Points to award (default: 50)")


@router.post("/calculate_points", response_model=BaseResponse)
async def calculate_points(request: CalculatePointsRequest):
    """
    Calculate and award points for activity completion.
    
    This tool calculates points for completing an activity and adds them to the user's balance.
    
    Args:
        request: Calculate points request
        
    Returns:
        BaseResponse with updated reward data
    """
    try:
        logger.info(f"Calculating points for activity: {request.activity_id}, user: {request.user_id}")
        
        # Add points to user's balance
        reward = await rewards_service.add_points(
            user_id=request.user_id,
            points=request.points,
            activity_id=request.activity_id,
            description=f"Completed activity {request.activity_id}"
        )
        
        logger.info(f"Points awarded: {request.points}. New balance: {reward.available_points}")
        
        return BaseResponse(
            success=True,
            message=f"Awarded {request.points} points",
            data={
                "user_id": reward.user_id,
                "points_awarded": request.points,
                "total_points": reward.total_points,
                "available_points": reward.available_points
            }
        )
    except ValueError as e:
        logger.warning(f"Points calculation failed: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error calculating points: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to calculate points: {str(e)}"
        )
