"""
API routes for the onboarding application.

This module defines all the API endpoints with proper error handling and validation.
"""

from ..core.config import settings
from typing import Union, Annotated
from fastapi import APIRouter, Header, HTTPException, status
from pydantic import ValidationError

from ..core.logging import get_logger
from ..core.exceptions import (
    OnboardingAPIException,
    ExternalServiceError,
    IntentClassificationError,
    SessionError,
    AuthenticationError,
    internal_server_error
)
from ..models.requests import MessageRequest
from ..models.responses import (
    OnboardingResponse,
    GeminiChatResponse,
    CombinedChatResponse,
    GeminiModelsResponse,
    SessionFlowResponse,
    AuthenticationResponse,
    SignupResponse
)
from ..models.session import FlowType
from ..services.intent_service import intent_service
from ..services.gemini_service import gemini_service
from ..services.auth_service import auth_service
from ..services.session_service import session_service

# Import activity and submission routers
from .endpoints import activities, submissions

# Import auth router
from . import auth

logger = get_logger(__name__)

# Create main router
router = APIRouter()

@router.post("/gemini-chat-with-intent", response_model=Union[CombinedChatResponse, SessionFlowResponse, AuthenticationResponse, SignupResponse])
async def gemini_chat_with_intent(
    msg: MessageRequest,
    session_id: Annotated[str, Header(alias="session_id")]
) -> Union[CombinedChatResponse, SessionFlowResponse, AuthenticationResponse, SignupResponse]:
    """
    Handle chat with intent detection and session-based flows.
    """
    logger.info(f"[DEBUG] Starting gemini_chat_with_intent for session: {session_id}")
    logger.info(f"[DEBUG] Message received: {msg.message}")
    
    try:
        logger.info(f"[DEBUG] Checking for active flow for session: {session_id}")
        # Check if there's an active flow
        if session_service.is_flow_active(session_id):
            logger.info(f"[DEBUG] Active flow found, handling flow for session: {session_id}")
            return _handle_active_flow(session_id, msg.message)
        
        logger.info("[DEBUG] No active flow, starting intent classification")
        # No active flow - perform intent classification
        try:
            detected_intent = intent_service.get_top_intent(msg.message)
            logger.info(f"[DEBUG] Intent classification successful. Detected intent: {detected_intent}")
        except Exception as e:
            logger.error(f"[ERROR] Intent classification failed: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "Intent classification failed",
                    "message": str(e),
                    "type": type(e).__name__
                }
            )
        
        # Handle intent-based flow initiation
        if detected_intent == "signup":
            logger.info(f"[DEBUG] Starting signup flow for session: {session_id}")
            session_service.start_flow(session_id, FlowType.SIGNUP)
            flow_message = session_service.get_flow_message(session_id)

            # Get the actual current step from the session
            session = session_service.get_session(session_id)
            current_step = session.flow_step.value if session.flow_step else "name"
            logger.info(f"[DEBUG] Signup flow started. Current step: {current_step}")

            return SessionFlowResponse(
                message=flow_message,
                flow_step=current_step,
                flow_type="signup"
            )
        
        elif detected_intent == "login":
            logger.info(f"[DEBUG] Starting login flow for session: {session_id}")
            session_service.start_flow(session_id, FlowType.LOGIN)
            flow_message = session_service.get_flow_message(session_id)
            logger.info("[DEBUG] Login flow started")
            
            return SessionFlowResponse(
                message=flow_message,
                flow_step="email",
                flow_type="login"
            )
        
        # Check if this is an activity intent
        if detected_intent.startswith("activity_"):
            logger.info(f"[DEBUG] Processing activity intent: {detected_intent}")
            from ..services.activity_service import get_activity_service
            activity_service = get_activity_service()
            
            try:
                # Strip the 'activity_' prefix to get the actual intent
                activity_type = detected_intent.replace("activity_", "")
                activities_data = await activity_service.get_activities(activity_type, session_id)
                
                # Generate a response that includes both activities and Gemini's interpretation
                gemini_response = gemini_service.generate_chat_response(
                    user_message=msg.message,
                    detected_intent=detected_intent,
                    context={"activities": activities_data}
                )
                
                return {
                    "success": True,
                    "input": msg.message,
                    "detected_intent": detected_intent,
                    "activities": activities_data,
                    "gemini_response": gemini_response,
                    "message": "Activity response generated successfully"
                }
                
            except ExternalServiceError as e:
                logger.error(f"Activity service error: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail={
                        "error": "Activity service unavailable",
                        "message": str(e),
                        "service": "activities"
                    }
                )
        
        # Generate regular chat response for non-activity intents
        logger.info("[DEBUG] Generating chat response with Gemini")
        try:
            gemini_response = gemini_service.generate_chat_response(
                user_message=msg.message,
                detected_intent=detected_intent
            )
            logger.info("[DEBUG] Gemini response generated successfully")
            
            return {
                "success": True,
                "input": msg.message,
                "detected_intent": detected_intent,
                "gemini_response": gemini_response,
                "message": "Response generated successfully"
            }
            
        except ValueError as e:
            logger.error(f"Invalid input in chat with intent: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "error": "Invalid input",
                    "message": str(e)
                }
            )
        
    except AuthenticationError as e:
        logger.error(f"Authentication error in combined chat: {str(e)}")
        status_code = status.HTTP_401_UNAUTHORIZED
        error_detail = {
            "error": "Authentication failed",
            "message": str(e)
        }
        if hasattr(e, "details"):
            error_detail["details"] = str(e.details) if e.details else None
        raise HTTPException(status_code=status_code, detail=error_detail)
        
    except SessionError as e:
        logger.error(f"Session error in combined chat: {str(e)}")
        status_code = status.HTTP_400_BAD_REQUEST
        error_detail = {
            "error": "Session error",
            "message": str(e)
        }
        if hasattr(e, "details"):
            error_detail["details"] = str(e.details) if e.details else None
        raise HTTPException(status_code=status_code, detail=error_detail)
        
    except ExternalServiceError as e:
        logger.error(f"External service error in combined chat: {str(e)}")
        status_code = getattr(e, "status_code", status.HTTP_503_SERVICE_UNAVAILABLE)
        error_detail = {
            "error": "Service unavailable",
            "message": str(e),
            "service": getattr(e, "service_name", "external")
        }
        if hasattr(e, "details"):
            error_detail["details"] = str(e.details) if e.details else None
        raise HTTPException(status_code=status_code, detail=error_detail)
        
    except Exception as e:
        import uuid
        error_id = str(uuid.uuid4())
        logger.error(f"Unexpected error in combined chat (ID: {error_id}): {str(e)}", exc_info=True)
        error_detail = {
            "error": "Internal Server Error",
            "message": "An unexpected error occurred"
        }
        if settings.debug:
            error_detail["details"] = str(e)
        error_message = f"An unexpected error occurred (ID: {error_id})"
        if settings.debug:
            error_message = f"{str(e)}\nError ID: {error_id}"
            
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Internal server error",
                "error_type": "internal_error",
                "message": error_message,
                "error_id": error_id,
                "details": {"error": str(e)} if settings.debug else None
            }
        )

@router.post("/onboarding", response_model=OnboardingResponse)
async def onboarding_intent(msg: MessageRequest) -> OnboardingResponse:
    """Classify user intent using Hugging Face model."""
    try:
        logger.info(f"Processing intent classification for message: {msg.message[:50]}...")
        
        # Classify intent
        intent_results = intent_service.classify_intent(msg.message)
        
        # Convert intent results to list of dicts for JSON serialization
        intent_dicts = [
            {"label": result.label, "score": float(result.score)}
            for result in intent_results
        ]
        
        logger.info(f"Intent classification completed. Top intent: {intent_dicts[0]['label']}")
        
        return {
            "success": True,
            "input": msg.message,
            "intent": intent_dicts,
            "message": "Intent classification completed successfully"
        }
        
    except IntentClassificationError as e:
        logger.error(f"Intent classification error: {str(e)}")
        logger.error(f"Error details: {e.details}")
        error_detail = {
            "error": "Intent classification failed",
            "message": str(e),
            "details": str(e.details) if e.details else None
        }
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_detail
        )
    except ValueError as e:
        logger.error(f"Value error in intent classification: {str(e)}")
        error_detail = {
            "error": "Invalid input",
            "message": str(e)
        }
        if settings.debug:
            error_detail["details"] = str(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_detail
        )
    except Exception as e:
        logger.error(f"Unexpected error in intent classification: {str(e)}")
        error_detail = {
            "error": "Internal Server Error",
            "message": "Failed to process intent classification"
        }
        if settings.debug:
            error_detail["details"] = str(e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_detail
        )

@router.post("/gemini-chat", response_model=GeminiChatResponse)
async def gemini_chat(msg: MessageRequest) -> GeminiChatResponse:
    """Generate chat response using Gemini AI."""
    try:
        logger.info(f"Processing Gemini chat for message: {msg.message[:50]}...")
        
        # Generate response
        response_text = gemini_service.generate_response(msg.message)
        
        logger.info("Gemini chat response generated successfully")
        
        return {
            "success": True,
            "input": msg.message,
            "response": response_text,
            "message": "Chat response generated successfully"
        }
        
    except ExternalServiceError as e:
        logger.error(f"Gemini service error: {str(e)}")
        error_detail = {
            "error": "Gemini service unavailable",
            "message": str(e),
            "service": getattr(e, "service_name", "gemini")
        }
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=error_detail
        )
    except ValueError as e:
        logger.error(f"Invalid input in Gemini chat: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error": "Invalid input",
                "message": str(e)
            }
        )
    except Exception as e:
        logger.error(f"Unexpected error in Gemini chat: {str(e)}")
        error_detail = {
            "error": "Internal Server Error",
            "message": "Failed to generate chat response"
        }
        if settings.debug:
            error_detail["details"] = str(e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_detail
        )

@router.get("/gemini-models", response_model=GeminiModelsResponse)
async def list_gemini_models() -> GeminiModelsResponse:
    """List available Gemini models."""
    try:
        logger.info("Fetching available Gemini models")
        
        models = gemini_service.list_available_models()
        
        logger.info(f"Retrieved {len(models)} Gemini models")
        
        return GeminiModelsResponse(
            available_models=models,
            message=f"Retrieved {len(models)} available models"
        )
        
    except ExternalServiceError as e:
        logger.error(f"Failed to list Gemini models: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={
                "error": "Gemini service unavailable",
                "message": str(e),
                "service": e.service_name
            }
        )
    except Exception as e:
        logger.error(f"Unexpected error listing Gemini models: {str(e)}")
        raise internal_server_error("Failed to list Gemini models")

@router.post("/debug-intent", response_model=dict)
async def debug_intent_classification(msg: MessageRequest) -> dict:
    """Debug endpoint to test intent classification pipeline."""
    try:
        logger.info(f"Debug: Processing intent classification for: {msg.message[:50]}...")

        # Check if pipeline is ready
        if not intent_service.is_pipeline_ready():
            return {
                "error": "Pipeline not ready",
                "pipeline_status": "not_initialized"
            }

        # Get raw pipeline output for debugging
        raw_results = intent_service._pipeline(msg.message.strip())

        return {
            "input": msg.message,
            "raw_results_type": str(type(raw_results)),
            "raw_results": raw_results,
            "pipeline_status": "ready"
        }

    except Exception as e:
        logger.error(f"Debug endpoint error: {str(e)}")
        import traceback
        return {
            "error": str(e),
            "traceback": traceback.format_exc(),
            "input": msg.message
        }

# Include all routers
router.include_router(auth.router, prefix="/auth", tags=["authentication"])
router.include_router(activities.router, prefix="/activities", tags=["activities"])
router.include_router(submissions.router, prefix="/submissions", tags=["submissions"])