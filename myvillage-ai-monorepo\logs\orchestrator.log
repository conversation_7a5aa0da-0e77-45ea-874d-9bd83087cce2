Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "D:\Projects\myvillage\Git_Repo\myvillageai\myvillage-ai-monorepo\orchestrator\app\main.py", line 37, in <module>
    from .routers import chat_router
  File "D:\Projects\myvillage\Git_Repo\myvillageai\myvillage-ai-monorepo\orchestrator\app\routers\__init__.py", line 3, in <module>
    from . import chat_router
  File "D:\Projects\myvillage\Git_Repo\myvillageai\myvillage-ai-monorepo\orchestrator\app\routers\chat_router.py", line 144
    if user_input.lower() in ["cancel", "stop", "exit", "quit"]:
                                                               ^
SyntaxError: invalid syntax
