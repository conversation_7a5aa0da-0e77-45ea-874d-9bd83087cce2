"""Mock Gemini service for testing."""
from typing import List, Dict, Any, Optional
from unittest.mock import MagicMock

class MockGeminiService:
    def __init__(self):
        self.generate_response = MagicMock(return_value="This is a test response from <PERSON>.")
        self.generate_chat_response = MagicMock(return_value="This is a test chat response.")
        self.list_available_models = MagicMock(return_value=[
            {"name": "models/gemini-2.5-flash", "supported_generation_methods": ["generateContent"]}
        ])
        self.is_ready = MagicMock(return_value=True)

# Create a singleton instance
gemini_service = MockGeminiService()
