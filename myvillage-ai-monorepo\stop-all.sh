#!/bin/bash

# Stop all MyVillage AI services

echo "========================================"
echo "Stopping MyVillage AI Services"
echo "========================================"
echo ""

# Function to stop services by port (Windows compatible)
stop_service_by_port() {
    local service_name=$1
    local port=$2
    
    echo "Stopping $service_name (port $port)..."
    
    # Find PIDs using the port on Windows
    local pids=$(netstat -ano | grep ":$port " | grep "LISTENING" | awk '{print $5}' | sort -u)
    
    if [ -z "$pids" ]; then
        echo "  ⚠️  No process found on port $port"
    else
        for pid in $pids; do
            echo "  🔍 Found PID: $pid"
            taskkill //PID $pid //F > /dev/null 2>&1
            if [ $? -eq 0 ]; then
                echo "  ✅ Killed process $pid"
            else
                echo "  ❌ Failed to kill process $pid"
            fi
        done
    fi
    echo ""
}

# Stop all services
stop_service_by_port "Orchestrator" "8100"
stop_service_by_port "Onboarding MCP" "8001"
stop_service_by_port "Activities MCP" "8002"
stop_service_by_port "Rewards MCP" "8003"
stop_service_by_port "Chat MCP" "8005"

# Wait a moment for ports to be released
echo "⏳ Waiting for ports to be released..."
sleep 2

# Verify ports are free
echo ""
echo "🔍 Verifying ports are free..."
all_free=true
for port in 8100 8001 8002 8003 8005; do
    if netstat -ano | grep ":$port " | grep "LISTENING" > /dev/null 2>&1; then
        echo "  ⚠️  Port $port still in use"
        all_free=false
    else
        echo "  ✅ Port $port is free"
    fi
done

echo ""
if [ "$all_free" = true ]; then
    echo "========================================"
    echo "✅ All services stopped successfully!"
    echo "========================================"
else
    echo "========================================"
    echo "⚠️  Some ports are still in use"
    echo "========================================"
    echo ""
    echo "You may need to manually kill processes:"
    echo "  netstat -ano | findstr :8100"
    echo "  taskkill /PID <PID> /F"
fi
echo ""
