version: '3.8'

services:
  orchestrator:
    build: ./orchestrator
    container_name: myvillage-orchestrator
    ports:
      - "8100:8100"
    environment:
      - ONBOARDING_MCP_URL=http://mcp-onboarding:8001
      - ACTIVITIES_MCP_URL=http://mcp-activities:8002
      - REWARDS_MCP_URL=http://mcp-rewards:8003
      - APPROVAL_MCP_URL=http://mcp-approval:8004
      - CHAT_MCP_URL=http://mcp-chat:8000
      - LOG_LEVEL=INFO
    depends_on:
      - mcp-onboarding
      - mcp-activities
      - mcp-rewards
      - mcp-approval
      - mcp-chat
    networks:
      - myvillage-network
    restart: unless-stopped

  mcp-onboarding:
    build: ./mcp-onboarding
    container_name: myvillage-mcp-onboarding
    ports:
      - "8001:8001"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - PORT=8001
      - LOG_LEVEL=INFO
    networks:
      - myvillage-network
    restart: unless-stopped

  mcp-activities:
    build: ./mcp-activities
    container_name: myvillage-mcp-activities
    ports:
      - "8002:8002"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - PORT=8002
      - LOG_LEVEL=INFO
    networks:
      - myvillage-network
    restart: unless-stopped

  mcp-rewards:
    build: ./mcp-rewards
    container_name: myvillage-mcp-rewards
    ports:
      - "8003:8003"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - PORT=8003
      - LOG_LEVEL=INFO
    networks:
      - myvillage-network
    restart: unless-stopped

  mcp-approval:
    build: ./mcp-approval
    container_name: myvillage-mcp-approval
    ports:
      - "8004:8004"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - PORT=8004
      - LOG_LEVEL=INFO
    networks:
      - myvillage-network
    restart: unless-stopped

  mcp-chat:
    build: ./mcp-chat
    container_name: myvillage-mcp-chat
    ports:
      - "8005:8000"
    environment:
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - DYNAMODB_TABLE_NAME=ChatGPT
      - API_PORT=8000
      - LOG_LEVEL=INFO
    networks:
      - myvillage-network
    restart: unless-stopped

networks:
  myvillage-network:
    driver: bridge

volumes:
  postgres-data:
