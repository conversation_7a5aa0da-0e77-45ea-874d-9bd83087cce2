"""Configuration for Rewards MCP."""
from pydantic_settings import BaseSettings
import os


class Settings(BaseSettings):
    """Application settings."""
    
    service_name: str = "rewards-mcp"
    service_version: str = "1.0.0"
    port: int = 8003
    
    database_url: str = os.getenv("DATABASE_URL", "")
    aws_region: str = os.getenv("AWS_REGION", "us-east-1")
    
    # DynamoDB table names
    points_table: str = os.getenv("POINTS_TABLE", "Points-rlumv7mljje5bb65hsiqpi77hu-amplifydev")
    
    log_level: str = "INFO"
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"  # Ignore extra fields from .env


settings = Settings()
