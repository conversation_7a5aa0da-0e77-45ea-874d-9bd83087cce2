"""Configuration for Orchestra<PERSON>."""
from pydantic_settings import BaseSettings
import os


class Settings(BaseSettings):
    """Application settings."""
    
    service_name: str = "orchestrator"
    service_version: str = "1.0.0"
    port: int = 8100
    
    # MCP Service URLs
    onboarding_mcp_url: str = os.getenv("ONBOARDING_MCP_URL", "http://localhost:8001")
    activities_mcp_url: str = os.getenv("ACTIVITIES_MCP_URL", "http://localhost:8002")
    rewards_mcp_url: str = os.getenv("REWARDS_MCP_URL", "http://localhost:8003")
    approval_mcp_url: str = os.getenv("APPROVAL_MCP_URL", "http://localhost:8004")
    chat_mcp_url: str = os.getenv("CHAT_MCP_URL", "http://localhost:8005")
    
    # Logging
    log_level: str = "INFO"
    
    # Timeout
    mcp_timeout: int = 30
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"  # Ignore extra fields from .env


settings = Settings()
