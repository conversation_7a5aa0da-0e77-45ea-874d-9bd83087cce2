"""Submission service."""
from datetime import datetime
from typing import Optional, List
import logging
import uuid

from ..models.submission import Submission, SubmissionCreate, SubmissionUpdate, SubmissionStatus
from ..core.database import get_db

from ..core.config import settings

logger = logging.getLogger(__name__)


class SubmissionService:
    """Submission management service."""
    
    def __init__(self):
        """Initialize submission service."""
        self.db = get_db()
        self.submissions_table = self.db.get_table(settings.submissions_table)
    
    async def create_submission(self, submission: SubmissionCreate) -> Submission:
        """
        Create a new submission.
        
        Args:
            submission: Submission creation data
            
        Returns:
            Created submission
        """
        submission_id = str(uuid.uuid4())
        now = datetime.utcnow().isoformat()
        
        submission_data = {
            "id": submission_id,
            "activity_id": submission.activity_id,
            "user_id": submission.user_id,
            "title": submission.title,
            "description": submission.description,
            "content": submission.content,
            "status": SubmissionStatus.SUBMITTED.value,
            "is_public": submission.is_public,
            "created_at": now,
            "updated_at": now,
            "submitted_at": now,
            "likes_count": 0
        }
        
        try:
            self.submissions_table.put_item(Item=submission_data)
            logger.info(f"Submission created successfully: {submission_id}")
            
            return Submission(**submission_data)
        except Exception as e:
            logger.error(f"Error creating submission: {e}")
            raise ValueError(f"Failed to create submission: {str(e)}")
    
    async def get_submission(self, submission_id: str) -> Optional[Submission]:
        """
        Get submission by ID.
        
        Args:
            submission_id: Submission ID
            
        Returns:
            Submission if found, None otherwise
        """
        try:
            response = self.submissions_table.get_item(Key={"id": submission_id})
            if "Item" in response:
                return Submission(**response["Item"])
            return None
        except Exception as e:
            logger.error(f"Error getting submission: {e}")
            return None
    
    async def list_submissions(
        self,
        activity_id: Optional[str] = None,
        user_id: Optional[str] = None,
        status: Optional[str] = None,
        city_id: Optional[str] = None,
        limit: int = 10
    ) -> List[Submission]:
        """
        List submissions with optional filters.
        
        Args:
            activity_id: Filter by activity ID
            user_id: Filter by user ID
            status: Filter by status
            city_id: Filter by city ID (via activity)
            limit: Maximum number of results
            
        Returns:
            List of submissions
        """
        try:
            # If city_id is provided, we need to get activity IDs for that city first
            valid_activity_ids = []
            if city_id:
                from .activity_service import ActivityService
                activity_service = ActivityService()
                activities = await activity_service.list_activities(city_id=city_id, limit=100)
                valid_activity_ids = [a.id for a in activities]
                
                if not valid_activity_ids:
                    return []
            
            scan_kwargs = {"Limit": limit}
            
            filter_expressions = []
            expression_values = {}
            expression_attribute_names = {}
            
            if activity_id:
                # If both city_id and activity_id are provided, check if activity_id is in valid_activity_ids
                if city_id and activity_id not in valid_activity_ids:
                    return []
                    
                filter_expressions.append("activity_id = :activity_id")
                expression_values[":activity_id"] = activity_id
            elif city_id:
                # Filter by valid activity IDs from the city
                # Create IN clause: activity_id IN (:a1, :a2, ...)
                # Note: DynamoDB has a limit on expression attribute values, but for < 100 activities it should be fine
                in_clauses = []
                for i, aid in enumerate(valid_activity_ids):
                    key = f":aid{i}"
                    in_clauses.append(key)
                    expression_values[key] = aid
                
                filter_expressions.append(f"activity_id IN ({', '.join(in_clauses)})")
            
            if user_id:
                filter_expressions.append("user_id = :user_id")
                expression_values[":user_id"] = user_id
            
            if status:
                filter_expressions.append("#status = :status")
                expression_values[":status"] = status
                expression_attribute_names["#status"] = "status"
            
            if filter_expressions:
                scan_kwargs["FilterExpression"] = " AND ".join(filter_expressions)
                scan_kwargs["ExpressionAttributeValues"] = expression_values
                if expression_attribute_names:
                    scan_kwargs["ExpressionAttributeNames"] = expression_attribute_names
            
            response = self.submissions_table.scan(**scan_kwargs)
            
            submissions = [Submission(**item) for item in response.get("Items", [])]
            return submissions
        except Exception as e:
            logger.error(f"Error listing submissions: {e}")
            return []
    
    async def grade_submission(
        self,
        submission_id: str,
        grade: float,
        feedback: str,
        grader_id: str
    ) -> Optional[Submission]:
        """
        Grade a submission.
        
        Args:
            submission_id: Submission ID
            grade: Grade value (0-100)
            feedback: Feedback text
            grader_id: ID of grader
            
        Returns:
            Updated submission if successful, None otherwise
        """
        try:
            now = datetime.utcnow().isoformat()
            
            response = self.submissions_table.update_item(
                Key={"id": submission_id},
                UpdateExpression="SET grade = :grade, feedback = :feedback, graded_by = :grader, graded_at = :graded_at, #status = :status, updated_at = :updated",
                ExpressionAttributeNames={"#status": "status"},
                ExpressionAttributeValues={
                    ":grade": grade,
                    ":feedback": feedback,
                    ":grader": grader_id,
                    ":graded_at": now,
                    ":status": SubmissionStatus.GRADED.value,
                    ":updated": now
                },
                ReturnValues="ALL_NEW"
            )
            
            logger.info(f"Submission graded: {submission_id}")
            return Submission(**response["Attributes"])
        except Exception as e:
            logger.error(f"Error grading submission: {e}")
            return None
    
    async def update_submission(
        self,
        submission_id: str,
        submission_update: SubmissionUpdate
    ) -> Optional[Submission]:
        """
        Update submission.
        
        Args:
            submission_id: Submission ID
            submission_update: Update data
            
        Returns:
            Updated submission if successful, None otherwise
        """
        try:
            submission = await self.get_submission(submission_id)
            if not submission:
                return None
            
            update_data = submission_update.dict(exclude_unset=True)
            if not update_data:
                return submission
            
            update_data["updated_at"] = datetime.utcnow().isoformat()
            
            if "status" in update_data and update_data["status"]:
                update_data["status"] = update_data["status"].value
            
            update_expression = "SET " + ", ".join(
                f"#{k} = :{k}" for k in update_data.keys()
            )
            expression_attribute_names = {
                f"#{k}": k for k in update_data.keys()
            }
            expression_attribute_values = {
                f":{k}": v for k, v in update_data.items()
            }
            
            response = self.submissions_table.update_item(
                Key={"id": submission_id},
                UpdateExpression=update_expression,
                ExpressionAttributeNames=expression_attribute_names,
                ExpressionAttributeValues=expression_attribute_values,
                ReturnValues="ALL_NEW"
            )
            
            return Submission(**response["Attributes"])
        except Exception as e:
            logger.error(f"Error updating submission: {e}")
            return None
