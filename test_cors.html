<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test</title>
</head>
<body>
    <h1>CORS Test for MyVillage API Gateway</h1>
    <button onclick="testCORS()">Test CORS</button>
    <div id="result"></div>

    <script>
        async function testCORS() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing CORS...';

            try {
                // Test OPTIONS request (preflight)
                console.log('Testing OPTIONS request...');
                const optionsResponse = await fetch('http://localhost:8000/gemini-chat-with-intent', {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': 'http://localhost:3000',
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type, session_id'
                    }
                });
                
                console.log('OPTIONS Response:', optionsResponse);
                console.log('OPTIONS Headers:', [...optionsResponse.headers.entries()]);

                // Test actual POST request
                console.log('Testing POST request...');
                const postResponse = await fetch('http://localhost:8000/gemini-chat-with-intent', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'session_id': 'test-session-123'
                    },
                    body: JSON.stringify({
                        message: 'Hello, this is a CORS test!'
                    })
                });

                console.log('POST Response:', postResponse);
                console.log('POST Headers:', [...postResponse.headers.entries()]);

                const data = await postResponse.json();
                console.log('Response Data:', data);

                resultDiv.innerHTML = `
                    <h3>CORS Test Results:</h3>
                    <p><strong>OPTIONS Status:</strong> ${optionsResponse.status}</p>
                    <p><strong>POST Status:</strong> ${postResponse.status}</p>
                    <p><strong>Response:</strong> ${JSON.stringify(data, null, 2)}</p>
                    <p style="color: green;"><strong>CORS is working!</strong></p>
                `;

            } catch (error) {
                console.error('CORS Test Error:', error);
                resultDiv.innerHTML = `
                    <h3>CORS Test Failed:</h3>
                    <p style="color: red;"><strong>Error:</strong> ${error.message}</p>
                    <p>Check the browser console for more details.</p>
                `;
            }
        }
    </script>
</body>
</html>
