# MyVillage Developer Handbook

## UI Components

### Button

A versatile button component with multiple variants and states.

#### Import

```typescript
import Button from '@/components/ui/Button';
```

#### Variants

| Variant  | Usage |
|----------|-------|
| `primary` | Primary action buttons |
| `secondary` | Secondary actions |
| `outline` | Less prominent actions |
| `text` | Text buttons for minimal UI |
| `danger` | Destructive actions |

#### Sizes

| Size | Usage |
|------|-------|
| `small` | Dense UI, forms |
| `medium` (default) | Most common size |
| `large` | Prominent actions |

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `'primary' | 'secondary' | 'outline' | 'text' | 'danger'` | `'primary'` | Button style variant |
| `size` | `'small' | 'medium' | 'large'` | `'medium'` | Button size |
| `fullWidth` | `boolean` | `false` | Make button full width |
| `startIcon` | `ReactNode` | - | Icon before text |
| `endIcon` | `ReactNode` | - | Icon after text |
| `isLoading` | `boolean` | `false` | Show loading state |
| `disabled` | `boolean` | `false` | Disable the button |
| `onClick` | `() => void` | - | Click handler |
| `type` | `'button' | 'submit' | 'reset'` | `'button'` | Button type |
| `className` | `string` | - | Additional CSS class |

#### Examples

```tsx
// Basic usage
<Button onClick={handleClick}>Click me</Button>

// With icons (using react-icons)
import { FiDownload, FiArrowRight } from 'react-icons/fi';

<Button 
  variant="primary" 
  startIcon={<FiDownload />}
  endIcon={<FiArrowRight />}
>
  Download
</Button>

// Loading state
<Button 
  variant="primary" 
  isLoading={isLoading}
>
  {isLoading ? 'Processing...' : 'Submit'}
</Button>

// Full width button
<Button fullWidth>Full Width Button</Button>

// Disabled state
<Button disabled>Disabled Button</Button>
```

#### Styling

The button uses the design tokens defined in `src/styles/design-system.ts`. You can customize:

- Colors: `colors.primary`, `colors.secondary`, etc.
- Typography: `typography.button`
- Spacing: Using theme spacing scale
- Border radius: `shape.borderRadius`

### Best Practices

1. **Use appropriate variants**
   - Use `primary` for the main action on a page
   - Use `secondary` for secondary actions
   - Use `outline` for less prominent actions
   - Use `text` for minimal UI
   - Use `danger` for destructive actions

2. **Button states**
   - Always provide visual feedback on hover/active states
   - Disable buttons during async operations
   - Show loading state for operations > 300ms

3. **Accessibility**
   - Always provide meaningful text
   - Use proper ARIA attributes when needed
   - Ensure sufficient color contrast
   - Support keyboard navigation

4. **Performance**
   - Memoize button components in lists
   - Avoid inline functions in render
   - Use React.memo for complex buttons

### Common Patterns

#### Button with Loading State

```tsx
const [isLoading, setIsLoading] = useState(false);

const handleSubmit = async () => {
  setIsLoading(true);
  try {
    await submitData();
  } finally {
    setIsLoading(false);
  }
};

<Button 
  variant="primary" 
  onClick={handleSubmit}
  isLoading={isLoading}
  disabled={isLoading}
>
  {isLoading ? 'Submitting...' : 'Submit'}
</Button>
```

#### Icon Button

```tsx
import { FiSettings } from 'react-icons/fi';

<Button 
  variant="text" 
  startIcon={<FiSettings />}
  aria-label="Settings"
/>
```

#### Button Group

```tsx
<div style={{ display: 'flex', gap: '8px' }}>
  <Button variant="outline">Cancel</Button>
  <Button variant="primary">Save Changes</Button>
</div>
```

### Troubleshooting

#### Button not responding to clicks
- Ensure `onClick` handler is properly passed
- Check if button is not disabled
- Verify no parent element is intercepting events

#### Styling issues
- Check for CSS specificity issues
- Verify design tokens are correctly imported
- Ensure no global styles are overriding component styles

#### TypeScript errors
- Check prop types match expected values
- Ensure all required props are provided
- Verify type imports are correct

## Next Steps

1. Create more UI components following the same pattern
2. Add component tests
3. Document component behavior and edge cases
4. Set up visual regression testing
