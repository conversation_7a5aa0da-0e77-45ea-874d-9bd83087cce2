'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { MapPin, Loader2 } from 'lucide-react'
import { chatApi, City } from '@/lib/api'

interface CitySelectorProps {
    onSelectCity: (cityName: string) => void
}

export default function CitySelector({ onSelectCity }: CitySelectorProps) {
    const [cities, setCities] = useState<City[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    useEffect(() => {
        const fetchCities = async () => {
            setLoading(true)
            setError(null)
            try {
                const cityList = await chatApi.getCities()
                setCities(cityList)
            } catch (err) {
                setError('Failed to load cities')
                console.error('Error fetching cities:', err)
            } finally {
                setLoading(false)
            }
        }
        fetchCities()
    }, [])

    if (loading) {
        return (
            <div className="flex items-center gap-2 mt-3 text-sm text-muted-foreground">
                <Loader2 className="w-4 h-4 animate-spin" />
                Loading cities...
            </div>
        )
    }

    if (error) {
        return (
            <div className="mt-3 text-sm text-destructive">
                {error}
            </div>
        )
    }

    if (cities.length === 0) {
        return (
            <div className="mt-3 text-sm text-muted-foreground">
                No cities available
            </div>
        )
    }

    return (
        <div className="mt-4">
            <p className="text-xs text-muted-foreground mb-2">Select a city:</p>
            <div className="flex flex-wrap gap-2">
                {cities.map((city) => (
                    <Button
                        key={city.id}
                        variant="outline"
                        size="sm"
                        onClick={() => onSelectCity(city.name)}
                        className="h-8 text-xs px-3 bg-background/50 hover:bg-primary hover:text-primary-foreground transition-all"
                    >
                        <MapPin className="w-3 h-3 mr-1.5" />
                        {city.name}
                        {city.state && <span className="ml-1 text-muted-foreground">({city.state})</span>}
                    </Button>
                ))}
            </div>
        </div>
    )
}
