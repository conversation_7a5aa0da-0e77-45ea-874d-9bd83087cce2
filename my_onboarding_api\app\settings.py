"""Application settings and configuration."""
import os
from typing import Optional
from pydantic import BaseSettings

class Settings(BaseSettings):
    """Application settings."""
    
    # Application settings
    debug: bool = os.getenv("DEBUG", "False").lower() in ("true", "1", "t")
    environment: str = os.getenv("ENVIRONMENT", "development")
    
    # API settings
    api_prefix: str = "/api/v1"
    API_V1_STR: str = "/api/v1"  # For backward compatibility
    
    # Security
    secret_key: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    access_token_expire_minutes: int = 60 * 24 * 7  # 7 days
    
    # CORS
    cors_origins: list[str] = ["*"]
    
    # External Services
    google_api_key: Optional[str] = os.getenv("GOOGLE_API_KEY")
    
    class Config:
        """Pydantic config."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

# Create settings instance
settings = Settings()
