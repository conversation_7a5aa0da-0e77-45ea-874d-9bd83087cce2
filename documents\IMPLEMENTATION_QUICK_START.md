# MCP Architecture Implementation Guide
## Quick Start & Code Examples

**Document Version:** 1.0  
**Related Document:** `MVP_MODULARIZATION_PLAN.md`

---

## Quick Start Checklist

- [ ] Read `MVP_MODULARIZATION_PLAN.md` first
- [ ] Set up MCP directory structure
- [ ] Create OnboardingMCP (`main.py`)
- [ ] Create ActivityMCP (`main.py`)
- [ ] Create Orchestrator (`main.py`)
- [ ] Configure shared libraries
- [ ] Run docker-compose for local development
- [ ] Run integration tests
- [ ] Update Next.js frontend

---

## Step-by-Step Implementation

### Step 1: Create MCP Directory Structure

```bash
cd my_onboarding_api

# Create MCP directories
mkdir -p mcp/{onboarding_mcp,activity_mcp,chat_mcp,orchestrator}/{api,services,schemas,core}

# Create shared libraries
mkdir -p shared/{models,exceptions,logging,utils,database}

# Create tests
mkdir -p tests/{unit,integration,e2e}/{onboarding_mcp,activity_mcp,orchestrator}
```

### Step 2: Create Shared Models

**File: `shared/models/base.py`**

```python
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime

class MCPRequest(BaseModel):
    """Base MCP request model."""
    session_id: str
    message: str
    detected_intent: Optional[str] = None
    context: Optional[Dict[str, Any]] = {}

class MCPResponse(BaseModel):
    """Base MCP response model."""
    success: bool
    type: str  # "flow_response", "error", etc.
    data: Optional[Dict[str, Any]] = {}
    error: Optional[Dict[str, Any]] = None

class SessionData(BaseModel):
    """Session data model."""
    session_id: str
    created_at: datetime
    last_activity: datetime
    flow_type: Optional[str] = None
    flow_step: Optional[str] = None
    collected_data: Dict[str, Any] = {}
    user_data: Dict[str, Any] = {}
```

### Step 3: OnboardingMCP - Minimal Implementation

**File: `mcp/onboarding_mcp/main.py`**

```python
from fastapi import FastAPI, HTTPException, status
from contextlib import asynccontextmanager
from typing import Dict, Any
from datetime import datetime, timezone
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# In-memory session storage (TODO: Replace with Redis)
SESSIONS: Dict[str, Dict[str, Any]] = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Starting OnboardingMCP")
    yield
    logger.info("Shutting down OnboardingMCP")

app = FastAPI(
    title="OnboardingMCP",
    version="1.0.0",
    lifespan=lifespan
)

class SessionManager:
    """Simple session manager."""
    
    @staticmethod
    def get_session(session_id: str) -> Dict[str, Any]:
        if session_id not in SESSIONS:
            SESSIONS[session_id] = {
                "session_id": session_id,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "flow_type": None,
                "flow_step": None,
                "collected_data": {},
                "status": "active"
            }
        return SESSIONS[session_id]
    
    @staticmethod
    def update_session(session_id: str, flow_type: str, flow_step: str, data: Dict[str, Any]):
        session = SessionManager.get_session(session_id)
        session["flow_type"] = flow_type
        session["flow_step"] = flow_step
        session["collected_data"].update(data)
        session["last_activity"] = datetime.now(timezone.utc).isoformat()

# Routes
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "onboarding_mcp",
        "version": "1.0.0"
    }

@app.post("/mcp/onboarding/session/init")
async def init_session(request: Dict[str, Any]):
    """Initialize new session."""
    session_id = request.get("session_id")
    session = SessionManager.get_session(session_id)
    
    return {
        "success": True,
        "type": "session_initialized",
        "data": {
            "session_id": session_id,
            "created_at": session["created_at"],
            "status": "active"
        }
    }

@app.post("/mcp/onboarding/message")
async def process_message(request: Dict[str, Any]):
    """Process onboarding message."""
    session_id = request.get("session_id")
    message = request.get("message")
    detected_intent = request.get("detected_intent")
    
    logger.info(f"Processing {detected_intent} for session {session_id}")
    
    session = SessionManager.get_session(session_id)
    
    # Simple flow logic
    if detected_intent == "signup":
        if not session.get("flow_type"):
            # Start signup flow
            SessionManager.update_session(
                session_id, 
                "signup", 
                "email",
                {"email": message}
            )
            return {
                "success": True,
                "type": "flow_response",
                "data": {
                    "flow_type": "signup",
                    "current_step": "email",
                    "message": "Great! What's your email?",
                    "flow_state": session["collected_data"]
                }
            }
        else:
            # Continue flow
            current_step = session["flow_step"]
            session["collected_data"][current_step] = message
            
            # Move to next step
            if current_step == "email":
                next_step = "password"
                session["flow_step"] = next_step
                return {
                    "success": True,
                    "type": "flow_response",
                    "data": {
                        "flow_type": "signup",
                        "current_step": next_step,
                        "message": "And your password?",
                        "flow_state": session["collected_data"]
                    }
                }
            elif current_step == "password":
                # Signup complete
                logger.info(f"Signup complete for {session['collected_data'].get('email')}")
                return {
                    "success": True,
                    "type": "flow_complete",
                    "data": {
                        "user": session["collected_data"],
                        "message": "Welcome!",
                        "token": "jwt_token_here"
                    }
                }
    
    elif detected_intent == "login":
        # Similar logic for login
        return {
            "success": True,
            "type": "flow_response",
            "data": {
                "flow_type": "login",
                "current_step": "email",
                "message": "Enter your email:"
            }
        }
    
    return {
        "success": False,
        "type": "error",
        "error": {"message": "Unknown intent"}
    }

@app.get("/mcp/onboarding/session/{session_id}/state")
async def get_session_state(session_id: str):
    """Get session state."""
    session = SessionManager.get_session(session_id)
    return {
        "success": True,
        "type": "flow_state",
        "data": {
            "flow_type": session["flow_type"],
            "current_step": session["flow_step"],
            "collected_data": session["collected_data"]
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
```

### Step 4: ActivityMCP - Minimal Implementation

**File: `mcp/activity_mcp/main.py`**

```python
from fastapi import FastAPI, HTTPException
from contextlib import asynccontextmanager
from typing import Dict, Any, List
import logging
from datetime import datetime, timezone
import uuid

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# In-memory activity storage (TODO: Use DynamoDB)
ACTIVITIES: Dict[str, Dict[str, Any]] = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Starting ActivityMCP")
    yield
    logger.info("Shutting down ActivityMCP")

app = FastAPI(
    title="ActivityMCP",
    version="1.0.0",
    lifespan=lifespan
)

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "activity_mcp",
        "version": "1.0.0"
    }

@app.post("/mcp/activities/")
async def create_activity(request: Dict[str, Any]):
    """Create new activity."""
    activity_id = str(uuid.uuid4())
    activity = {
        "id": activity_id,
        "title": request.get("title"),
        "description": request.get("description"),
        "type": request.get("type"),
        "created_at": datetime.now(timezone.utc).isoformat(),
        "created_by": request.get("created_by")
    }
    ACTIVITIES[activity_id] = activity
    
    logger.info(f"Created activity: {activity_id}")
    
    return {
        "success": True,
        "type": "activity_created",
        "data": activity
    }

@app.get("/mcp/activities/{activity_id}")
async def get_activity(activity_id: str):
    """Get activity by ID."""
    if activity_id not in ACTIVITIES:
        raise HTTPException(status_code=404, detail="Activity not found")
    
    return {
        "success": True,
        "type": "activity_retrieved",
        "data": ACTIVITIES[activity_id]
    }

@app.get("/mcp/activities/")
async def list_activities(limit: int = 10, offset: int = 0):
    """List activities."""
    activities = list(ACTIVITIES.values())
    
    return {
        "success": True,
        "type": "activities_list",
        "data": {
            "activities": activities[offset:offset+limit],
            "total": len(activities),
            "limit": limit,
            "offset": offset
        }
    }

@app.post("/mcp/activities/classify")
async def classify_activity(request: Dict[str, Any]):
    """Classify activity intent."""
    text = request.get("text", "")
    
    # Dummy classification
    intents = [
        {"label": "community_service", "score": 0.85},
        {"label": "learning", "score": 0.12},
        {"label": "other", "score": 0.03}
    ]
    
    return {
        "success": True,
        "type": "intent_classification",
        "data": {"intents": intents}
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
```

### Step 5: Orchestrator - Minimal Implementation

**File: `mcp/orchestrator/main.py`**

```python
from fastapi import FastAPI, HTTPException, Header
from contextlib import asynccontextmanager
from typing import Annotated, Dict, Any, Optional
import uuid
import httpx
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# MCP Service URLs
MCP_URLS = {
    "onboarding": "http://localhost:8001",
    "activity": "http://localhost:8002",
    "chat": "http://localhost:8003"
}

@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Starting Orchestrator")
    yield
    logger.info("Shutting down Orchestrator")

app = FastAPI(
    title="ConversationOrchestrator",
    version="1.0.0",
    lifespan=lifespan
)

class MessageRouter:
    """Route messages to appropriate MCP."""
    
    SIGNUP_KEYWORDS = {"sign up", "register", "create account", "new account"}
    LOGIN_KEYWORDS = {"login", "sign in", "log in", "authenticate"}
    
    @staticmethod
    def detect_intent(message: str) -> str:
        """Simple intent detection."""
        message_lower = message.lower()
        
        for keyword in MessageRouter.SIGNUP_KEYWORDS:
            if keyword in message_lower:
                return "signup"
        
        for keyword in MessageRouter.LOGIN_KEYWORDS:
            if keyword in message_lower:
                return "login"
        
        return "general"
    
    @staticmethod
    def route(intent: str, flow_context: Optional[Dict[str, Any]] = None) -> str:
        """Route to appropriate MCP."""
        flow_context = flow_context or {}
        
        # Priority 1: Active flow
        if flow_context.get("flow_type") in ["signup", "login"]:
            return "onboarding"
        
        # Priority 2: Intent-based
        if intent in ["signup", "login"]:
            return "onboarding"
        
        if intent == "activity":
            return "activity"
        
        # Default
        return "chat"

# Session store
SESSIONS: Dict[str, Dict[str, Any]] = {}

def get_session(session_id: str) -> Dict[str, Any]:
    """Get or create session."""
    if session_id not in SESSIONS:
        SESSIONS[session_id] = {
            "session_id": session_id,
            "flow_type": None,
            "flow_step": None,
            "intents": []
        }
    return SESSIONS[session_id]

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "orchestrator",
        "version": "1.0.0"
    }

@app.post("/message")
async def process_message(
    request: Dict[str, Any],
    session_id: Annotated[Optional[str], Header()] = None
):
    """
    Main endpoint: Process user message.
    
    1. Detect intent
    2. Route to appropriate MCP
    3. Return unified response
    """
    # Generate session ID if not provided
    if not session_id:
        session_id = str(uuid.uuid4())
    
    message = request.get("message", "")
    logger.info(f"Processing message for session {session_id}")
    
    try:
        # Detect intent
        intent = MessageRouter.detect_intent(message)
        logger.info(f"Detected intent: {intent}")
        
        # Get session
        session = get_session(session_id)
        session["intents"].append(intent)
        
        # Route to appropriate MCP
        target_mcp = MessageRouter.route(intent, {"flow_type": session.get("flow_type")})
        logger.info(f"Routing to {target_mcp} MCP")
        
        # Prepare request for MCP
        mcp_request = {
            "session_id": session_id,
            "message": message,
            "detected_intent": intent,
            "context": {
                "flow_type": session.get("flow_type"),
                "flow_step": session.get("flow_step")
            }
        }
        
        # Call MCP service
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{MCP_URLS[target_mcp]}/mcp/{target_mcp}/message",
                json=mcp_request,
                timeout=10
            )
            
            mcp_response = response.json()
        
        # Update session based on MCP response
        if mcp_response.get("success"):
            data = mcp_response.get("data", {})
            if "flow_type" in data:
                session["flow_type"] = data.get("flow_type")
                session["flow_step"] = data.get("current_step")
        
        # Return orchestrator response
        return {
            "success": mcp_response.get("success", False),
            "session_id": session_id,
            "delegated_to": target_mcp,
            "intent_detected": intent,
            "response": mcp_response
        }
        
    except httpx.TimeoutException:
        logger.error(f"MCP timeout for session {session_id}")
        return {
            "success": False,
            "session_id": session_id,
            "error": "Service timeout"
        }
    except Exception as e:
        logger.error(f"Error processing message: {str(e)}")
        return {
            "success": False,
            "session_id": session_id,
            "error": str(e)
        }

@app.get("/session/{session_id}")
async def get_session_info(session_id: str):
    """Get session information."""
    session = get_session(session_id)
    return {
        "success": True,
        "data": session
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

### Step 6: Create Docker Compose for Local Testing

**File: `docker-compose.dev.yml`**

```yaml
version: '3.8'

services:
  onboarding-mcp:
    build:
      context: .
      dockerfile: ./mcp/onboarding_mcp/Dockerfile
    ports:
      - "8001:8000"
    environment:
      - PYTHONUNBUFFERED=1
    command: python -m uvicorn mcp.onboarding_mcp.main:app --host 0.0.0.0 --port 8000

  activity-mcp:
    build:
      context: .
      dockerfile: ./mcp/activity_mcp/Dockerfile
    ports:
      - "8002:8000"
    environment:
      - PYTHONUNBUFFERED=1
    command: python -m uvicorn mcp.activity_mcp.main:app --host 0.0.0.0 --port 8000

  orchestrator:
    build:
      context: .
      dockerfile: ./mcp/orchestrator/Dockerfile
    ports:
      - "8000:8000"
    environment:
      - PYTHONUNBUFFERED=1
    command: python -m uvicorn mcp.orchestrator.main:app --host 0.0.0.0 --port 8000
    depends_on:
      - onboarding-mcp
      - activity-mcp
```

### Step 7: Create Dockerfiles

**File: `mcp/onboarding_mcp/Dockerfile`**

```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "-m", "uvicorn", "mcp.onboarding_mcp.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

Repeat for `activity_mcp/Dockerfile` and `orchestrator/Dockerfile`.

---

## Testing Examples

### Unit Test Example

**File: `tests/unit/test_orchestrator.py`**

```python
import pytest
from mcp.orchestrator.main import MessageRouter

def test_detect_signup_intent():
    """Test signup intent detection."""
    result = MessageRouter.detect_intent("I want to sign up")
    assert result == "signup"

def test_detect_login_intent():
    """Test login intent detection."""
    result = MessageRouter.detect_intent("Can I log in?")
    assert result == "login"

def test_route_signup():
    """Test routing signup to onboarding."""
    target = MessageRouter.route("signup")
    assert target == "onboarding"

def test_route_login():
    """Test routing login to onboarding."""
    target = MessageRouter.route("login", {"flow_type": "login"})
    assert target == "onboarding"

def test_route_active_flow():
    """Test that active flow takes priority."""
    target = MessageRouter.route(
        "general",
        {"flow_type": "signup"}
    )
    assert target == "onboarding"
```

### Integration Test Example

**File: `tests/integration/test_signup_flow.py`**

```python
import pytest
import httpx
import asyncio

@pytest.mark.asyncio
async def test_signup_flow():
    """Test complete signup flow through orchestrator."""
    
    async with httpx.AsyncClient() as client:
        # Step 1: Initialize signup
        response = await client.post(
            "http://localhost:8000/message",
            json={"message": "I want to sign up"},
            headers={"session_id": "test-session-1"}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["intent_detected"] == "signup"
        assert data["delegated_to"] == "onboarding"
        
        # Step 2: Provide email
        response = await client.post(
            "http://localhost:8000/message",
            json={"message": "<EMAIL>"},
            headers={"session_id": "test-session-1"}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["response"]["data"]["current_step"] == "password"
        
        # Step 3: Provide password
        response = await client.post(
            "http://localhost:8000/message",
            json={"message": "secure_password_123"},
            headers={"session_id": "test-session-1"}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["response"]["type"] == "flow_complete"
```

---

## Next: Frontend Integration

After implementing the MCPs, update your Next.js frontend:

**File: `gemini-interface/src/services/api.ts`**

```typescript
// Update to use Orchestrator instead of monolith
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";

export async function sendMessage(message: string, sessionId?: string): Promise<any> {
  const response = await fetch(`${API_BASE_URL}/message`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      ...(sessionId && { "session_id": sessionId })
    },
    body: JSON.stringify({ message })
  });

  if (!response.ok) {
    throw new Error(`API error: ${response.statusText}`);
  }

  return response.json();
}
```

---

## Running Everything

```bash
# Start all services
docker-compose -f docker-compose.dev.yml up

# In another terminal, test
curl -X POST http://localhost:8000/message \
  -H "Content-Type: application/json" \
  -H "session_id: test-123" \
  -d '{"message": "I want to sign up"}'

# Expected response
{
  "success": true,
  "session_id": "test-123",
  "delegated_to": "onboarding",
  "intent_detected": "signup",
  "response": {
    "success": true,
    "type": "flow_response",
    "data": {
      "flow_type": "signup",
      "current_step": "email",
      "message": "Great! What's your email?"
    }
  }
}
```

---

## Common Issues & Solutions

### Issue: MCPs can't communicate
**Solution:** Check firewall, ensure docker-compose ports are exposed, verify URLs in Orchestrator config

### Issue: Session state lost between calls
**Solution:** Include `session_id` in request headers, ensure all MCPs use same session store (Redis/DB)

### Issue: High latency
**Solution:** Profile with `--profile` flag, consider caching, check external API calls

---

## Next Steps

1. Implement full OnboardingMCP with real auth_service
2. Implement full ActivityMCP with real activity_service
3. Add Redis for distributed session management
4. Integrate with existing DynamoDB
5. Deploy to staging environment
6. A/B test with real users
7. Migrate monolith traffic gradually

---

**For detailed architecture:** See `MVP_MODULARIZATION_PLAN.md`
