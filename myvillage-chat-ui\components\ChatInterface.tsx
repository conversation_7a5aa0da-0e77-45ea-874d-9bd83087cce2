'use client'

import { useState, useRef, useEffect } from 'react'
import axios from 'axios'
import ChatMessage from './ChatMessage'
import ChatInput from './ChatInput'
import Sidebar from './Sidebar'
import { Button } from './ui/button'
import { Card } from './ui/card'
import { ScrollArea } from './ui/scroll-area'
import { Badge } from './ui/badge'
import { MessageSquare, Sparkles, Gift, UserPlus, LogOut, User as UserIcon, FileText } from 'lucide-react'
import { authUtils, User } from '@/lib/auth'
import { chatApi, Conversation, ChatMessage as ApiChatMessage } from '@/lib/api'
import { v4 as uuidv4 } from 'uuid'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  intent?: string
  mcp_service?: string
  data?: any
}

export default function ChatInterface() {
  const [messages, setMessages] = useState<Message[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [userId] = useState(`user-${Date.now()}`)
  const [user, setUser] = useState<User | null>(null)
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null)
  const [input, setInput] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Load user from localStorage on mount
  useEffect(() => {
    const savedUser = authUtils.getUser()
    if (savedUser) {
      setUser(savedUser)
    }
  }, [])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const loadConversation = async (conversation: Conversation) => {
    if (!user) return

    setIsLoading(true)
    setCurrentConversationId(conversation.conversationId)

    try {
      // Use conversationId as chatType if it's not a new conversation
      const chatType = conversation.chatType || conversation.conversationId
      const history = await chatApi.getChatHistory(user.id, chatType)

      // Convert API messages to UI messages
      const uiMessages: Message[] = history.map(msg => ({
        id: msg.id,
        role: msg.role,
        content: msg.message,
        timestamp: new Date(msg.createdAt),
        // We might lose some metadata like intent/mcp_service if not stored in DynamoDB
        // but basic message content is preserved
      }))

      setMessages(uiMessages)
    } catch (error) {
      console.error('Failed to load conversation', error)
    } finally {
      setIsLoading(false)
    }
  }

  const startNewChat = () => {
    setMessages([])
    setCurrentConversationId(null)
  }

  const sendMessage = async (text: string) => {
    if (!text.trim()) return

    // Check if user is trying to access protected features
    const protectedIntents = ['activities', 'rewards', 'activity']
    const lowerText = text.toLowerCase()
    const isProtectedRequest = protectedIntents.some(intent =>
      lowerText.includes(intent) || lowerText.includes('show') || lowerText.includes('list')
    )

    if (isProtectedRequest && !user) {
      const loginPrompt: Message = {
        id: `msg-${Date.now()}-auth`,
        role: 'assistant',
        content: '🔒 Please log in or sign up first to access activities and rewards. Type "I want to log in" or "I want to sign up".',
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, loginPrompt])
      return
    }

    // Add user message
    const userMessage: Message = {
      id: `msg-${Date.now()}`,
      role: 'user',
      content: text,
      timestamp: new Date(),
    }
    setMessages((prev) => [...prev, userMessage])
    setIsLoading(true)

    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8100'
      const currentUserId = user?.id || userId

      // Determine chat type
      // If existing conversation, use its ID (which is the chatType)
      // If new conversation, generate a new unique chatType
      let chatType = currentConversationId
      if (!chatType) {
        chatType = `GeneralWeb-${Date.now()}`
        setCurrentConversationId(chatType)
      }

      const response = await axios.post(`${apiUrl}/chat`, {
        user_id: currentUserId,
        text: text,
        chat_type: chatType // Pass chat_type to backend (needs backend update to accept this)
      })

      // Note: The current orchestrator chat endpoint might not accept chat_type in the request body
      // We might need to update the ChatRequest schema in the backend or pass it differently
      // For now, we'll assume the backend handles it or defaults to GeneralWeb
      // If we want true multi-chat support, we MUST update the backend to accept chat_type

      // Check if login/signup was successful
      if (response.data.intent === 'login' || response.data.intent === 'signup') {
        if (response.data.success && response.data.data?.user) {
          const userData = response.data.data.user

          // Map various role formats to our standard roles
          let userRole: 'admin' | 'stakeholder' | 'user' | 'guest' = 'user'
          const roleStr = (userData.role || userData.assignedRole || '').toLowerCase()

          if (roleStr === 'admin' || roleStr === 'administrator' || roleStr === 'super_admin') {
            userRole = 'admin'
          } else if (roleStr === 'stakeholder') {
            userRole = 'stakeholder'
          } else if (roleStr === 'member' || roleStr === 'user') {
            userRole = 'user'
          }

          const newUser: User = {
            id: userData.id || userData.user_id || userId,
            email: userData.email || '',
            name: userData.name || 'User',
            role: userRole,
            token: response.data.data.access_token,
          }
          authUtils.saveUser(newUser)
          setUser(newUser)
        }
      }

      // Add assistant response
      const assistantMessage: Message = {
        id: `msg-${Date.now()}-response`,
        role: 'assistant',
        content: response.data.response || response.data.message || 'No response',
        timestamp: new Date(),
        intent: response.data.intent,
        mcp_service: response.data.mcp_service || response.data.routed_to,
        // Preserve the entire data object to keep flags like needs_city_list
        data: response.data.data || {
          activities: response.data.activities,
          submissions: response.data.submissions
        }
      }
      setMessages((prev) => [...prev, assistantMessage])
    } catch (error) {
      console.error('Error sending message:', error)
      const errorMessage: Message = {
        id: `msg-${Date.now()}-error`,
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please make sure the backend services are running.',
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogout = () => {
    authUtils.clearUser()
    setUser(null)
    setMessages([])
    setCurrentConversationId(null)
  }

  // Role-based quick actions
  const getQuickActions = () => {
    if (!user) {
      return [
        {
          icon: UserPlus,
          label: "Sign Up",
          message: "I want to sign up",
          description: "Create an account",
        },
        {
          icon: UserIcon,
          label: "Log In",
          message: "I want to log in",
          description: "Access your account",
        },
      ]
    }

    // Admin quick actions
    if (user.role === 'admin') {
      return [
        {
          icon: Sparkles,
          label: "Manage Activities",
          message: "Show all activities",
          description: "View and manage activities",
        },
        {
          icon: Gift,
          label: "Manage Rewards",
          message: "Show all rewards",
          description: "View and manage rewards",
        },
        {
          icon: UserIcon,
          label: "User Management",
          message: "Show user statistics",
          description: "Manage users",
        },
        {
          icon: FileText,
          label: "My Submissions",
          message: "Show my submissions",
          description: "View my submissions",
        },
      ]
    }

    // Stakeholder quick actions
    if (user.role === 'stakeholder') {
      return [
        {
          icon: Sparkles,
          label: "View Activities",
          message: "Show me available activities",
          description: "Browse activities",
        },
        {
          icon: Gift,
          label: "View Rewards",
          message: "Check rewards program",
          description: "View rewards",
        },
        {
          icon: FileText,
          label: "My Submissions",
          message: "Show my submissions",
          description: "View my submissions",
        },
      ]
    }

    // Default user quick actions
    return [
      {
        icon: Sparkles,
        label: "Activities",
        message: "Show me available activities",
        description: "Explore activities",
      },
      {
        icon: Gift,
        label: "Rewards",
        message: "Check my rewards",
        description: "View your rewards",
      },
      {
        icon: FileText,
        label: "My Submissions",
        message: "Show my submissions",
        description: "View my submissions",
      },
    ]
  }

  const quickActions = getQuickActions()

  const handleEditMessage = (content: string) => {
    setInput(content)
  }

  const handleResubmit = (messageId: string, newContent: string) => {
    const messageIndex = messages.findIndex(m => m.id === messageId)
    if (messageIndex !== -1) {
      // Keep messages up to the edited message (exclusive of the edited message itself, 
      // as sendMessage will add the new user message)
      const newHistory = messages.slice(0, messageIndex)
      setMessages(newHistory)
      sendMessage(newContent)
    }
  }

  return (
    <div className="flex h-screen bg-background">
      <Sidebar
        onClearChat={startNewChat}
        onSelectConversation={loadConversation}
        currentConversationId={currentConversationId}
      />

      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="bg-card border-b px-6 py-4 shadow-sm">
          <div className="flex items-center justify-between max-w-4xl mx-auto">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center">
                <MessageSquare className="w-6 h-6 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-xl font-semibold">MyVillage AI Assistant</h1>
                <p className="text-xs text-muted-foreground">Powered by MCP Services</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              {user ? (
                <>
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center">
                      <UserIcon className="w-4 h-4 text-secondary-foreground" />
                    </div>
                    <div className="hidden sm:block">
                      <p className="text-sm font-medium">{user.name}</p>
                      <div className="flex items-center gap-2">
                        <p className="text-xs text-muted-foreground">{user.email}</p>
                        <Badge
                          variant={user.role === 'admin' ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {user.role === 'admin' ? 'Admin' : user.role === 'stakeholder' ? 'Stakeholder' : 'User'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleLogout}
                    className="text-muted-foreground hover:text-foreground"
                  >
                    <LogOut className="w-4 h-4" />
                  </Button>
                </>
              ) : (
                <Badge variant="outline" className="text-muted-foreground">
                  <UserIcon className="w-3 h-3 mr-1" />
                  Guest
                </Badge>
              )}
              <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
            </div>
          </div>
        </header>

        {/* Messages */}
        <ScrollArea className="flex-1">
          <div className="container max-w-4xl mx-auto px-4 py-6">
            {messages.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full min-h-[60vh] text-center">
                <div className="w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                  <MessageSquare className="w-10 h-10 text-primary" />
                </div>
                <h2 className="text-3xl font-bold mb-3">Welcome to MyVillage AI</h2>
                <p className="text-muted-foreground max-w-md mb-8">
                  Start a conversation! Ask about activities, rewards, or sign up for new features.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full max-w-3xl">
                  {quickActions.map((action) => (
                    <Card
                      key={action.label}
                      className="p-4 hover:shadow-md transition-all cursor-pointer group hover:border-primary"
                      onClick={() => sendMessage(action.message)}
                    >
                      <div className="flex flex-col items-center text-center gap-3">
                        <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                          <action.icon className="w-6 h-6 text-primary" />
                        </div>
                        <div>
                          <div className="font-semibold mb-1">{action.label}</div>
                          <div className="text-sm text-muted-foreground">
                            {action.description}
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {messages.map((message) => (
                  <ChatMessage
                    key={message.id}
                    message={message}
                    onResubmit={(content) => handleResubmit(message.id, content)}
                    onReply={sendMessage}
                  />
                ))}
                {isLoading && (
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-primary rounded-full typing-dot"></div>
                      <div className="w-2 h-2 bg-primary rounded-full typing-dot"></div>
                      <div className="w-2 h-2 bg-primary rounded-full typing-dot"></div>
                    </div>
                    <span className="text-sm">AI is thinking...</span>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Input */}
        <ChatInput
          onSend={sendMessage}
          isLoading={isLoading}
          quickActions={messages.length > 0 ? quickActions : []}
          input={input}
          setInput={setInput}
        />
      </div >
    </div >
  )
}
