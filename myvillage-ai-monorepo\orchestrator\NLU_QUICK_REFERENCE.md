# NLU Service - Quick Reference Card

## 🚀 Quick Start

```python
from app.services.intent_detector import IntentDetector

detector = IntentDetector()

# Advanced detection with entities
result = await detector.detect_advanced("show quizzes in Mumbai")

# Extract filters for API calls
filters = detector.extract_filters_from_entities(result['entities'])
```

## 📦 Entity Types

| Type | Examples | Pattern |
|------|----------|---------|
| `activity_type` | quiz, assignment, project | "show **quizzes**" |
| `status` | submitted, graded, pending | "**submitted** assignments" |
| `city` | Mumbai, Delhi, Bangalore | "activities in **Mumbai**" |
| `date` | today, tomorrow, 2025-12-15 | "due **tomorrow**" |
| `date_range` | this week, next month | "events **this week**" |
| `subject` | math, science, english | "**math** homework" |
| `points` | 500 | "redeem **500 points**" |
| `activity_id` | ACT-123 | "activity_id: **ACT-123**" |
| `user_id` | USER-456 | "user_id: **USER-456**" |
| `action` | create, submit, list | "**submit** assignment" |

## 🎯 Common Patterns

### Activity Queries
```python
"show quizzes in Mumbai" 
→ activity_type=quiz, city=Mumbai

"list assignments due this week"
→ activity_type=assignment, date_range=this_week

"what events are happening tomorrow"
→ activity_type=event, date=tomorrow
```

### Submission Queries
```python
"show submitted assignments"
→ activity_type=assignment, status=submitted

"my graded projects"
→ activity_type=project, status=graded

"submit math homework"
→ activity_type=assignment, subject=math, action=submit
```

### Filtered Queries
```python
"show activities in Bangalore due this week"
→ city=Bangalore, date_range=this_week

"submitted assignments for math"
→ status=submitted, activity_type=assignment, subject=math
```

## 💡 Response Structure

```python
{
    "intent": "activity_list",           # Primary intent
    "confidence": 0.95,                  # 0-1 score
    "sentiment": "positive",             # positive/negative/neutral
    "action": "list",                    # Extracted action
    "entities": {                        # Extracted entities
        "activity_type": "quiz",
        "city": "Mumbai"
    },
    "secondary_intents": [],             # Other possible intents
    "full_analysis": {...}               # Complete NLU data
}
```

## 🔧 Common Operations

### Extract Filters
```python
result = await detector.detect_advanced(message)
filters = detector.extract_filters_from_entities(result['entities'])
# Use filters in API calls
activities = await client.list_activities(**filters)
```

### Check Confidence
```python
if result['confidence'] >= 0.9:
    # High confidence - proceed
    execute_action()
elif result['confidence'] >= 0.7:
    # Medium - confirm
    ask_confirmation()
else:
    # Low - clarify
    request_clarification()
```

### Use Context
```python
context = {
    "user_id": "user123",
    "last_intent": "activity_list"
}
result = await detector.detect_advanced(message, context=context)
```

### Get MCP Service
```python
service = detector.get_mcp_service(result['intent'])
# Returns: "activities", "onboarding", "rewards", "approval"
```

## 📅 Date Patterns

| Pattern | Result |
|---------|--------|
| "today" | Current date |
| "tomorrow" | Next day |
| "yesterday" | Previous day |
| "this week" | Monday-Sunday current week |
| "next week" | Monday-Sunday next week |
| "this month" | 1st-last day current month |
| "next month" | 1st-last day next month |
| "2025-12-15" | Specific date (ISO) |
| "15/12/2025" | Specific date (DD/MM/YYYY) |

## 😊 Sentiment Detection

| Sentiment | Keywords |
|-----------|----------|
| **Positive** | great, good, excellent, thanks, love, happy |
| **Negative** | bad, terrible, confused, difficult, problem |
| **Neutral** | (default) |

## 🎯 Intent Mapping

| Intent | MCP Service | Keywords |
|--------|-------------|----------|
| `activity_list` | activities | "show activities", "list events" |
| `activity_create` | activities | "create activity", "new quiz" |
| `activity_submit` | activities | "submit", "turn in" |
| `submission_list` | activities | "show submissions", "my submissions" |
| `rewards_get` | rewards | "my rewards", "check points" |
| `rewards_redeem` | rewards | "redeem", "use points" |
| `approval_pending` | approval | "pending approvals" |
| `signup` | onboarding | "sign up", "register" |
| `login` | onboarding | "log in", "sign in" |

## ⚡ Performance Tips

1. **Cache simple queries**: Use `@lru_cache` for frequent patterns
2. **Provide context**: Improves accuracy by 10-15%
3. **Check confidence**: Validate before automated actions
4. **Extract filters early**: Reduce conversation steps
5. **Log results**: Monitor and improve patterns

## 🧪 Testing

```bash
# Run examples
python -m examples.nlu_examples

# Run tests
pytest tests/test_nlu_service.py -v
```

## 📚 Full Documentation

- **Complete Guide**: `NLU_SERVICE_GUIDE.md`
- **Implementation**: `NLU_IMPLEMENTATION_SUMMARY.md`
- **Examples**: `examples/nlu_examples.py`
- **Tests**: `tests/test_nlu_service.py`

## 🆘 Troubleshooting

| Problem | Solution |
|---------|----------|
| Low confidence | Add more keywords to patterns |
| Missing entities | Check entity pattern exists |
| Wrong intent | Adjust confidence scores |
| No date extraction | Verify date format matches patterns |

---

**Version**: 1.0.0 | **Updated**: Dec 2, 2025
