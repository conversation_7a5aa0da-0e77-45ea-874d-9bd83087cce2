'use client';

interface AuthenticatedQuickActionsProps {
  onAction: (action: string) => void;
  userRole?: string;
}

export default function AuthenticatedQuickActions({ onAction, userRole }: AuthenticatedQuickActionsProps) {
  // Common actions for all users
  const commonActions = [
    {
      emoji: '📚',
      label: 'Book Drive',
      prompt: 'I want to participate in the weekend book drive activity.'
    },
    {
      emoji: '🌱',
      label: 'Tree Plantation',
      prompt: 'Tell me about the tree plantation event and how to join.'
    },
    {
      emoji: '📅',
      label: "Today's Activities",
      prompt: 'Show me my activities for today.'
    },
    {
      emoji: '🎯',
      label: 'My Goals',
      prompt: 'Show me my current goals and progress.'
    }
  ];

  // Actions specific to roles with approval permissions
  const approvalActions = [
    {
      emoji: '✅',
      label: 'Pending Approvals',
      prompt: 'Show me my pending approvals.'
    },
    {
      emoji: '📊',
      label: 'Activity Report',
      prompt: 'Generate a report of recent activities.'
    },
    {
      emoji: '📈',
      label: 'Impact Analytics',
      prompt: 'Show me my performance analytics and impact predictions.'
    }
  ];

  // Combine actions based on user role
  const quickActions = userRole === 'admin' || userRole === 'manager' 
    ? [...commonActions, ...approvalActions]
    : commonActions;

  return (
    <div className="w-full max-w-3xl mx-auto px-4 mb-4">
      <h3 className="text-sm font-medium text-gray-500 mb-3 text-center">Quick Actions</h3>
      <div className="flex flex-wrap justify-center gap-3">
        {quickActions.map((action, index) => (
          <button
            key={index}
            onClick={() => onAction(action.prompt)}
            className="flex items-center gap-2 px-4 py-2 bg-card border border-border rounded-full text-sm font-medium text-foreground hover:bg-muted transition-colors duration-200 shadow-sm whitespace-nowrap"
          >
            <span className="text-base">{action.emoji}</span>
            <span>{action.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
}
