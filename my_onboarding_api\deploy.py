#!/usr/bin/env python3
"""
Unified deployment script for both API server and MCP server.

This script launches both the FastAPI server and the MCP server concurrently,
making it suitable for deployment on platforms like Render.
"""

import os
import sys
import signal
import logging
import multiprocessing
from pathlib import Path
import uvicorn

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Import logging configuration
try:
    from logging_config import LOGGING_CONFIG
    import logging.config
    logging.config.dictConfig(LOGGING_CONFIG)
except ImportError:
    # Fallback basic config if logging_config fails
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

logger = logging.getLogger(__name__)

def run_api_server():
    """Run the FastAPI server with enhanced logging."""
    try:
        from app.core.config import settings
        from app.core.logging import setup_logging
        
        # Initialize our custom logging
        setup_logging()
        
        logger = logging.getLogger("api-server")
        logger.info(f"Starting API server on {settings.host}:{settings.port}")
        logger.info(f"Log level: {settings.log_level}")
        
        # Run the FastAPI application
        uvicorn.run(
            "app.main:app",
            host=settings.host,
            port=settings.port,
            reload=False,
            log_level="debug",
            access_log=True,
            server_header=False,
            date_header=False,
            log_config=LOGGING_CONFIG,
        )
    except Exception as e:
        logger.error(f"Failed to start API server: {e}", exc_info=True)
        raise

def run_mcp_server():
    """Run the MCP server with enhanced logging."""
    try:
        # Set up logging for MCP server
        logger = logging.getLogger("mcp-server")
        logger.info("Starting MCP server...")
        
        # Import and run MCP server
        from mcp_server import run_mcp_server
        run_mcp_server()
        
    except Exception as e:
        logger = logging.getLogger("mcp-server")
        logger.error(f"Failed to start MCP server: {e}", exc_info=True)
        raise

def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info(f"Received signal {signum}, shutting down...")
    sys.exit(0)

def main():
    """Main entry point for unified deployment."""
    logger.info("🚀 Starting unified deployment for API and MCP servers")
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Check if we should run only one server (for debugging)
    run_mode = os.getenv("RUN_MODE", "both").lower()
    
    if run_mode == "api":
        logger.info("Running in API-only mode")
        run_api_server()
    elif run_mode == "mcp":
        logger.info("Running in MCP-only mode")
        run_mcp_server()
    elif run_mode == "both":
        logger.info("Running both API and MCP servers")
        
        # Create processes for both servers with proper error handling
        api_process = multiprocessing.Process(
            target=run_api_server,
            name="API-Server"
        )
        
        mcp_process = multiprocessing.Process(
            target=run_mcp_server,
            name="MCP-Server"
        )
        
        try:
            logger.info("Starting API and MCP servers...")
            # Start both processes
            api_process.start()
            mcp_process.start()
            
            logger.info("Both servers started successfully")
            logger.info(f"API Server PID: {api_process.pid}")
            logger.info(f"MCP Server PID: {mcp_process.pid}")
            
            # Wait for both processes
            api_process.join()
            mcp_process.join()
            
        except KeyboardInterrupt:
            logger.info("Received interrupt signal, shutting down...")
        except Exception as e:
            logger.error(f"Error in main process: {e}")
        finally:
            # Ensure both processes are terminated
            if api_process.is_alive():
                logger.info("Terminating API server...")
                api_process.terminate()
                api_process.join(timeout=5)
                if api_process.is_alive():
                    api_process.kill()
            
            if mcp_process.is_alive():
                logger.info("Terminating MCP server...")
                mcp_process.terminate()
                mcp_process.join(timeout=5)
                if mcp_process.is_alive():
                    mcp_process.kill()
            
            logger.info("Shutdown complete")
    else:
        logger.error(f"Invalid RUN_MODE: {run_mode}. Use 'api', 'mcp', or 'both'")
        sys.exit(1)

if __name__ == "__main__":
    # Ensure we're using spawn method for multiprocessing (required on some platforms)
    multiprocessing.set_start_method('spawn', force=True)
    main()
