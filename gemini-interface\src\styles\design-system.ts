// Design System for MyVillage Assistant

export const colors = {
  // Primary Colors
  primary: {
    50: '#E8F4FD',
    100: '#C5E0F5',
    200: '#9DCAF2',
    300: '#72B3ED',
    400: '#4DA2E9',
    500: '#1A8CFF', // Main brand blue
    600: '#0D7AE5',
    700: '#0064CC',
    800: '#0052B3',
    900: '#003366',
  },
  
  // Secondary Colors
  secondary: {
    50: '#E8F5E9',
    100: '#C8E6C9',
    200: '#A5D6A7',
    300: '#81C784',
    400: '#66BB6A',
    500: '#4CAF50', // Main green
    600: '#43A047',
    700: '#388E3C',
    800: '#2E7D32',
    900: '#1B5E20',
  },

  // Neutral Colors
  neutral: {
    50: '#F8F9FA',
    100: '#F1F3F5',
    200: '#E9ECEF',
    300: '#DEE2E6',
    400: '#CED4DA',
    500: '#ADB5BD',
    600: '#6C757D',
    700: '#495057',
    800: '#343A40',
    900: '#212529',
  },

  // Status Colors
  success: {
    light: '#D4EDDA',
    main: '#28A745',
    dark: '#1E7E34',
  },
  warning: {
    light: '#FFF3CD',
    main: '#FFC107',
    dark: '#D39E00',
  },
  error: {
    light: '#F8D7DA',
    main: '#DC3545',
    dark: '#BD2130',
  },
  info: {
    light: '#D1ECF1',
    main: '#17A2B8',
    dark: '#117A8B',
  },
};

export const typography = {
  fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  fontWeights: {
    light: 300,
    regular: 400,
    medium: 500,
    semiBold: 600,
    bold: 700,
  },
  h1: {
    fontSize: '2.5rem', // 40px
    lineHeight: 1.2,
    fontWeight: 700,
  },
  h2: {
    fontSize: '2rem', // 32px
    lineHeight: 1.25,
    fontWeight: 600,
  },
  h3: {
    fontSize: '1.75rem', // 28px
    lineHeight: 1.3,
    fontWeight: 600,
  },
  h4: {
    fontSize: '1.5rem', // 24px
    lineHeight: 1.35,
    fontWeight: 600,
  },
  h5: {
    fontSize: '1.25rem', // 20px
    lineHeight: 1.4,
    fontWeight: 600,
  },
  body1: {
    fontSize: '1rem', // 16px
    lineHeight: 1.5,
    fontWeight: 400,
  },
  body2: {
    fontSize: '0.875rem', // 14px
    lineHeight: 1.5,
    fontWeight: 400,
  },
  caption: {
    fontSize: '0.75rem', // 12px
    lineHeight: 1.5,
    fontWeight: 400,
  },
  button: {
    fontSize: '0.875rem',
    lineHeight: 1.75,
    fontWeight: 500,
    textTransform: 'uppercase',
    letterSpacing: '0.02857em',
  },
};

export const shadows = {
  none: 'none',
  xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
};

export const breakpoints = {
  xs: '0px',
  sm: '600px',
  md: '960px',
  lg: '1280px',
  xl: '1920px',
};

export const zIndex = {
  appBar: 1100,
  drawer: 1200,
  modal: 1300,
  snackbar: 1400,
  tooltip: 1500,
};

export const shape = {
  borderRadius: 8,
  borderRadiusSm: 4,
  borderRadiusMd: 8,
  borderRadiusLg: 16,
  borderRadiusXl: 24,
};
