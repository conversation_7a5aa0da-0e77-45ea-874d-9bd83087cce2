"""Tool: List Activities."""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "common"))

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from typing import Optional, List
import logging

from common.models.base import BaseResponse
from ..services.activity_service import ActivityService

logger = logging.getLogger(__name__)
router = APIRouter()
activity_service = ActivityService()


@router.get("/list_activities", response_model=BaseResponse)
async def list_activities(
    activity_type: Optional[str] = Query(None, description="Filter by activity type"),
    status: Optional[str] = Query(None, description="Filter by status"),
    city_id: Optional[str] = Query(None, description="Filter by city ID"),
    limit: int = Query(10, ge=1, le=100, description="Maximum number of results")
):
    """
    List available activities with optional filters.
    
    This tool retrieves a list of activities, optionally filtered by type and status.
    
    Args:
        activity_type: Optional activity type filter
        status: Optional status filter
        limit: Maximum number of results (1-100)
        
    Returns:
        BaseResponse with list of activities
    """
    try:
        logger.info(f"Listing activities: type={activity_type}, status={status}, city_id={city_id}, limit={limit}")
        
        activities = await activity_service.list_activities(
            activity_type=activity_type,
            status=status,
            city_id=city_id,
            limit=limit
        )
        
        activities_data = [
            {
                "id": activity.id,
                "title": activity.title,
                "description": activity.description,
                "activity_type": activity.activity_type.value,
                "status": activity.status.value,
                "created_by": activity.created_by,
                "created_at": activity.created_at.isoformat(),
                "updated_at": activity.updated_at.isoformat(),
                "due_date": activity.due_date.isoformat() if activity.due_date else None,
                "points": activity.points,
                "submission_count": activity.submission_count,
                "city_id": activity.city_id
            }
            for activity in activities
        ]
        
        logger.info(f"Found {len(activities_data)} activities")
        
        return BaseResponse(
            success=True,
            message=f"Found {len(activities_data)} activities",
            data={"activities": activities_data, "count": len(activities_data)}
        )
    except Exception as e:
        logger.error(f"Error listing activities: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list activities: {str(e)}"
        )
