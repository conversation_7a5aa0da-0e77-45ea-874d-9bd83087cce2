# 🎉 START HERE - MyVillage AI Complete System

## ⚡ Quick Start (Copy & Paste)

### Terminal 1: Backend Services
```bash
cd myvillage-ai-monorepo
./start-all.sh
```

### Terminal 2: Chat UI
```bash
cd myvillage-chat-ui
./start-ui.sh
```

### Browser
Open: **http://localhost:3000**

---

## ✅ What You Get

1. **4 Backend Services** running on ports 8001, 8002, 8003, 8100
2. **ChatGPT-like UI** on port 3000
3. **AI-powered chat** with intent detection
4. **MCP microservices** architecture

---

## 🧪 Quick Test

Type in the chat:
- "Show me activities" → Activities MCP
- "Check my rewards" → Rewards MCP  
- "I want to sign up" → Onboarding MCP

---

## 📚 Full Documentation

- **Complete Guide**: [COMPLETE_SETUP_GUIDE.md](./COMPLETE_SETUP_GUIDE.md)
- **Backend Details**: [myvillage-ai-monorepo/README.md](./myvillage-ai-monorepo/README.md)
- **Chat UI Details**: [myvillage-chat-ui/SETUP.md](./myvillage-chat-ui/SETUP.md)

---

## 🛑 Stop Everything

```bash
# Stop backend
cd myvillage-ai-monorepo
./stop-all.sh

# Stop UI (Ctrl+C in terminal)
```

---

That's it! You're ready to go! 🚀
