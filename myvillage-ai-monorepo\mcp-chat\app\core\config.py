from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    """Application settings."""
    
    # Service info
    service_name: str = "MCP Chat Service"
    service_version: str = "1.0.0"
    
    # AWS Configuration
    aws_region: str = "us-east-1"
    dynamodb_table_name: str = "ChatGPT"
    
    # API Configuration
    api_host: str = "0.0.0.0"
    api_port: int = os.getenv("API_PORT", 8000)
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"


settings = Settings()
