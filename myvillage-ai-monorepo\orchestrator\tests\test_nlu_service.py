"""Unit tests for NLU Service."""
import pytest
from datetime import datetime, timedelta
from app.services.nlu_service import NLUService, EntityType, Sentiment


class TestNLUService:
    """Test cases for NLU Service."""
    
    @pytest.fixture
    def nlu_service(self):
        """Create NLU service instance."""
        return NLUService()
    
    @pytest.mark.asyncio
    async def test_basic_intent_detection(self, nlu_service):
        """Test basic intent detection."""
        result = await nlu_service.analyze_intent("show me all activities")
        
        assert result["primary_intent"] == "activity_list"
        assert result["confidence"] > 0.8
        assert result["sentiment"] in [Sentiment.POSITIVE, Sentiment.NEGATIVE, Sentiment.NEUTRAL]
    
    @pytest.mark.asyncio
    async def test_activity_type_extraction(self, nlu_service):
        """Test activity type entity extraction."""
        test_cases = [
            ("show me quizzes", "quiz"),
            ("list all assignments", "assignment"),
            ("what projects are available", "project"),
            ("show discussion topics", "discussion")
        ]
        
        for text, expected_type in test_cases:
            result = await nlu_service.analyze_intent(text)
            assert EntityType.ACTIVITY_TYPE in result["entities"]
            assert result["entities"][EntityType.ACTIVITY_TYPE] == expected_type
    
    @pytest.mark.asyncio
    async def test_city_extraction(self, nlu_service):
        """Test city entity extraction."""
        test_cases = [
            ("show activities in Mumbai", "Mumbai"),
            ("list events at Delhi", "Delhi"),
            ("Bangalore activities", "Bangalore")
        ]
        
        for text, expected_city in test_cases:
            result = await nlu_service.analyze_intent(text)
            assert EntityType.CITY in result["entities"]
            assert result["entities"][EntityType.CITY] == expected_city
    
    @pytest.mark.asyncio
    async def test_status_extraction(self, nlu_service):
        """Test status entity extraction."""
        test_cases = [
            ("show submitted assignments", "submitted"),
            ("list graded quizzes", "graded"),
            ("show draft projects", "draft"),
            ("pending approvals", "pending")
        ]
        
        for text, expected_status in test_cases:
            result = await nlu_service.analyze_intent(text)
            assert EntityType.STATUS in result["entities"]
            assert result["entities"][EntityType.STATUS] == expected_status
    
    @pytest.mark.asyncio
    async def test_date_extraction(self, nlu_service):
        """Test date entity extraction."""
        result = await nlu_service.analyze_intent("show activities for today")
        
        assert EntityType.DATE in result["entities"]
        date_info = result["entities"][EntityType.DATE]
        assert "value" in date_info
        assert date_info["description"] == "today"
    
    @pytest.mark.asyncio
    async def test_date_range_extraction(self, nlu_service):
        """Test date range entity extraction."""
        result = await nlu_service.analyze_intent("show activities this week")
        
        assert EntityType.DATE_RANGE in result["entities"]
        range_info = result["entities"][EntityType.DATE_RANGE]
        assert "start" in range_info
        assert "end" in range_info
        assert range_info["description"] == "this week"
    
    @pytest.mark.asyncio
    async def test_subject_extraction(self, nlu_service):
        """Test subject entity extraction."""
        test_cases = [
            ("submit math assignment", "Math"),
            ("show science projects", "Science"),
            ("english homework", "English")
        ]
        
        for text, expected_subject in test_cases:
            result = await nlu_service.analyze_intent(text)
            assert EntityType.SUBJECT in result["entities"]
            assert result["entities"][EntityType.SUBJECT] == expected_subject
    
    @pytest.mark.asyncio
    async def test_points_extraction(self, nlu_service):
        """Test points entity extraction."""
        result = await nlu_service.analyze_intent("redeem 500 points")
        
        assert EntityType.POINTS in result["entities"]
        assert result["entities"][EntityType.POINTS] == 500
    
    @pytest.mark.asyncio
    async def test_action_extraction(self, nlu_service):
        """Test action entity extraction."""
        test_cases = [
            ("create new activity", "create"),
            ("submit assignment", "submit"),
            ("show all activities", "list"),
            ("redeem points", "redeem")
        ]
        
        for text, expected_action in test_cases:
            result = await nlu_service.analyze_intent(text)
            assert result["action"] == expected_action
    
    @pytest.mark.asyncio
    async def test_sentiment_analysis_positive(self, nlu_service):
        """Test positive sentiment detection."""
        result = await nlu_service.analyze_intent("Great! Show me my rewards")
        
        assert result["sentiment"] == Sentiment.POSITIVE
    
    @pytest.mark.asyncio
    async def test_sentiment_analysis_negative(self, nlu_service):
        """Test negative sentiment detection."""
        result = await nlu_service.analyze_intent("This is confusing, I need help")
        
        assert result["sentiment"] == Sentiment.NEGATIVE
    
    @pytest.mark.asyncio
    async def test_sentiment_analysis_neutral(self, nlu_service):
        """Test neutral sentiment detection."""
        result = await nlu_service.analyze_intent("show activities")
        
        assert result["sentiment"] == Sentiment.NEUTRAL
    
    @pytest.mark.asyncio
    async def test_multi_entity_extraction(self, nlu_service):
        """Test extraction of multiple entities."""
        result = await nlu_service.analyze_intent(
            "Show me quizzes in Mumbai due this week"
        )
        
        assert EntityType.ACTIVITY_TYPE in result["entities"]
        assert EntityType.CITY in result["entities"]
        assert EntityType.DATE_RANGE in result["entities"]
        
        assert result["entities"][EntityType.ACTIVITY_TYPE] == "quiz"
        assert result["entities"][EntityType.CITY] == "Mumbai"
    
    @pytest.mark.asyncio
    async def test_context_aware_detection(self, nlu_service):
        """Test context-aware intent detection."""
        context = {
            "last_intent": "activity_list",
            "user_role": "student"
        }
        
        result = await nlu_service.analyze_intent("in Mumbai", context=context)
        
        assert result["context_aware"] is True
        assert EntityType.CITY in result["entities"]
    
    @pytest.mark.asyncio
    async def test_confidence_scoring(self, nlu_service):
        """Test confidence scoring."""
        # High confidence query
        result1 = await nlu_service.analyze_intent("show me all activities")
        assert result1["confidence"] >= 0.85
        
        # Lower confidence query (vague)
        result2 = await nlu_service.analyze_intent("what can I do")
        assert 0.5 <= result2["confidence"] <= 1.0
    
    @pytest.mark.asyncio
    async def test_empty_input(self, nlu_service):
        """Test handling of empty input."""
        result = await nlu_service.analyze_intent("")
        
        assert result["primary_intent"] == "general"
        assert result["confidence"] == 0.0
        assert result["entities"] == {}
    
    @pytest.mark.asyncio
    async def test_activity_id_extraction(self, nlu_service):
        """Test activity ID extraction."""
        result = await nlu_service.analyze_intent(
            "show submissions for activity_id: ACT-12345"
        )
        
        assert EntityType.ACTIVITY_ID in result["entities"]
        assert result["entities"][EntityType.ACTIVITY_ID] == "ACT-12345"
    
    @pytest.mark.asyncio
    async def test_user_id_extraction(self, nlu_service):
        """Test user ID extraction."""
        result = await nlu_service.analyze_intent(
            "show submissions for user_id: USER-789"
        )
        
        assert EntityType.USER_ID in result["entities"]
        assert result["entities"][EntityType.USER_ID] == "USER-789"
    
    @pytest.mark.asyncio
    async def test_complex_query(self, nlu_service):
        """Test complex query with multiple entities and intents."""
        result = await nlu_service.analyze_intent(
            "Show me all submitted assignments for math class in Bangalore due this week"
        )
        
        # Check intent
        assert result["primary_intent"] in ["submission_list", "activity_list"]
        
        # Check entities
        assert EntityType.ACTIVITY_TYPE in result["entities"]
        assert EntityType.STATUS in result["entities"]
        assert EntityType.SUBJECT in result["entities"]
        assert EntityType.CITY in result["entities"]
        assert EntityType.DATE_RANGE in result["entities"]
        
        # Verify values
        assert result["entities"][EntityType.ACTIVITY_TYPE] == "assignment"
        assert result["entities"][EntityType.STATUS] == "submitted"
        assert result["entities"][EntityType.SUBJECT] == "Math"
        assert result["entities"][EntityType.CITY] == "Bangalore"
    
    @pytest.mark.asyncio
    async def test_signup_intent(self, nlu_service):
        """Test signup intent detection."""
        test_cases = [
            "I want to sign up",
            "create new account",
            "register me",
            "join the platform"
        ]
        
        for text in test_cases:
            result = await nlu_service.analyze_intent(text)
            assert result["primary_intent"] == "signup"
    
    @pytest.mark.asyncio
    async def test_login_intent(self, nlu_service):
        """Test login intent detection."""
        test_cases = [
            "I want to log in",
            "sign in please",
            "authenticate me"
        ]
        
        for text in test_cases:
            result = await nlu_service.analyze_intent(text)
            assert result["primary_intent"] == "login"
    
    @pytest.mark.asyncio
    async def test_rewards_intent(self, nlu_service):
        """Test rewards intent detection."""
        # Get rewards
        result1 = await nlu_service.analyze_intent("check my rewards")
        assert result1["primary_intent"] == "rewards_get"
        
        # Redeem rewards
        result2 = await nlu_service.analyze_intent("redeem 100 points")
        assert result2["primary_intent"] == "rewards_redeem"
        assert EntityType.POINTS in result2["entities"]
    
    @pytest.mark.asyncio
    async def test_approval_intent(self, nlu_service):
        """Test approval intent detection."""
        result = await nlu_service.analyze_intent("show pending approvals")
        
        assert result["primary_intent"] == "approval_pending"
        assert EntityType.STATUS in result["entities"]
        assert result["entities"][EntityType.STATUS] == "pending"


class TestIntentDetector:
    """Test cases for Intent Detector."""
    
    @pytest.fixture
    def intent_detector(self):
        """Create intent detector instance."""
        from app.services.intent_detector import IntentDetector
        return IntentDetector()
    
    def test_backward_compatibility(self, intent_detector):
        """Test backward compatible detect method."""
        intent = intent_detector.detect("show me all activities")
        assert intent == "activity_list"
    
    @pytest.mark.asyncio
    async def test_advanced_detection(self, intent_detector):
        """Test advanced detection method."""
        result = await intent_detector.detect_advanced(
            "show quizzes in Mumbai"
        )
        
        assert "intent" in result
        assert "entities" in result
        assert "confidence" in result
        assert "sentiment" in result
    
    @pytest.mark.asyncio
    async def test_extract_filters(self, intent_detector):
        """Test filter extraction from entities."""
        result = await intent_detector.detect_advanced(
            "show submitted assignments in Mumbai"
        )
        
        filters = intent_detector.extract_filters_from_entities(result["entities"])
        
        assert "activity_type" in filters
        assert "status" in filters
        assert "city" in filters
        assert filters["activity_type"] == "assignment"
        assert filters["status"] == "submitted"
        assert filters["city"] == "Mumbai"
    
    def test_get_mcp_service(self, intent_detector):
        """Test MCP service mapping."""
        assert intent_detector.get_mcp_service("activity_list") == "activities"
        assert intent_detector.get_mcp_service("signup") == "onboarding"
        assert intent_detector.get_mcp_service("rewards_get") == "rewards"
        assert intent_detector.get_mcp_service("approval_pending") == "approval"
    
    @pytest.mark.asyncio
    async def test_date_filter_extraction(self, intent_detector):
        """Test date filter extraction."""
        result = await intent_detector.detect_advanced(
            "show activities this week"
        )
        
        filters = intent_detector.extract_filters_from_entities(result["entities"])
        
        assert "start_date" in filters
        assert "end_date" in filters


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
