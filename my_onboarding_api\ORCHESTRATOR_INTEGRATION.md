# Orchestrator Integration Guide

## Overview

The `my_onboarding_api` backend has been integrated with the standalone **MVP Conversation Orchestrator** service. This integration delegates conversation routing and intent detection to an external microservice, following the modular MCP architecture.

## What Changed

### 1. New Service Client

**File:** `app/services/orchestrator_client.py`

A new client service that communicates with the external orchestrator:

```python
from app.services.orchestrator_client import send_to_orchestrator

response = await send_to_orchestrator(
    user_id="user-123",
    text="I want to sign up",
    session_id="session-abc"
)
```

**Features:**
- Async HTTP client using `httpx`
- Comprehensive logging at every step
- Error handling for connection failures, timeouts, and HTTP errors
- Health check function to verify orchestrator availability

### 2. New API Endpoint

**Endpoint:** `POST /api/v1/chat-orchestrated`

**Headers:**
- `session_id`: Required session identifier

**Request Body:**
```json
{
  "message": "I want to sign up"
}
```

**Response:**
Depends on the detected intent and routing:

**For Signup/Login (Onboarding Intent):**
```json
{
  "message": "Great! Let's get you signed up. What's your name?",
  "flow_step": "name",
  "flow_type": "signup"
}
```

**For Activity/General Intent:**
```json
{
  "success": true,
  "input": "Show me activities",
  "detected_intent": "activity",
  "gemini_response": "Here are some activities available...",
  "message": "Response from activity_mcp: Here are some activities available..."
}
```

### 3. Configuration Updates

**File:** `app/core/config.py`

Added orchestrator configuration:

```python
orchestrator_url: str = "http://localhost:8100/chat"
use_orchestrator: bool = True
```

**Environment Variables:**

Add to your `.env` file:

```env
ORCHESTRATOR_URL=http://localhost:8100/chat
USE_ORCHESTRATOR=true
```

### 4. Updated Routes

**File:** `app/api/routes.py`

- Imported orchestrator client
- Added new `/chat-orchestrated` endpoint
- Existing endpoints remain unchanged for backward compatibility

## Architecture Flow

### Before (Internal Routing)

```
Frontend → /gemini-chat-with-intent
              ↓
         Intent Service (HuggingFace)
              ↓
         Internal Routing Logic
              ↓
         Gemini Service / Session Service
```

### After (External Orchestrator)

```
Frontend → /chat-orchestrated
              ↓
         Orchestrator Client
              ↓
         MVP Conversation Orchestrator (Port 8100)
              ↓
         [OnboardingMCP | ActivityMCP | ChatMCP]
              ↓
         Backend receives orchestrated response
              ↓
         Initiates local flows (signup/login) if needed
```

## How It Works

### Step 1: Message Received

Frontend sends a message to `/chat-orchestrated`:

```bash
curl -X POST http://localhost:8000/api/v1/chat-orchestrated \
  -H "Content-Type: application/json" \
  -H "session_id: session-123" \
  -d '{"message": "I want to sign up"}'
```

### Step 2: Check for Active Flow

The endpoint first checks if there's an active signup/login flow:

```python
if session_service.is_flow_active(session_id):
    return _handle_active_flow(session_id, msg.message)
```

If a flow is active, it's handled locally (no orchestrator call).

### Step 3: Delegate to Orchestrator

If no active flow, delegate to the orchestrator:

```python
orchestrator_response = await send_to_orchestrator(
    user_id=session_id,
    text=msg.message,
    session_id=session_id
)
```

### Step 4: Process Orchestrator Response

The orchestrator returns:

```json
{
  "success": true,
  "message": "Great! Let's get you signed up...",
  "intent": "onboarding",
  "routed_to": "onboarding_mcp",
  "data": {
    "flow_type": "signup",
    "current_step": "email",
    "session_id": "session-123"
  }
}
```

### Step 5: Initiate Local Flow (if needed)

For onboarding intents (signup/login), the backend initiates the local flow:

```python
if detected_intent == "onboarding" and flow_type == 'signup':
    session_service.start_flow(session_id, FlowType.SIGNUP)
    return SessionFlowResponse(...)
```

### Step 6: Return Response

The response is returned to the frontend in the appropriate format.

## Logging

Comprehensive logging shows the delegation flow:

```
[ChatOrchestrated] Processing message via orchestrator for session: session-123
[ChatOrchestrated] Message: I want to sign up...
[ChatOrchestrated] No active flow, delegating to orchestrator
[OrchestratorClient] Delegating message to orchestrator for user: session-123
[OrchestratorClient] Sending POST request to http://localhost:8100/chat
[OrchestratorClient] Received response with status: 200
[OrchestratorClient] Orchestrator routed to: onboarding_mcp
[OrchestratorClient] Detected intent: onboarding
[ChatOrchestrated] ✓ Orchestrator delegated to onboarding_mcp MCP
[ChatOrchestrated] ✓ Detected intent: onboarding
[ChatOrchestrated] Starting signup flow for session: session-123
```

## Testing

### 1. Start the Orchestrator

```bash
cd mvp-conversation-orchestrator
python -m app.main
```

Verify it's running:
```bash
curl http://localhost:8100/health
```

### 2. Start the Backend

```bash
cd my_onboarding_api
python main.py
```

### 3. Test the Integration

**Test Signup Intent:**
```bash
curl -X POST http://localhost:8000/api/v1/chat-orchestrated \
  -H "Content-Type: application/json" \
  -H "session_id: test-session-1" \
  -d '{"message": "I want to sign up"}'
```

**Test Login Intent:**
```bash
curl -X POST http://localhost:8000/api/v1/chat-orchestrated \
  -H "Content-Type: application/json" \
  -H "session_id: test-session-2" \
  -d '{"message": "I need to log in"}'
```

**Test Activity Intent:**
```bash
curl -X POST http://localhost:8000/api/v1/chat-orchestrated \
  -H "Content-Type: application/json" \
  -H "session_id: test-session-3" \
  -d '{"message": "Show me activities"}'
```

## Error Handling

### Orchestrator Unavailable

If the orchestrator is not running:

```json
{
  "detail": {
    "error": "Orchestrator unavailable",
    "message": "External orchestrator service is not available. Please ensure it's running at http://localhost:8100/chat"
  }
}
```

**Status Code:** 503 Service Unavailable

### Orchestrator Disabled

If `USE_ORCHESTRATOR=false`:

```json
{
  "detail": {
    "error": "Orchestrator disabled",
    "message": "External orchestrator is disabled. Use /gemini-chat-with-intent instead."
  }
}
```

**Status Code:** 503 Service Unavailable

### Timeout

If the orchestrator takes too long to respond:

```json
{
  "detail": {
    "error": "Orchestrator error",
    "message": "Orchestrator service timeout. Please try again."
  }
}
```

**Status Code:** 500 Internal Server Error

## Backward Compatibility

The existing `/gemini-chat-with-intent` endpoint remains unchanged and fully functional. This allows:

1. **Gradual Migration:** Frontend can migrate to the new endpoint incrementally
2. **Fallback:** If orchestrator is unavailable, frontend can fall back to the old endpoint
3. **A/B Testing:** Compare performance between internal and external routing

## Configuration Options

### Enable/Disable Orchestrator

```env
# Enable orchestrator (default)
USE_ORCHESTRATOR=true

# Disable orchestrator (use internal routing)
USE_ORCHESTRATOR=false
```

### Change Orchestrator URL

```env
# Local development
ORCHESTRATOR_URL=http://localhost:8100/chat

# Production
ORCHESTRATOR_URL=https://orchestrator.myvillage.com/chat

# Docker Compose
ORCHESTRATOR_URL=http://orchestrator:8100/chat
```

## Frontend Integration

Update your frontend to use the new endpoint:

### Before

```typescript
const response = await fetch('/api/v1/gemini-chat-with-intent', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'session_id': sessionId
  },
  body: JSON.stringify({ message: userMessage })
});
```

### After

```typescript
const response = await fetch('/api/v1/chat-orchestrated', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'session_id': sessionId
  },
  body: JSON.stringify({ message: userMessage })
});
```

The response format remains the same for signup/login flows!

## Benefits

### 1. Separation of Concerns
- Intent detection logic moved to dedicated service
- Backend focuses on flow management and authentication
- Easier to maintain and test

### 2. Scalability
- Orchestrator can be scaled independently
- Multiple backend instances can share one orchestrator
- Orchestrator can be deployed closer to users (CDN/edge)

### 3. Flexibility
- Easy to swap intent detection models
- Can route to different MCPs without backend changes
- A/B test different routing strategies

### 4. Observability
- Centralized logging for all routing decisions
- Clear separation between routing and business logic
- Easier to debug conversation flows

## Troubleshooting

### Issue: "Orchestrator unavailable"

**Solution:**
1. Check if orchestrator is running: `curl http://localhost:8100/health`
2. Verify `ORCHESTRATOR_URL` in `.env` matches the running service
3. Check firewall/network settings

### Issue: "Orchestrator disabled"

**Solution:**
1. Set `USE_ORCHESTRATOR=true` in `.env`
2. Restart the backend service

### Issue: Timeout errors

**Solution:**
1. Check orchestrator logs for slow responses
2. Increase timeout in `orchestrator_client.py` (default: 30s)
3. Verify network latency between services

### Issue: Wrong intent detected

**Solution:**
1. Check orchestrator logs to see which keywords matched
2. Update intent keywords in `mvp-conversation-orchestrator/app/services/intent_client.py`
3. Restart orchestrator service

## Next Steps

### Phase 1: Testing
- [ ] Test all conversation flows with orchestrator
- [ ] Verify error handling and fallbacks
- [ ] Load test the integration

### Phase 2: Frontend Migration
- [ ] Update frontend to use `/chat-orchestrated`
- [ ] Add error handling for orchestrator unavailability
- [ ] Implement fallback to `/gemini-chat-with-intent`

### Phase 3: Production Deployment
- [ ] Deploy orchestrator to production
- [ ] Update `ORCHESTRATOR_URL` for production
- [ ] Monitor logs and metrics
- [ ] Gradually shift traffic to new endpoint

### Phase 4: Deprecation
- [ ] Monitor usage of old endpoint
- [ ] Deprecate `/gemini-chat-with-intent` when ready
- [ ] Remove internal intent detection code

## Related Documentation

- `MVP_MODULARIZATION_PLAN.md` - Overall architecture plan
- `mvp-conversation-orchestrator/README.md` - Orchestrator service documentation
- `mvp-conversation-orchestrator/SETUP_COMPLETE.md` - Orchestrator setup guide

---

**Status:** ✅ Integration Complete
**Version:** 1.0.0
**Date:** November 13, 2025
