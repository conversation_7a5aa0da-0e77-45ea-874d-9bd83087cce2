# Chat AI API Demo Script (4 min)

## Introduction (30s)
- "Hello everyone! Today I'll be demonstrating our Chat AI API."
- "This API powers natural conversations with advanced language understanding."
- "Let me show you what it can do!"

## 1. Basic Chat (45s)
```python
# Simple chat example
response = chat_api.send_message(
    message="Hello! How can I help you today?",
    user_id="demo_user_123"
)
print(response.text)
```
- "This is the basic chat flow - just send a message and get a response."
- "The API maintains conversation context automatically."

## 2. Context Management (45s)
```python
# Multi-turn conversation
conversation = [
    {"role": "user", "content": "What's the weather like?"},
    {"role": "assistant", "content": "I don't have real-time weather data."}
]

response = chat_api.send_message(
    message="What should I wear?",
    conversation_history=conversation
)
```
- "The API maintains conversation context for natural follow-ups."
- "Just pass the conversation history with each request."

## 3. Customization (60s)
```python
# Customize AI personality
response = chat_api.send_message(
    message="Tell me about our company",
    system_prompt="You are a helpful assistant for MyVillage AI."
                     "Be friendly and professional.",
    temperature=0.7  # Controls randomness
)
```
- "Customize the AI's personality and behavior."
- "Control response creativity with temperature."
- "Add domain-specific knowledge through system prompts."

## 4. Error Handling (30s)
```python
try:
    response = chat_api.send_message(
        message=message,
        timeout=10  # seconds
    )
except APIError as e:
    print(f"Error: {e.status_code} - {e.message}")
```
- "Robust error handling for production use."
- "Set timeouts and handle rate limits gracefully."

## 5. Real-world Example (30s)
```python
# Customer support example
response = chat_api.send_message(
    message="My order #12345 hasn't arrived",
    conversation_history=conversation,
    metadata={
        "user_tier": "premium",
        "language": "en-US"
    }
)
```
- "Perfect for customer support, e-commerce, and more."
- "Add metadata for personalized responses."

## Conclusion (30s)
- "In just 4 minutes, we've seen:"
  1. Basic chat functionality
  2. Context management
  3. Customization options
  4. Error handling
  5. Real-world applications
- "The API is ready to integrate into your applications!"
- "Questions?"

## Tips for Demo:
- Keep code examples short and focused
- Show actual API responses when possible
- Have 1-2 common use cases prepared
- Leave 30s for Q&A at the end
