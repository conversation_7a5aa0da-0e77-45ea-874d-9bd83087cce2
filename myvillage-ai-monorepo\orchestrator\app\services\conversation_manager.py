"""Conversation manager for handling multi-step flows."""
from typing import Dict, Optional, Any
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


class ConversationState:
    """Represents the state of a conversation."""
    
    def __init__(self, user_id: str, flow: str):
        self.user_id = user_id
        self.flow = flow
        self.step = 0
        self.data = {}
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
    
    def update(self, data: Dict[str, Any]):
        """Update conversation data."""
        self.data.update(data)
        self.updated_at = datetime.now()
    
    def next_step(self):
        """Move to next step."""
        self.step += 1
        self.updated_at = datetime.now()
    
    def is_expired(self, timeout_minutes: int = 30) -> bool:
        """Check if conversation has expired."""
        return datetime.now() - self.updated_at > timedelta(minutes=timeout_minutes)


class ConversationManager:
    """Manages conversation states for multi-step flows."""
    
    def __init__(self):
        self.conversations: Dict[str, ConversationState] = {}
        
        # Define conversation flows
        self.flows = {
            "signup": {
                "steps": [
                    {
                        "field": "name",
                        "prompt": "Great! Let's get you signed up. What's your full name?",
                        "validation": lambda x: len(x.strip()) >= 2
                    },
                    {
                        "field": "email",
                        "prompt": "Thanks {name}! What's your email address?",
                        "validation": lambda x: "@" in x and "." in x
                    },
                    {
                        "field": "password",
                        "prompt": "Perfect! Now create a password (minimum 8 characters):",
                        "validation": lambda x: len(x) >= 8
                    },
                    {
                        "field": "phone",
                        "prompt": "Almost done! What's your phone number? (optional, press skip to continue)",
                        "validation": lambda x: True,  # Optional field
                        "optional": True
                    }
                ]
            },
            "login": {
                "steps": [
                    {
                        "field": "email",
                        "prompt": "Welcome back! What's your email address?",
                        "validation": lambda x: "@" in x and "." in x
                    },
                    {
                        "field": "password",
                        "prompt": "Great! Now enter your password:",
                        "validation": lambda x: len(x) > 0
                    }
                ]
            },
            "create_activity": {
                "steps": [
                    {
                        "field": "title",
                        "prompt": "Let's create an activity! What's the title?",
                        "validation": lambda x: len(x.strip()) >= 3
                    },
                    {
                        "field": "description",
                        "prompt": "Great title! Now provide a description:",
                        "validation": lambda x: len(x.strip()) >= 10
                    },
                    {
                        "field": "activity_type",
                        "prompt": "What type of activity is this? (assignment, quiz, project, discussion)",
                        "validation": lambda x: x.lower() in ["assignment", "quiz", "project", "discussion"]
                    }
                ]
            },
            "list_activities": {
                "steps": [
                    {
                        "field": "city_name",
                        "prompt": "Which city would you like to see activities for?\n\n💡 You can select from the available cities or type a city name:",
                        "validation": lambda x: len(x.strip()) >= 2,
                        "needs_city_list": True  # Flag for frontend to show city selector
                    }
                ]
            },
            "list_submissions": {
                "steps": [
                    {
                        "field": "filter_type",
                        "prompt": "Would you like to filter submissions by:\n1. Activity (see all submissions for a specific activity)\n2. User (see all submissions by a specific user)\n3. Status (see submissions by status: draft, submitted, graded, returned)\n4. All (see all submissions)\n5. City (see submissions for activities in a specific city)\n\nPlease enter the number or name:",
                        "validation": lambda x: x.strip().lower() in ["1", "2", "3", "4", "5", "activity", "user", "status", "all", "city"]
                    }
                ]
            },
            "filter_submissions_by_status": {
                "steps": [
                    {
                        "field": "status",
                        "prompt": "Please enter the status (draft, submitted, graded, or returned):",
                        "validation": lambda x: x.strip().lower() in ["draft", "submitted", "graded", "returned", "approved", "rejected", "pending"]
                    }
                ]
            },
            "filter_submissions_by_activity": {
                "steps": [
                    {
                        "field": "activity_id",
                        "prompt": "Please enter the Activity ID you want to see submissions for:",
                        "validation": lambda x: len(x.strip()) > 0
                    }
                ]
            },
            "filter_submissions_by_user": {
                "steps": [
                    {
                        "field": "user_id",
                        "prompt": "Please enter the User ID you want to see submissions for:",
                        "validation": lambda x: len(x.strip()) > 0
                    }
                ]
            },
            "filter_submissions_by_city": {
                "steps": [
                    {
                        "field": "city_name",
                        "prompt": "Which city would you like to see submissions for?\n\n💡 You can select from the available cities or type a city name:",
                        "validation": lambda x: len(x.strip()) >= 2,
                        "needs_city_list": True  # Flag for frontend to show city selector
                    }
                ]
            },
            "filter_my_submissions_by_city": {
                "steps": [
                    {
                        "field": "city_name",
                        "prompt": "Which city would you like to see your submissions for?\n\n💡 You can select from the available cities or type a city name:",
                        "validation": lambda x: len(x.strip()) >= 2,
                        "needs_city_list": True  # Flag for frontend to show city selector
                    }
                ]
            }
        }
    
    def start_flow(self, user_id: str, flow: str) -> Dict[str, Any]:
        """Start a new conversation flow."""
        if flow not in self.flows:
            return {
                "success": False,
                "message": f"Unknown flow: {flow}"
            }
        
        # Clean up expired conversations
        self._cleanup_expired()
        
        # Create new conversation state
        state = ConversationState(user_id, flow)
        self.conversations[user_id] = state
        
        # Get first step
        first_step = self.flows[flow]["steps"][0]
        
        logger.info(f"Started {flow} flow for user {user_id}")
        
        return {
            "success": True,
            "message": first_step["prompt"],
            "flow": flow,
            "step": 0,
            "field": first_step["field"],
            "in_progress": True,
            "needs_city_list": first_step.get("needs_city_list", False)
        }
    
    def process_input(self, user_id: str, user_input: str) -> Dict[str, Any]:
        """Process user input in an active conversation."""
        # Check if user has active conversation
        if user_id not in self.conversations:
            return {
                "success": False,
                "message": "No active conversation. How can I help you?",
                "in_progress": False
            }
        
        state = self.conversations[user_id]
        
        # Check if conversation expired
        if state.is_expired():
            del self.conversations[user_id]
            return {
                "success": False,
                "message": "Your session expired. Let's start over. What would you like to do?",
                "in_progress": False
            }
        
        flow = self.flows[state.flow]
        current_step = flow["steps"][state.step]
        
        # Handle skip for optional fields
        if user_input.lower() == "skip" and current_step.get("optional", False):
            state.data[current_step["field"]] = None
            state.next_step()
        else:
            # Validate input
            if not current_step["validation"](user_input):
                return {
                    "success": False,
                    "message": f"Invalid {current_step['field']}. {current_step['prompt']}",
                    "flow": state.flow,
                    "step": state.step,
                    "field": current_step["field"],
                    "in_progress": True
                }
            
            # Store the input
            state.data[current_step["field"]] = user_input
            state.next_step()
        
        # Check if flow is complete
        if state.step >= len(flow["steps"]):
            # Flow complete
            completed_data = state.data.copy()
            del self.conversations[user_id]
            
            logger.info(f"Completed {state.flow} flow for user {user_id}")
            
            return {
                "success": True,
                "message": "Perfect! Processing your request...",
                "flow": state.flow,
                "completed": True,
                "data": completed_data,
                "in_progress": False
            }
        
        # Get next step
        next_step = flow["steps"][state.step]
        next_prompt = next_step["prompt"]
        
        # Replace placeholders with collected data
        for key, value in state.data.items():
            next_prompt = next_prompt.replace(f"{{{key}}}", str(value))
        
        return {
            "success": True,
            "message": next_prompt,
            "flow": state.flow,
            "step": state.step,
            "field": next_step["field"],
            "in_progress": True,
            "needs_city_list": next_step.get("needs_city_list", False)
        }
    
    def cancel_flow(self, user_id: str) -> Dict[str, Any]:
        """Cancel an active conversation flow."""
        if user_id in self.conversations:
            flow = self.conversations[user_id].flow
            del self.conversations[user_id]
            logger.info(f"Cancelled {flow} flow for user {user_id}")
            return {
                "success": True,
                "message": "Conversation cancelled. How else can I help you?",
                "in_progress": False
            }
        return {
            "success": False,
            "message": "No active conversation to cancel.",
            "in_progress": False
        }
    
    def has_active_conversation(self, user_id: str) -> bool:
        """Check if user has an active conversation."""
        if user_id in self.conversations:
            state = self.conversations[user_id]
            if state.is_expired():
                del self.conversations[user_id]
                return False
            return True
        return False
    
    def get_conversation_state(self, user_id: str) -> Optional[ConversationState]:
        """Get current conversation state for a user."""
        return self.conversations.get(user_id)
    
    def _cleanup_expired(self):
        """Remove expired conversations."""
        expired_users = [
            user_id for user_id, state in self.conversations.items()
            if state.is_expired()
        ]
        for user_id in expired_users:
            logger.info(f"Cleaning up expired conversation for user {user_id}")
            del self.conversations[user_id]


# Global instance
conversation_manager = ConversationManager()
