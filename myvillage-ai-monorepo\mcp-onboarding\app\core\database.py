"""Database configuration and utilities."""
import boto3
from typing import Optional
import logging
from .config import settings

logger = logging.getLogger(__name__)


class Database:
    """Database connection manager."""
    
    def __init__(self):
        """Initialize database connection."""
        self._resource = None
        self._client = None
    
    @property
    def resource(self):
        """Get DynamoDB resource."""
        if self._resource is None:
            self._resource = boto3.resource(
                'dynamodb',
                region_name=settings.aws_region
            )
        return self._resource
    
    @property
    def client(self):
        """Get DynamoDB client."""
        if self._client is None:
            self._client = boto3.client(
                'dynamodb',
                region_name=settings.aws_region
            )
        return self._client
    
    def get_table(self, table_name: str):
        """
        Get a DynamoDB table.
        
        Args:
            table_name: Name of the table
            
        Returns:
            DynamoDB table resource
        """
        return self.resource.Table(table_name)
    
    def create_table(
        self,
        table_name: str,
        key_schema: list,
        attribute_definitions: list,
        **kwargs
    ):
        """
        Create a DynamoDB table.
        
        Args:
            table_name: Name of the table
            key_schema: Key schema definition
            attribute_definitions: Attribute definitions
            **kwargs: Additional table parameters
        """
        try:
            table = self.resource.create_table(
                TableName=table_name,
                KeySchema=key_schema,
                AttributeDefinitions=attribute_definitions,
                **kwargs
            )
            table.wait_until_exists()
            logger.info(f"Table {table_name} created successfully")
            return table
        except Exception as e:
            logger.error(f"Error creating table {table_name}: {e}")
            raise


# Global database instance
db = Database()


def get_db() -> Database:
    """
    Get database instance.
    
    Returns:
        Database instance
    """
    return db
