'use client'

import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import ActivityList from "./ActivityList"
import SubmissionList from "./SubmissionList"
import CitySelector from "./CitySelector"
import { But<PERSON> } from "@/components/ui/button"
import { useState } from "react"

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  intent?: string
  mcp_service?: string
  data?: any
}

interface ChatMessageProps {
  message: Message
  onResubmit?: (content: string) => void
  onReply?: (content: string) => void
}

export default function ChatMessage({ message, onResubmit, onReply }: ChatMessageProps) {
  const isUser = message.role === 'user'
  const [isCopied, setIsCopied] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [hasSelectedCity, setHasSelectedCity] = useState(false)
  const [editContent, setEditContent] = useState(message.content)

  // Check if we have activities to display
  // The backend might return 'activities' or 'data' containing the list
  const activities = Array.isArray(message.data) ? message.data :
    (message.data?.activities && Array.isArray(message.data.activities)) ? message.data.activities :
      (message.data?.Items && Array.isArray(message.data.Items)) ? message.data.Items : null

  // Check if we have submissions to display
  // Backend returns: { submissions: [...], count: X }
  const submissions = (message.data?.submissions && Array.isArray(message.data.submissions)) ? message.data.submissions :
    (message.intent === 'submission_list' && Array.isArray(message.data)) ? message.data :
      (message.intent === 'submission_list' && message.data?.data?.submissions) ? message.data.data.submissions : null

  const handleCopy = () => {
    navigator.clipboard.writeText(message.content)
    setIsCopied(true)
    setTimeout(() => setIsCopied(false), 2000)
  }

  const handleEditStart = () => {
    setEditContent(message.content)
    setIsEditing(true)
  }

  const handleEditCancel = () => {
    setIsEditing(false)
    setEditContent(message.content)
  }

  const handleEditSubmit = () => {
    if (editContent.trim() && onResubmit) {
      onResubmit(editContent)
      setIsEditing(false)
    }
  }

  return (
    <div className={cn(
      "flex gap-3 message-enter group", // Added group for hover effects
      isUser ? "justify-end" : "justify-start"
    )}>
      {!isUser && (
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground">
          <Bot className="w-5 h-5" />
        </div>
      )}

      <div className={cn(
        "flex flex-col max-w-[85%]",
        isUser ? "items-end" : "items-start",
        isEditing ? "w-full max-w-2xl" : "" // Expand width when editing
      )}>
        {isEditing ? (
          <div className="w-full bg-muted/50 rounded-xl p-3 border border-primary/20">
            <textarea
              value={editContent}
              onChange={(e) => setEditContent(e.target.value)}
              className="w-full bg-transparent border-none resize-none focus:outline-none text-sm min-h-[60px]"
              autoFocus
            />
            <div className="flex justify-end gap-2 mt-2">
              <Button
                size="sm"
                variant="ghost"
                onClick={handleEditCancel}
                className="h-8 px-3 text-xs"
              >
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={handleEditSubmit}
                disabled={!editContent.trim()}
                className="h-8 px-3 text-xs"
              >
                Send
              </Button>
            </div>
          </div>
        ) : (
          <div
            className={cn(
              "rounded-2xl px-4 py-3 shadow-sm relative",
              isUser
                ? "bg-primary text-primary-foreground"
                : "bg-card border text-card-foreground"
            )}
          >
            <p className="whitespace-pre-wrap break-words text-sm leading-relaxed">
              {message.content}
            </p>

            {/* Render Activities if present */}
            {!isUser && activities && activities.length > 0 && (
              <ActivityList activities={activities} />
            )}

            {/* Render Submissions if present */}
            {!isUser && submissions && submissions.length > 0 && (
              <SubmissionList submissions={submissions} />
            )}



            {/* Render City Selector if needed */}
            {(() => {
              const shouldShowCitySelector = !isUser &&
                message.data?.needs_city_list &&
                (onReply || onResubmit) &&
                !hasSelectedCity

              if (!shouldShowCitySelector) return null

              return (
                <CitySelector onSelectCity={(cityName) => {
                  setHasSelectedCity(true)
                  if (onReply) {
                    onReply(cityName)
                  } else if (onResubmit) {
                    onResubmit(cityName)
                  }
                }} />
              )
            })()}
          </div>
        )}

        {!isEditing && (
          <div className="flex items-center gap-2 mt-1.5 px-2 min-h-[24px]">
            <span className="text-xs text-muted-foreground">
              {message.timestamp.toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </span>

            {message.intent && (
              <Badge variant="outline" className="text-xs">
                {message.intent}
              </Badge>
            )}

            {message.mcp_service && (
              <Badge variant="secondary" className="text-xs">
                {message.mcp_service}
              </Badge>
            )}

            {isUser && (
              <div className="flex items-center gap-1 ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 text-muted-foreground hover:text-foreground"
                  onClick={handleCopy}
                  title="Copy message"
                >
                  {isCopied ? <Check className="w-3 h-3" /> : <Copy className="w-3 h-3" />}
                </Button>
                {onResubmit && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 text-muted-foreground hover:text-foreground"
                    onClick={handleEditStart}
                    title="Edit message"
                  >
                    <Pencil className="w-3 h-3" />
                  </Button>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {isUser && (
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-secondary flex items-center justify-center text-secondary-foreground">
          <User className="w-5 h-5" />
        </div>
      )}
    </div>
  )
}
