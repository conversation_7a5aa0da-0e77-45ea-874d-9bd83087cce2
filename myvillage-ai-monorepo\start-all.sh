#!/bin/bash

# Start all MyVillage AI services with detailed logging and error handling

set -e  # Exit on error
trap cleanup ERR  # Call cleanup on error

# Cleanup function to stop all services on error
cleanup() {
    echo ""
    echo "========================================"
    echo "❌ ERROR DETECTED - Cleaning up..."
    echo "========================================"
    echo ""
    echo "🧹 Stopping all services..."
    ./stop-all.sh
    echo ""
    echo "❌ Startup failed! Check the logs above for details."
    exit 1
}

echo "========================================"
echo "Starting MyVillage AI Services"
echo "========================================"
echo ""

# Get the absolute path of the monorepo root
MONOREPO_ROOT="$(cd "$(dirname "$0")" && pwd)"
echo "📂 Monorepo root: $MONOREPO_ROOT"
echo ""

# Check Python installation
echo "🔍 Checking Python installation..."
if command -v python &> /dev/null; then
    PYTHON_CMD=python
    PYTHON_VERSION=$(python --version 2>&1)
    echo "✅ Found Python: $PYTHON_VERSION"
    echo "   📍 Location: $(which python)"
elif command -v python3 &> /dev/null; then
    PYTHON_CMD=python3
    PYTHON_VERSION=$(python3 --version 2>&1)
    echo "✅ Found Python3: $PYTHON_VERSION"
    echo "   📍 Location: $(which python3)"
else
    echo "❌ ERROR: Python not found! Please install Python 3.11+"
    exit 1
fi
echo ""

# Check if directories exist
echo "🔍 Checking service directories..."
REQUIRED_DIRS=("mcp-onboarding" "mcp-activities" "mcp-rewards" "mcp-chat" "orchestrator" "common")
for dir in "${REQUIRED_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "✅ Found: $dir/"
        # Check if it has app/main.py
        if [ "$dir" != "common" ] && [ -f "$dir/app/main.py" ]; then
            echo "   ├─ app/main.py ✓"
        fi
    else
        echo "❌ Missing: $dir/"
        echo "   ERROR: Required directory not found!"
        exit 1
    fi
done
echo ""

# Check for requirements.txt and dependencies
echo "🔍 Checking Python dependencies..."
MISSING_DEPS=false
for dir in mcp-onboarding mcp-activities mcp-rewards mcp-chat orchestrator; do
    if [ -f "$dir/requirements.txt" ]; then
        echo "✅ Found: $dir/requirements.txt"
    else
        echo "⚠️  Missing: $dir/requirements.txt"
        MISSING_DEPS=true
    fi
done
if [ "$MISSING_DEPS" = true ]; then
    echo "   💡 Tip: You may need to install dependencies"
fi
echo ""

# Load environment variables if .env exists
if [ -f .env ]; then
    echo "📄 Loading environment variables from .env"
    export $(cat .env | grep -v '^#' | xargs)
    echo "✅ Environment variables loaded"
    echo "   📊 Variables count: $(cat .env | grep -v '^#' | grep -v '^$' | wc -l)"
else
    echo "⚠️  WARNING: .env file not found (optional)"
    echo "   💡 Create .env from .env.example if needed"
fi
echo ""

# Add monorepo root to PYTHONPATH so 'common' module can be found
export PYTHONPATH="$MONOREPO_ROOT:$PYTHONPATH"
echo "🐍 Setting up Python environment..."
echo "   PYTHONPATH: $PYTHONPATH"
echo "   ✅ Common module will be accessible"
echo ""

# Create logs directory
mkdir -p logs
echo "📁 Log directory setup..."
echo "   📂 Created: logs/"
echo "   🗑️  Clearing old logs..."
rm -f logs/*.log 2>/dev/null || true
echo "   ✅ Ready for new logs"
echo ""

echo "🔍 Checking for already running services..."
echo ""

# Check if ports are already in use
check_port() {
    local port=$1
    if netstat -ano | grep ":$port " | grep "LISTENING" > /dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

PORTS_IN_USE=false
PORTS=("8001:Onboarding MCP" "8002:Activities MCP" "8003:Rewards MCP" "8005:Chat MCP" "8100:Orchestrator")
for port_info in "${PORTS[@]}"; do
    port="${port_info%%:*}"
    service="${port_info##*:}"
    if check_port $port; then
        echo "⚠️  Port $port is already in use ($service)"
        PORTS_IN_USE=true
    else
        echo "✅ Port $port is available ($service)"
    fi
done

if [ "$PORTS_IN_USE" = true ]; then
    echo ""
    echo "❌ ERROR: Some ports are already in use!"
    echo "   Please run './stop-all.sh' first to stop existing services."
    echo ""
    exit 1
fi

echo ""
echo "✅ All ports are available"
echo ""

echo "========================================"
echo "🚀 Starting Services"
echo "========================================"
echo ""

# Track started services
STARTED_SERVICES=()

# Function to start a service
start_service() {
    local service_name=$1
    local service_dir=$2
    local port=$3
    local step=$4
    
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "[$step/4] Starting $service_name"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # Check if directory exists
    if [ ! -d "$service_dir" ]; then
        echo "❌ ERROR: Directory $service_dir not found!"
        return 1
    fi
    
    # Check if app/main.py exists
    if [ ! -f "$service_dir/app/main.py" ]; then
        echo "❌ ERROR: $service_dir/app/main.py not found!"
        return 1
    fi
    
    echo "📂 Service directory: $service_dir"
    echo "🌐 Port: $port"
    echo "🐍 Command: $PYTHON_CMD -m ${service_dir/\//.}.app.main"
    echo ""
    echo "⏳ Starting process..."
    
    # Start the service from monorepo root with proper module path
    cd "$MONOREPO_ROOT"
    $PYTHON_CMD -m "${service_dir/\//.}.app.main" > "logs/${service_name}.log" 2>&1 &
    local pid=$!
    
    echo "   🆔 Process ID: $pid"
    echo "   📝 Log file: logs/${service_name}.log"
    echo ""
    echo "⏳ Waiting for service to initialize (3 seconds)..."
    sleep 3
    
    # Check if process is still running
    if ps -p $pid > /dev/null 2>&1; then
        echo "✅ $service_name started successfully!"
        STARTED_SERVICES+=("$service_name:$pid:$port")
        
        # Show first few lines of log
        echo ""
        echo "📄 Initial log output:"
        head -n 5 "logs/${service_name}.log" | sed 's/^/   │ /'
        echo "   └─ (see logs/${service_name}.log for full output)"
    else
        echo "❌ $service_name failed to start!"
        echo ""
        echo "📄 Error log (last 15 lines):"
        tail -n 15 "logs/${service_name}.log" | sed 's/^/   │ /'
        echo ""
        return 1
    fi
    
    echo ""
    sleep 1
}

# Start all services
start_service "onboarding-mcp" "mcp-onboarding" "8001" "1" || cleanup
start_service "activities-mcp" "mcp-activities" "8002" "2" || cleanup
start_service "rewards-mcp" "mcp-rewards" "8003" "3" || cleanup
API_PORT=8005 start_service "chat-mcp" "mcp-chat" "8005" "4" || cleanup
start_service "orchestrator" "orchestrator" "8100" "5" || cleanup

echo "========================================"
echo "✅ All Services Started Successfully!"
echo "========================================"
echo ""
echo "📊 Running Services Summary:"
for service_info in "${STARTED_SERVICES[@]}"; do
    IFS=':' read -r name pid port <<< "$service_info"
    echo "  ✓ $name"
    echo "    ├─ PID: $pid"
    echo "    ├─ Port: $port"
    echo "    └─ URL: http://localhost:$port"
done
echo ""
echo "📝 Log files:"
echo "  - logs/orchestrator.log"
echo "  - logs/onboarding-mcp.log"
echo "  - logs/activities-mcp.log"
echo "  - logs/rewards-mcp.log"
echo "  - logs/chat-mcp.log"
echo ""
echo "⏳ Waiting for services to fully initialize (10 seconds)..."
sleep 10

# Test health checks with detailed output
echo ""
echo "========================================"
echo "🏥 Health Check Tests"
echo "========================================"
echo ""

HEALTH_CHECK_FAILED=false

test_health() {
    local service_name=$1
    local url=$2
    
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "Testing: $service_name"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "🌐 URL: $url"
    echo "⏳ Sending request..."
    
    response=$(curl -s -w "\n%{http_code}" "$url" 2>&1)
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n-1)
    
    if [ "$http_code" = "200" ]; then
        echo "✅ Status: $http_code OK"
        echo "📄 Response:"
        echo "$body" | python -m json.tool 2>/dev/null | sed 's/^/   │ /' || echo "$body" | sed 's/^/   │ /'
    else
        echo "❌ Status: $http_code FAILED"
        echo "📄 Response: $body"
        echo "💡 Check logs/${service_name}.log for details"
        HEALTH_CHECK_FAILED=true
    fi
    echo ""
}

test_health "orchestrator" "http://localhost:8100/health"
test_health "onboarding-mcp" "http://localhost:8001/health"
test_health "activities-mcp" "http://localhost:8002/health"
test_health "rewards-mcp" "http://localhost:8003/health"
test_health "chat-mcp" "http://localhost:8005/health"

if [ "$HEALTH_CHECK_FAILED" = true ]; then
    echo "⚠️  WARNING: Some health checks failed!"
    echo "   Services are running but may not be fully operational."
    echo "   Check the logs for more details."
    echo ""
fi

echo "========================================"
echo "🎉 Startup Complete!"
echo "========================================"
echo ""
echo "📊 Quick Commands:"
echo ""
echo "  📈 View logs in real-time:"
echo "     tail -f logs/orchestrator.log"
echo "     tail -f logs/onboarding-mcp.log"
echo "     tail -f logs/activities-mcp.log"
echo "     tail -f logs/rewards-mcp.log"
echo "     tail -f logs/chat-mcp.log"
echo ""
echo "  🛑 Stop all services:"
echo "     ./stop-all.sh"
echo ""
echo "  🧪 Test the chat endpoint:"
echo "     curl -X POST http://localhost:8100/chat \\"
echo "       -H 'Content-Type: application/json' \\"
echo "       -d '{\"user_id\":\"user-123\",\"text\":\"Show me activities\"}'"
echo ""
echo "  🔍 Check service status:"
echo "     curl http://localhost:8100/health"
echo ""
echo "========================================"
echo "✅ MyVillage AI is ready!"
echo "========================================"
echo ""
