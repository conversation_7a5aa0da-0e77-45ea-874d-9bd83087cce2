'use client';

import { createContext, useContext, ReactNode, useState, useEffect } from 'react';

type Activity = {
  id: string;
  title: string;
  type: string;
  date?: string;
  location?: string;
  organizer?: string;
  description?: string;
  status?: string;
};

type User = {
  id: string;
  email: string;
  name: string;
  mvpPoints: number;
  isStakeholder: boolean;
  role?: 'user' | 'manager' | 'admin';
  activities?: Activity[];
  impactScore?: number;
  activitiesCompleted?: number;
  pendingActivities?: number;
  weeklyProgress?: number; // Percentage for this week
  weeklyActivities?: { completed: number; total: number }; // Activities this week
  monthlyGoal?: number; // Percentage of monthly goal
  monthlyActivities?: { completed: number; total: number }; // Monthly activities
};

type AuthContextType = {
  user: User | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
  isLoggingIn: boolean;
  setAuthenticatedUser: (userData: Partial<User>) => void;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true); // Start with true to prevent flash of content
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const isAuthenticated = !!user;

  // Check for existing session on initial load
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check if user is stored in localStorage
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          const parsedUser = JSON.parse(storedUser);
          setUser(parsedUser);
        } else {
          // If no stored user, check with the server
          const response = await fetch('/api/auth/me');
          if (response.ok) {
            const userData = await response.json();
            if (userData) {
              setUser(userData);
              localStorage.setItem('user', JSON.stringify(userData));
            }
          }
        }
      } catch (error) {
        console.error('Error checking auth status:', error);
        localStorage.removeItem('user');
      }
    };

    checkAuth();
  }, []);

  // Listen for storage events to sync auth state across components
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'user') {
        if (e.newValue) {
          try {
            const userData = JSON.parse(e.newValue);
            setUser(userData);
          } catch (error) {
            console.error('Error parsing user data from storage:', error);
          }
        } else {
          setUser(null);
        }
      }
    };

    const handleCustomStorageEvent = () => {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        try {
          const userData = JSON.parse(storedUser);
          setUser(userData);
        } catch (error) {
          console.error('Error parsing user data from storage:', error);
        }
      } else {
        setUser(null);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('storage', handleCustomStorageEvent);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('storage', handleCustomStorageEvent);
    };
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    setIsLoggingIn(true);
    setIsLoading(true);
    try {
      // Make API call to authenticate
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
        credentials: 'include', // Important for cookies
      });
      
      if (!response.ok) {
        throw new Error('Login failed');
      }
      
      const data = await response.json();
      
      if (data.success) {
        // Extract user data from the response
        const userData = data.data?.user || {};
        const user = {
          id: userData.id || 'user-123',
          email: userData.email || email,
          name: userData.name || email.split('@')[0],
          mvpPoints: userData.mvpPoints || userData.mvp_points || 0,
          isStakeholder: userData.isStakeholder || userData.is_stakeholder || false,
          role: userData.role || userData.user_role || 'user', // Default to 'user' if role is not provided
          activities: userData.activities || userData.user_activities || [],
          impactScore: userData.impactScore || userData.impact_score || 87,
          activitiesCompleted: userData.activitiesCompleted || userData.activities_completed || 15,
          pendingActivities: userData.pendingActivities || userData.pending_activities || 3,
          weeklyProgress: userData.weeklyProgress || userData.weekly_progress || 15,
          weeklyActivities: userData.weeklyActivities || userData.weekly_activities || { completed: 5, total: 7 },
          monthlyGoal: userData.monthlyGoal || userData.monthly_goal || 75,
          monthlyActivities: userData.monthlyActivities || userData.monthly_activities || { completed: 12, total: 16 }
        };
        
        // Update state and localStorage
        setUser(user);
        localStorage.setItem('user', JSON.stringify(user));
        
        // Force a re-render of the app
        window.dispatchEvent(new Event('storage'));
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Login failed:', error);
      return false;
    } finally {
      setIsLoading(false);
      setIsLoggingIn(false);
    }
  };

  const logout = async () => {
    try {
      // Make API call to log out
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      // Clear user from state and localStorage
      setUser(null);
      localStorage.removeItem('user');
      
      // Force a re-render of the app
      window.dispatchEvent(new Event('storage'));
    }
  };

  const setAuthenticatedUser = (userData: Partial<User>) => {
    const user = {
      id: userData.id || 'user-123',
      email: userData.email || '<EMAIL>',
      name: userData.name || 'User',
      mvpPoints: userData.mvpPoints || 250,
      isStakeholder: userData.isStakeholder || false,
      role: userData.role || 'user',
      activities: userData.activities || [],
      impactScore: userData.impactScore || 87,
      activitiesCompleted: userData.activitiesCompleted || 15,
      pendingActivities: userData.pendingActivities || 3,
      weeklyProgress: userData.weeklyProgress || 15,
      weeklyActivities: userData.weeklyActivities || { completed: 5, total: 7 },
      monthlyGoal: userData.monthlyGoal || 75,
      monthlyActivities: userData.monthlyActivities || { completed: 12, total: 16 }
    };
    
    // Update state and localStorage
    setUser(user);
    localStorage.setItem('user', JSON.stringify(user));
    
    // Force a re-render of the app
    window.dispatchEvent(new Event('storage'));
  };

  return (
    <AuthContext.Provider value={{ 
      user, 
      isAuthenticated, 
      login, 
      logout, 
      isLoading,
      isLoggingIn,
      setAuthenticatedUser
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
