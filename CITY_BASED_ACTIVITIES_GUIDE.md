# City-Based Activities - Complete Guide

## 🎯 Overview

Activities are now filtered by city. Users provide a city name, and the system automatically converts it to a city ID for database lookup.

## 🚀 How It Works

### User Flow
1. User asks: "Show me activities" or "List activities"
2. <PERSON><PERSON> asks: "Which city would you like to see activities for?"
3. User enters: "New York" (or any city name)
4. <PERSON> converts city name → city ID
5. <PERSON><PERSON> shows activities for that city (sorted by most recent)

### For MCP Clients (ChatGPT, Claude, etc.)
MCP tools can directly pass `city_id` parameter:

```bash
# Direct API call with city_id
GET /tools/list_activities?city_id=abc-123&limit=10
```

## 📋 Implementation Details

### 1. Files Modified

**Backend:**
- ✅ `orchestrator/app/services/city_service.py` - NEW: City lookup service
- ✅ `orchestrator/app/services/conversation_manager.py` - Added list_activities flow
- ✅ `orchestrator/app/routers/chat_router.py` - City name → ID conversion
- ✅ `orchestrator/app/services/activities_client.py` - Added city_id parameter
- ✅ `mcp-activities/app/models/activity.py` - Added city_id field
- ✅ `mcp-activities/app/services/activity_service.py` - City filtering + sorting
- ✅ `mcp-activities/app/tools/list_activities.py` - Added city_id parameter

**Frontend:**
- No changes needed! The UI automatically displays activities returned by the API

### 2. Database Schema

**Activities Table:**
```json
{
  "id": "uuid",
  "title": "string",
  "description": "string",
  "activity_type": "assignment|event|survey|discussion|volunteer",
  "status": "draft|published|archived|completed",
  "city_id": "string",  // ← NEW FIELD
  "created_by": "string",
  "created_at": "timestamp",
  "updated_at": "timestamp",
  "due_date": "timestamp",
  "points": "integer",
  "tags": "array",
  "submission_count": "integer"
}
```

**Cities Table:**
```json
{
  "id": "uuid",
  "name": "string",
  "isDeleted": "boolean"
}
```

### 3. API Endpoints

#### List Activities (with city filter)
```bash
GET /tools/list_activities?city_id=abc-123&limit=10

# Optional parameters:
# - activity_type: assignment|event|survey|discussion|volunteer
# - status: draft|published|archived|completed
# - city_id: filter by city
# - limit: max results (1-100, default: 10)

# Response:
{
  "success": true,
  "message": "Found 5 activities",
  "data": {
    "activities": [
      {
        "id": "act-123",
        "title": "Community Cleanup",
        "description": "Help clean the park",
        "activity_type": "volunteer",
        "status": "published",
        "city_id": "abc-123",
        "created_at": "2025-11-21T10:00:00",
        "updated_at": "2025-11-21T15:30:00",
        "points": 50
      }
    ],
    "count": 5
  }
}
```

## 🧪 Testing

### Test 1: Basic Flow
```bash
# Start orchestrator
cd myvillage-ai-monorepo/orchestrator
python -m app.main

# Start activities MCP
cd myvillage-ai-monorepo/mcp-activities
python -m app.main

# Test via UI or API
curl -X POST http://localhost:8100/chat \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "test-user",
    "text": "Show me activities"
  }'

# Expected: Bot asks for city name

curl -X POST http://localhost:8100/chat \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "test-user",
    "text": "New York"
  }'

# Expected: Bot shows activities in New York
```

### Test 2: Invalid City
```bash
curl -X POST http://localhost:8100/chat \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "test-user",
    "text": "Show me activities"
  }'

curl -X POST http://localhost:8100/chat \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "test-user",
    "text": "InvalidCityName"
  }'

# Expected: Error message about city not found
```

### Test 3: Direct MCP Call
```bash
# Get city ID first (if you know it)
curl "http://localhost:8002/tools/list_activities?city_id=your-city-id&limit=5"
```

## 🔑 Key Features

### ✅ Conversational Flow
- Natural language interaction
- User-friendly prompts
- Error handling for invalid cities

### ✅ MCP-Compatible
- Works with ChatGPT, Claude, and other MCP clients
- Optional `city_id` parameter
- Stateless tool design

### ✅ Sorted by Recent Updates
- Activities sorted by `updated_at` DESC
- Most recently updated activities appear first

### ✅ Case-Insensitive City Search
- "new york", "New York", "NEW YORK" all work
- Filters out deleted cities automatically

## 🎨 UI Integration

The UI automatically handles the new city-based flow:

1. **ChatInterface.tsx** - Displays conversation flow
2. **ActivityList.tsx** - Shows activities (no changes needed)
3. **ChatMessage.tsx** - Renders bot responses

## 🔐 Environment Variables

Add to your `.env` files:

```env
# Orchestrator & MCP Services
DYNAMODB_CITIES_TABLE=cities
DYNAMODB_ACTIVITIES_TABLE=activities
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-key
AWS_SECRET_ACCESS_KEY=your-secret
```

## 📊 Benefits

### For Users
- ✅ Simple: Just type the city name
- ✅ Fast: Instant city lookup
- ✅ Accurate: Case-insensitive matching
- ✅ Helpful: Clear error messages

### For Developers
- ✅ MCP-compliant: Works with any MCP client
- ✅ Reusable: City service can be used elsewhere
- ✅ Maintainable: Clean separation of concerns
- ✅ Scalable: DynamoDB handles large datasets

### For Admins
- ✅ Flexible: Can filter by city, type, status
- ✅ Organized: Activities grouped by city
- ✅ Trackable: Sorted by recent updates

## 🚨 Error Handling

### City Not Found
```
❌ Sorry, I couldn't find a city named 'XYZ'. 
   Please check the spelling and try again.
```

### No Activities
```
📍 No activities found in [City Name] at the moment.
```

### Service Error
```
❌ Sorry, there was an error fetching activities. 
   Please try again.
```

## 🔄 Future Enhancements

1. **Auto-complete**: Suggest cities as user types
2. **Nearby Cities**: Show activities from nearby cities
3. **User Preferences**: Remember user's preferred city
4. **Multi-City**: Allow viewing activities from multiple cities
5. **City Stats**: Show activity count per city

## 📞 Support

If you encounter issues:
1. Check DynamoDB tables exist and have data
2. Verify AWS credentials are configured
3. Ensure all services are running
4. Check logs for detailed error messages

---

**Implementation Complete! ✅**

Activities are now filtered by city with a conversational user experience.
