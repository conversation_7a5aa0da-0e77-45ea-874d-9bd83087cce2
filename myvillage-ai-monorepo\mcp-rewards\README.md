# Rewards MCP Service

Rewards tracking and redemption microservice.

## 🎯 Purpose

This service handles:
- Reward points tracking
- Points calculation for activities
- Points redemption
- Transaction history

## 🔧 Tools

### 1. get_rewards
Get user's reward balance and history.

**Parameters:**
- `user_id` (string, required): User ID

**Example:**
```bash
curl -X POST http://localhost:8003/tools/get_rewards \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user-123"}'
```

### 2. calculate_points
Award points for activity completion.

**Parameters:**
- `activity_id` (string, required): Activity ID
- `user_id` (string, required): User ID
- `points` (integer, optional): Points to award (default: 50)

**Example:**
```bash
curl -X POST http://localhost:8003/tools/calculate_points \
  -H "Content-Type: application/json" \
  -d '{
    "activity_id": "activity-123",
    "user_id": "user-456",
    "points": 100
  }'
```

### 3. redeem_reward
Redeem points for a reward.

**Parameters:**
- `user_id` (string, required): User ID
- `reward_id` (string, required): Reward ID
- `points` (integer, required): Points to redeem
- `description` (string, optional): Redemption description

**Example:**
```bash
curl -X POST http://localhost:8003/tools/redeem_reward \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user-123",
    "reward_id": "reward-789",
    "points": 50,
    "description": "Redeemed gift card"
  }'
```

## 🚀 Running the Service

### Local Development

```bash
pip install -r requirements.txt
python -m app.main
```

### Docker

```bash
docker build -t mcp-rewards .
docker run -p 8003:8003 mcp-rewards
```

## 📡 Endpoints

- `GET /health` - Health check
- `GET /manifest` - MCP manifest
- `POST /tools/get_rewards` - Get reward balance
- `POST /tools/calculate_points` - Award points
- `POST /tools/redeem_reward` - Redeem points

## 🗄️ Database

Uses DynamoDB with the following tables:

**Table: rewards**
- Primary Key: `user_id`
- Attributes: id, total_points, available_points, redeemed_points, created_at, updated_at

**Table: reward_transactions**
- Primary Key: `id`
- Attributes: user_id, activity_id, reward_id, points, transaction_type, description, created_at

## 🎁 Transaction Types

- `earn` - Points earned from activities
- `redeem` - Points redeemed for rewards

## 🧪 Testing

```bash
# Health check
curl http://localhost:8003/health

# Get manifest
curl http://localhost:8003/manifest
```

## 📦 Port

8003
