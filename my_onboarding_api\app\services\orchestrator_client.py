"""
Orchestrator client for delegating conversation routing to external orchestrator service.

This client replaces internal intent detection and routing logic with calls to the
standalone MVP Conversation Orchestrator service.
"""

import httpx
from typing import Dict, Any, Optional
from ..core.logging import get_logger
from ..core.config import settings

logger = get_logger(__name__)

# Orchestrator URL - configurable via environment variable
ORCHESTRATOR_URL = getattr(settings, 'orchestrator_url', "http://localhost:8100/chat")


async def send_to_orchestrator(
    user_id: str, 
    text: str, 
    session_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Send message to the orchestrator service for intent detection and routing.
    
    Args:
        user_id: Unique user identifier
        text: User message text
        session_id: Optional session ID for context
        
    Returns:
        Dict containing orchestrator response with:
            - success: bool
            - message: str (response message)
            - intent: str (detected intent)
            - routed_to: str (which MCP handled this)
            - data: dict (additional response data)
            
    Raises:
        httpx.HTTPError: If orchestrator service is unavailable
        Exception: For other unexpected errors
    """
    logger.info(f"[OrchestratorClient] Delegating message to orchestrator for user: {user_id}")
    logger.debug(f"[OrchestratorClient] Message: '{text[:50]}...'")
    logger.debug(f"[OrchestratorClient] Session ID: {session_id}")
    logger.debug(f"[OrchestratorClient] Orchestrator URL: {ORCHESTRATOR_URL}")
    
    payload = {
        "user_id": user_id,
        "text": text
    }
    
    if session_id:
        payload["session_id"] = session_id
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            logger.info(f"[OrchestratorClient] Sending POST request to {ORCHESTRATOR_URL}")
            
            response = await client.post(
                ORCHESTRATOR_URL,
                json=payload
            )
            
            logger.info(f"[OrchestratorClient] Received response with status: {response.status_code}")
            
            # Raise exception for 4xx/5xx status codes
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"[OrchestratorClient] Orchestrator routed to: {result.get('routed_to')}")
            logger.info(f"[OrchestratorClient] Detected intent: {result.get('intent')}")
            logger.debug(f"[OrchestratorClient] Full response: {result}")
            
            return result
            
    except httpx.HTTPStatusError as e:
        logger.error(f"[OrchestratorClient] HTTP error from orchestrator: {e.response.status_code}")
        logger.error(f"[OrchestratorClient] Response body: {e.response.text}")
        raise
        
    except httpx.ConnectError as e:
        logger.error(f"[OrchestratorClient] Failed to connect to orchestrator at {ORCHESTRATOR_URL}")
        logger.error(f"[OrchestratorClient] Error: {str(e)}")
        raise Exception(
            f"Orchestrator service unavailable at {ORCHESTRATOR_URL}. "
            "Please ensure the orchestrator is running."
        )
        
    except httpx.TimeoutException as e:
        logger.error(f"[OrchestratorClient] Timeout connecting to orchestrator")
        logger.error(f"[OrchestratorClient] Error: {str(e)}")
        raise Exception("Orchestrator service timeout. Please try again.")
        
    except Exception as e:
        logger.error(f"[OrchestratorClient] Unexpected error: {str(e)}", exc_info=True)
        raise


def is_orchestrator_available() -> bool:
    """
    Check if the orchestrator service is available.
    
    Returns:
        bool: True if orchestrator is reachable, False otherwise
    """
    try:
        import httpx
        health_url = ORCHESTRATOR_URL.replace('/chat', '/health')
        
        with httpx.Client(timeout=5.0) as client:
            response = client.get(health_url)
            return response.status_code == 200
            
    except Exception as e:
        logger.warning(f"[OrchestratorClient] Health check failed: {str(e)}")
        return False
