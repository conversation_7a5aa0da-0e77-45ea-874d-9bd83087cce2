"use client";

import { useState, useRef, useEffect } from "react";
import { Send, Loader2, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { useAuth } from "@/contexts/AuthContext";
import Sidebar from "@/components/layout/Sidebar";

interface Message {
  id: string;
  content: string;
  sender: "user" | "assistant";
  timestamp: Date;
  type?: "text" | "flow" | "error";
  flowData?: {
    flow_type?: string;
    flow_step?: string;
  };
}

export default function OnboardingChatPage() {
  const { isAuthenticated, user, logout, setAuthenticatedUser } = useAuth();

  // Dynamic welcome message based on auth status
  const getWelcomeMessage = () => {
    if (isAuthenticated && user) {
      return `Welcome back, ${
        user.name || "there"
      }! I can help you explore activities, participate in events, or answer any questions. What would you like to do?`;
    }
    return "Hello! I can help you sign up, log in, or explore activities. What would you like to do?";
  };

  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      content: getWelcomeMessage(),
      sender: "assistant",
      timestamp: new Date(),
      type: "text",
    },
  ]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId] = useState(() => `session-${Date.now()}`);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Debug effect to monitor auth state changes
  useEffect(() => {
    console.log("Auth state changed:", {
      isAuthenticated,
      user,
      userName: user?.name,
      userEmail: user?.email,
      userRole: user?.role,
    });
  }, [isAuthenticated, user]);

  const sendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      sender: "user",
      timestamp: new Date(),
      type: "text",
    };

    setMessages((prev) => [...prev, userMessage]);
    const messageText = inputValue.toLowerCase().trim();
    setInputValue("");
    setIsLoading(true);
    setError(null);

    // Check for logout commands
    const logoutCommands = ['logout', 'log out', 'sign out', 'signout', 'exit', 'quit'];
    const isLogoutCommand = logoutCommands.some(cmd => 
      messageText === cmd || 
      messageText.includes(`i want to ${cmd}`) ||
      messageText.includes(`i need to ${cmd}`) ||
      messageText.includes(`please ${cmd}`)
    );

    if (isLogoutCommand && isAuthenticated) {
      setIsLoading(false);
      logout();
      const logoutMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: "You have been logged out successfully. Feel free to sign up or log in again anytime!",
        sender: "assistant",
        timestamp: new Date(),
        type: "text",
      };
      setMessages((prev) => [...prev, logoutMessage]);
      return;
    }

    try {
      const apiUrl =
        process.env.NEXT_PUBLIC_ORCHESTRATOR_URL || "http://localhost:8100";
      const response = await fetch(`${apiUrl}/chat`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          user_id: sessionId,
          text: inputValue,
          session_id: sessionId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.detail?.message ||
            errorData.message ||
            `Server error: ${response.status}`
        );
      }

      const data = await response.json();
      console.log("Orchestrator response:", data);

      // Handle different response types
      let assistantMessage: Message;

      // Check if this is a flow response (signup/login)
      const isFlowResponse = data.data?.flow_type || data.flow_type;
      const flowType = data.data?.flow_type || data.flow_type;
      const flowStep = data.data?.current_step || data.flow_step;

      if (isFlowResponse) {
        // Flow response (signup/login) - from orchestrator or direct API
        assistantMessage = {
          id: (Date.now() + 1).toString(),
          content: data.message,
          sender: "assistant",
          timestamp: new Date(),
          type: "flow",
          flowData: {
            flow_type: flowType,
            flow_step: flowStep,
          },
        };
      } else if (data.gemini_response) {
        // General/activity response
        assistantMessage = {
          id: (Date.now() + 1).toString(),
          content: data.gemini_response,
          sender: "assistant",
          timestamp: new Date(),
          type: "text",
        };
      } else if (data.message) {
        // Activity or general response with data
        let messageContent = data.message;

        // If there are activities in the data, format them nicely
        if (data.data?.activities && Array.isArray(data.data.activities)) {
          const activities = data.data.activities;
          messageContent += "\n\n";
          activities.forEach((activity: any, index: number) => {
            messageContent += `${index + 1}. ${activity.title}\n`;
            messageContent += `   Type: ${activity.type}\n`;
            if (activity.date) messageContent += `   Date: ${activity.date}\n`;
            if (activity.location)
              messageContent += `   Location: ${activity.location}\n`;
            if (activity.organizer)
              messageContent += `   Organizer: ${activity.organizer}\n`;
            if (activity.description)
              messageContent += `   ${activity.description}\n`;
            messageContent += "\n";
          });
          messageContent += `Total: ${
            data.data.total || activities.length
          } activities found`;
        }

        assistantMessage = {
          id: (Date.now() + 1).toString(),
          content: messageContent,
          sender: "assistant",
          timestamp: new Date(),
          type: "text",
        };
      } else {
        // Unknown response format
        assistantMessage = {
          id: (Date.now() + 1).toString(),
          content:
            "I received your message but had trouble understanding the response format.",
          sender: "assistant",
          timestamp: new Date(),
          type: "error",
        };
      }

      setMessages((prev) => [...prev, assistantMessage]);

      // Check for successful login/signup
      const messageText = data.message?.toLowerCase() || "";
      const isLoginSuccess =
        messageText.includes("login successful") ||
        messageText.includes("welcome back") ||
        messageText.includes("logged in successfully") ||
        (data.status === "success" &&
          (messageText.includes("welcome") ||
            messageText.includes("authenticated")));

      const isSignupSuccess =
        messageText.includes("signup successful") ||
        messageText.includes("account created") ||
        messageText.includes("registration successful") ||
        (data.status === "success" && messageText.includes("signed up"));

      if (isLoginSuccess || isSignupSuccess) {
        console.log("Authentication success detected!");
        console.log("Full response data:", JSON.stringify(data, null, 2));

        // Extract user information from the response - check multiple possible locations
        const responseData = data.data || data;

        // Try to extract name from the welcome message if not in data
        let extractedName =
          responseData.name ||
          responseData.user_name ||
          responseData.full_name ||
          "";
        if (!extractedName && messageText) {
          // Try to extract from "Welcome back, mark!" or similar patterns
          const nameMatch =
            messageText.match(/welcome back,?\s+([^!,.\s]+)/i) ||
            messageText.match(/welcome,?\s+([^!,.\s]+)/i) ||
            messageText.match(/hi,?\s+([^!,.\s]+)/i);
          if (nameMatch && nameMatch[1]) {
            extractedName =
              nameMatch[1].charAt(0).toUpperCase() + nameMatch[1].slice(1);
          }
        }

        // Try to extract email from the user input (last email entered)
        let extractedEmail =
          responseData.email || responseData.user_email || "";
        if (!extractedEmail) {
          // Look through recent messages for an email
          const recentUserMessages = messages
            .filter((m) => m.sender === "user")
            .slice(-5);
          for (const msg of recentUserMessages.reverse()) {
            const emailMatch = msg.content.match(
              /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/
            );
            if (emailMatch) {
              extractedEmail = emailMatch[0];
              break;
            }
          }
        }

        // Extract activities from response
        const activities = responseData.activities || responseData.user_activities || [];

        // Extract role with multiple fallbacks
        const extractedRole = 
          responseData.role || 
          responseData.user_role || 
          responseData.assignedRole || 
          responseData.assigned_role ||
          "user";
        
        console.log("Role extraction debug:", {
          responseData_role: responseData.role,
          responseData_user_role: responseData.user_role,
          responseData_assignedRole: responseData.assignedRole,
          responseData_assigned_role: responseData.assigned_role,
          extractedRole: extractedRole,
          fullResponseData: responseData
        });

        const userData = {
          id: responseData.user_id || responseData.id || `user-${sessionId}`,
          email: extractedEmail,
          name: extractedName,
          mvpPoints: responseData.mvp_points || responseData.mvpPoints || 250,
          isStakeholder:
            responseData.is_stakeholder || responseData.isStakeholder || false,
          role: extractedRole,
          activities: activities,
          impactScore: responseData.impact_score || responseData.impactScore || 87,
          activitiesCompleted: responseData.activities_completed || responseData.activitiesCompleted || 15,
          pendingActivities: responseData.pending_activities || responseData.pendingActivities || 3,
          weeklyProgress: responseData.weekly_progress || responseData.weeklyProgress || 15,
          weeklyActivities: responseData.weekly_activities || responseData.weeklyActivities || { completed: 5, total: 7 },
          monthlyGoal: responseData.monthly_goal || responseData.monthlyGoal || 75,
          monthlyActivities: responseData.monthly_activities || responseData.monthlyActivities || { completed: 12, total: 16 },
        };

        // Update authentication state
        setAuthenticatedUser(userData);
        console.log(
          "Authentication state updated with:",
          JSON.stringify(userData, null, 2)
        );

        // Add a welcome message after successful authentication
        setTimeout(() => {
          const welcomeMessage: Message = {
            id: (Date.now() + 2).toString(),
            content: `🎉 Great! You're now logged in. You can now view activities, participate in events, and more. Check out the quick actions below!`,
            sender: "assistant",
            timestamp: new Date(),
            type: "text",
          };
          setMessages((prev) => [...prev, welcomeMessage]);
        }, 500);

        // Force a re-render by triggering a state update
        setTimeout(() => {
          window.dispatchEvent(new Event("storage"));
        }, 100);
      }

      // Refocus input after response
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    } catch (err) {
      console.error("Error sending message:", err);
      const errorMessage =
        err instanceof Error ? err.message : "Failed to send message";
      setError(errorMessage);

      const errorResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: `Error: ${errorMessage}. Please make sure the backend and orchestrator services are running.`,
        sender: "assistant",
        timestamp: new Date(),
        type: "error",
      };

      setMessages((prev) => [...prev, errorResponse]);

      // Refocus input even on error
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div className="flex h-screen bg-gradient-to-br from-background to-muted pt-16">
      {/* Sidebar */}
      <Sidebar />
      
      {/* Main Chat Area */}
      <div className="flex flex-col flex-1">
      {/* Header */}
      <div className="bg-card border-b border-border px-6 py-4 shadow-sm">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">
                My Village Chat
              </h1>

              <p className="text-xs text-muted-foreground mt-1">
                Session ID: {sessionId}
              </p>
            </div>
            {isAuthenticated && user && (
              <div className="text-right">
                <div className="flex items-center justify-end gap-2 mb-1">
                  <p className="text-sm font-medium text-foreground">
                    {user.name || "User"}
                  </p>
                  {user.role && (
                    <span className={`text-xs px-2.5 py-1 rounded-full font-medium ${
                      user.role === 'admin' ? 'bg-destructive-light text-destructive border border-destructive' :
                      user.role === 'manager' ? 'bg-warning-light text-warning border border-warning' :
                      'bg-primary-light text-primary border border-primary'
                    }`}>
                      {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                    </span>
                  )}
                </div>
                <p className="text-xs text-muted-foreground">
                  {user.email || ""}
                </p>
                <div className="flex items-center justify-end gap-2 mt-1">
                  <span className="text-xs text-muted-foreground">MVP:</span>
                  <span className="text-xs font-semibold text-warning">{user.mvpPoints || 0}</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Error Banner */}
      {error && (
        <div className="bg-destructive-light border-b border-destructive px-6 py-3">
          <div className="max-w-4xl mx-auto flex items-center gap-2 text-destructive">
            <AlertCircle className="w-4 h-4" />
            <span className="text-sm">{error}</span>
          </div>
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto px-6 py-6 pb-48">
        <div className="max-w-4xl mx-auto space-y-6">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${
                message.sender === "user" ? "justify-end" : "justify-start"
              }`}
            >
              <div
                className={`max-w-[80%] rounded-2xl px-4 py-3 ${
                  message.sender === "user"
                    ? "bg-primary text-primary-foreground"
                    : message.type === "error"
                    ? "bg-destructive-light text-destructive border border-destructive"
                    : message.type === "flow"
                    ? "bg-success-light text-success border border-success"
                    : "bg-card text-foreground shadow-sm border border-border"
                }`}
              >
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>

                {/* Flow metadata */}
                {message.flowData && (
                  <div className="mt-2 pt-2 border-t border-success">
                    <p className="text-xs opacity-75">
                      Flow: {message.flowData.flow_type} • Step:{" "}
                      {message.flowData.flow_step}
                    </p>
                  </div>
                )}

                <p
                  className={`text-xs mt-1 ${
                    message.sender === "user"
                      ? "text-primary-foreground opacity-75"
                      : "text-muted-foreground"
                  }`}
                >
                  {message.timestamp.toLocaleTimeString()}
                </p>
              </div>
            </div>
          ))}

          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-card rounded-2xl px-4 py-3 shadow-sm border border-border">
                <div className="flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    Thinking...
                  </span>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input - Fixed at bottom */}
      <div className="bg-card border-t border-border px-6 py-4 shadow-lg">
        <div className="max-w-4xl mx-auto">
          <div className="flex gap-3">
            <Input
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message... (e.g., 'I want to sign up')"
              disabled={isLoading}
              className="flex-1"
              autoFocus
            />
            <Button
              onClick={sendMessage}
              disabled={isLoading || !inputValue.trim()}
              className="px-6"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </div>

          {/* Quick actions - conditional based on auth status */}
          <div className="mt-3 flex flex-wrap gap-2">
            {!isAuthenticated ? (
              <>
                {/* Show login/signup for unauthenticated users */}
                <button
                  onClick={() => setInputValue("I want to sign up")}
                  disabled={isLoading}
                  className="text-xs px-3 py-1.5 rounded-full bg-muted text-foreground hover:bg-muted/80 transition-colors disabled:opacity-50"
                >
                  Sign up
                </button>
                <button
                  onClick={() => setInputValue("I need to log in")}
                  disabled={isLoading}
                  className="text-xs px-3 py-1.5 rounded-full bg-muted text-foreground hover:bg-muted/80 transition-colors disabled:opacity-50"
                >
                  Log in
                </button>
              </>
            ) : (
              <>
                {/* Show activities for authenticated users */}
                <button
                  onClick={() => setInputValue("Show me activities")}
                  disabled={isLoading}
                  className="text-xs px-3 py-1.5 rounded-full bg-primary-light text-primary hover:bg-primary/20 transition-colors disabled:opacity-50"
                >
                  View activities
                </button>
                <button
                  onClick={() =>
                    setInputValue(
                      "I want to participate in the weekend book drive activity"
                    )
                  }
                  disabled={isLoading}
                  className="text-xs px-3 py-1.5 rounded-full bg-muted text-foreground hover:bg-muted/80 transition-colors disabled:opacity-50"
                >
                  Book Drive
                </button>
                <button
                  onClick={() =>
                    setInputValue("Tell me about the tree plantation event")
                  }
                  disabled={isLoading}
                  className="text-xs px-3 py-1.5 rounded-full bg-muted text-foreground hover:bg-muted/80 transition-colors disabled:opacity-50"
                >
                  Tree Plantation
                </button>
                <button
                  onClick={() => setInputValue("I want to logout")}
                  disabled={isLoading}
                  className="text-xs px-3 py-1.5 rounded-full bg-destructive-light text-destructive hover:bg-destructive/20 transition-colors disabled:opacity-50"
                >
                  Logout
                </button>
              </>
            )}
            <button
              onClick={() => setInputValue("What can you help me with?")}
              disabled={isLoading}
              className="text-xs px-3 py-1.5 rounded-full bg-muted text-foreground hover:bg-muted/80 transition-colors disabled:opacity-50"
            >
              Help
            </button>
          </div>
        </div>
      </div>
      </div>
    </div>
  );
}
