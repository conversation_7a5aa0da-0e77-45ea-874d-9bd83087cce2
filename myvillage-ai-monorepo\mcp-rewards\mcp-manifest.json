{"name": "rewards-mcp", "version": "1.0.0", "description": "Rewards tracking and redemption", "author": "MyVillage AI Team", "tools": [{"name": "get_rewards", "description": "Get user's reward points balance and recent transaction history", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "Unique identifier of the user"}}, "required": ["user_id"]}}, {"name": "calculate_points", "description": "Calculate and award points for completing an activity", "parameters": {"type": "object", "properties": {"activity_id": {"type": "string", "description": "ID of the completed activity"}, "user_id": {"type": "string", "description": "ID of the user who completed the activity"}, "points": {"type": "integer", "description": "Number of points to award", "minimum": 0, "default": 50}}, "required": ["activity_id", "user_id"]}}, {"name": "redeem_reward", "description": "Redeem points for a reward", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "ID of the user redeeming points"}, "reward_id": {"type": "string", "description": "ID of the reward to redeem"}, "points": {"type": "integer", "description": "Number of points to redeem", "minimum": 1}, "description": {"type": "string", "description": "Description of the redemption", "default": "Reward redemption"}}, "required": ["user_id", "reward_id", "points"]}}]}