# Run all MyVillage services in separate windows

# Function to start a service in a new window
function Start-ServiceInNewWindow {
    param (
        [string]$serviceName,
        [string]$command,
        [string]$workingDir
    )
    
    Write-Host "Starting $serviceName..." -ForegroundColor Green
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$workingDir'; $command" -WindowStyle Normal
    Start-Sleep -Seconds 2  # Small delay to let the service start
}

# Get the project root directory
$projectRoot = Split-Path -Parent $MyInvocation.MyCommand.Path

# Start API Gateway
Start-ServiceInNewWindow -serviceName "API Gateway" -command "uvicorn app.main:app --port 8000 --reload" -workingDir "$projectRoot\myvillageai\api"

# Start Onboarding Service
Start-ServiceInNewWindow -serviceName "Onboarding Service" -command "uvicorn app.main:app --port 8001 --reload" -workingDir "$projectRoot\myvillageai\services\onboarding"

# Start Gemini Service
Start-ServiceInNewWindow -serviceName "Gemini Service" -command "uvicorn app.main:app --port 8002 --reload" -workingDir "$projectRoot\myvillageai\services\gemini"

# Start MCP Service
Start-ServiceInNewWindow -serviceName "MCP Service" -command "uvicorn app.main:app --port 8003 --reload" -workingDir "$projectRoot\myvillageai\mcp"

Write-Host "`nAll services have been started in separate windows.`n" -ForegroundColor Cyan
Write-Host "API Gateway:    http://localhost:8000" -ForegroundColor Yellow
Write-Host "Onboarding API: http://localhost:8001" -ForegroundColor Yellow
Write-Host "Gemini API:     http://localhost:8002" -ForegroundColor Yellow
Write-Host "MCP API:        http://localhost:8003" -ForegroundColor Yellow
Write-Host "`nPress any key to stop all services..." -ForegroundColor Cyan

# Wait for user input to stop
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Kill all Python processes (this will stop the services)
Write-Host "`nStopping all services..." -ForegroundColor Red
Stop-Process -Name python -ErrorAction SilentlyContinue
Stop-Process -Name uvicorn -ErrorAction SilentlyContinue

Write-Host "All services have been stopped." -ForegroundColor Green
