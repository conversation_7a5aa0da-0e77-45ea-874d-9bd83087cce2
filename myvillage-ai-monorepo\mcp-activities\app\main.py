"""Activities MCP Service - Activity management and submissions."""
from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
import sys
from pathlib import Path
import logging

# Add common to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "common"))

from common.models.base import HealthResponse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Activities MCP",
    version="1.0.0",
    description="MCP service for activity management and submissions"
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import routers after app creation
from .tools import (
    list_activities,
    list_cities,
    get_activity,
    create_activity,
    submit_assignment,
    grade_submission,
    list_submissions
)

# Include tool routers
app.include_router(list_activities.router, prefix="/tools", tags=["tools"])
app.include_router(list_cities.router, prefix="/tools", tags=["tools"])
app.include_router(get_activity.router, prefix="/tools", tags=["tools"])
app.include_router(create_activity.router, prefix="/tools", tags=["tools"])
app.include_router(submit_assignment.router, prefix="/tools", tags=["tools"])
app.include_router(grade_submission.router, prefix="/tools", tags=["tools"])
app.include_router(list_submissions.router, prefix="/tools", tags=["tools"])


@app.get("/", response_model=HealthResponse)
async def root():
    """Root endpoint."""
    return HealthResponse(
        status="healthy",
        service="Activities MCP",
        version="1.0.0"
    )


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        service="Activities MCP",
        version="1.0.0"
    )


@app.get("/manifest")
async def get_manifest():
    """Return MCP manifest for ChatGPT integration."""
    return {
        "name": "activities-mcp",
        "version": "1.0.0",
        "description": "Activity management and submission handling",
        "tools": [
            {
                "name": "list_activities",
                "description": "List available activities with optional filters",
                "parameters": {
                    "activity_type": {"type": "string", "required": False},
                    "status": {"type": "string", "required": False},
                    "limit": {"type": "integer", "required": False, "default": 10}
                }
            },
            {
                "name": "get_activity",
                "description": "Get detailed information about a specific activity",
                "parameters": {
                    "activity_id": {"type": "string", "required": True}
                }
            },
            {
                "name": "create_activity",
                "description": "Create a new activity",
                "parameters": {
                    "title": {"type": "string", "required": True},
                    "description": {"type": "string", "required": True},
                    "activity_type": {"type": "string", "required": True},
                    "created_by": {"type": "string", "required": True}
                }
            },
            {
                "name": "submit_assignment",
                "description": "Submit an assignment for an activity",
                "parameters": {
                    "activity_id": {"type": "string", "required": True},
                    "user_id": {"type": "string", "required": True},
                    "title": {"type": "string", "required": True},
                    "description": {"type": "string", "required": False}
                }
            },
            {
                "name": "grade_submission",
                "description": "Grade a submission",
                "parameters": {
                    "submission_id": {"type": "string", "required": True},
                    "grade": {"type": "number", "required": True},
                    "feedback": {"type": "string", "required": True},
                    "grader_id": {"type": "string", "required": True}
                }
            },
            {
                "name": "list_submissions",
                "description": "List submissions with optional filters",
                "parameters": {
                    "activity_id": {"type": "string", "required": False},
                    "user_id": {"type": "string", "required": False},
                    "status": {"type": "string", "required": False},
                    "limit": {"type": "integer", "required": False, "default": 10}
                }
            },
            {
                "name": "list_cities",
                "description": "List all available cities",
                "parameters": {
                    "limit": {"type": "integer", "required": False, "default": 100}
                }
            }
        ]
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
