"""Message schemas for request/response models."""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any


class ChatRequest(BaseModel):
    """Chat message request from frontend."""
    
    user_id: str = Field(..., description="Unique user identifier")
    text: str = Field(..., description="User message text")
    session_id: Optional[str] = Field(None, description="Optional session ID for context")
    
    class Config:
        json_schema_extra = {
            "example": {
                "user_id": "user-123",
                "text": "I want to sign up",
                "session_id": "session-abc"
            }
        }


class ChatResponse(BaseModel):
    """Unified chat response to frontend."""
    
    success: bool = Field(..., description="Whether the request was successful")
    message: str = Field(..., description="Response message to user")
    intent: str = Field(..., description="Detected intent (onboarding/activity/general)")
    routed_to: str = Field(..., description="Which MCP service handled this")
    data: Optional[Dict[str, Any]] = Field(None, description="Additional response data")
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "Great! Let's get you signed up.",
                "intent": "signup",
                "routed_to": "onboarding_mcp",
                "data": {"next_step": "email"}
            }
        }


class HealthResponse(BaseModel):
    """Health check response."""
    
    status: str
    service: str
    version: str
