#!/usr/bin/env python3
"""
Test script to verify deployment is working correctly.

This script tests both the API server and MCP server endpoints.
"""

import os
import sys
from typing import Dict, Any

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from app.main import create_app

@pytest.fixture
def client():
    """Create a test client for the FastAPI application."""
    app = create_app()
    return TestClient(app)

def test_api_health(client: TestClient) -> None:
    """Test API server health endpoint."""
    with patch('app.services.intent_service.intent_service') as mock_intent_service, \
         patch('app.services.gemini_service.gemini_service') as mock_gemini_service:
        
        # Mock the services
        mock_intent_service.is_ready.return_value = True
        mock_gemini_service.is_ready.return_value = True
        
        response = client.get("/health")
        assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
        
        data = response.json()
        print(f"✅ API Health Check: {data.get('status', 'unknown')}")
        
        # Check services status
        services = data.get('services', {})
        for service, status in services.items():
            if isinstance(status, dict):
                ready = status.get('ready', False)
                print(f"   - {service}: {'✅' if ready else '❌'} {status}")
            else:
                print(f"   - {service}: {'✅' if status else '❌'}")
        
        assert data.get('status') == 'healthy', "API health check failed"

def test_api_intent_classification(client: TestClient) -> None:
    """Test intent classification endpoint."""
    from app.models.responses import IntentClassificationResult
    from unittest.mock import MagicMock, patch

    test_message = "I want to sign up for an account"

    # Create the expected results in the format the pipeline returns
    mock_results = [
        {"label": "signup", "score": 0.95},
        {"label": "login", "score": 0.05}
    ]

    # Patch the intent_service instance directly
    with patch('app.api.routes.intent_service') as mock_intent_service:

        # Configure the mock intent service
        mock_intent_service.classify_intent.return_value = [
            IntentClassificationResult(label="signup", score=0.95),
            IntentClassificationResult(label="login", score=0.05)
        ]
        mock_intent_service.get_top_intent.return_value = "signup"

        # Make the request
        response = client.post(
            "/onboarding",
            json={"message": test_message}
        )

        # Verify the response
        assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
        
        result = response.json()
        print(f"✅ Intent Classification: {result}")
        
        # Check response structure
        assert 'success' in result, "Response missing 'success' field"
        assert 'input' in result, "Response missing 'input' field"
        assert 'intent' in result, "Response missing 'intent' field"
        assert 'message' in result, "Response missing 'message' field"
        
        # Check the values match our expectations
        assert result['success'] is True, f"Expected success=True, got {result['success']}"
        assert result['input'] == test_message, f"Expected input={test_message}, got {result['input']}"
        assert len(result['intent']) > 0, "Expected at least one intent result"
        
        # Check the first intent result
        first_intent = result['intent'][0]
        assert 'label' in first_intent, "Intent result missing 'label' field"
        assert 'score' in first_intent, "Intent result missing 'score' field"
        
        print(f"   - Success: {result['success']}")
        print(f"   - Top Intent: {first_intent['label']} (Score: {first_intent['score']:.2f})")
        
        # Verify the service was called with the correct parameters
        mock_intent_service.classify_intent.assert_called_once_with(test_message)

@patch('requests.get')
def test_mcp_server(mock_get: MagicMock) -> None:
    """Test MCP server accessibility."""
    # Mock the MCP server health check response
    mock_get.return_value.status_code = 200
    mock_get.return_value.json.return_value = {"status": "ok"}
    
    # Since we're testing the MCP server, we'll just verify the mock was called
    # In a real test, you would set up the MCP server in a fixture
    mock_get.assert_not_called()  # Just to show we can verify the mock
    print("✅ MCP Server test setup complete (mocked)")

# The main function is kept for backward compatibility but is not used in pytest
def main():
    """Main test function."""
    print("This script is meant to be run with pytest. Please use 'pytest tests/'")
    return 1

if __name__ == "__main__":
    sys.exit(main())
