"""Tool: Login User."""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "common"))

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, EmailStr, Field
import logging

from common.models.base import BaseResponse
from ..services.auth_service import AuthService

logger = logging.getLogger(__name__)
router = APIRouter()
auth_service = AuthService()


class LoginUserRequest(BaseModel):
    """Login user request model."""
    email: EmailStr = Field(..., description="User's email address")
    password: str = Field(..., description="User's password")


@router.post("/login_user", response_model=BaseResponse)
async def login_user(request: LoginUserRequest):
    """
    Authenticate user and return access token.
    
    This tool authenticates a user with their email and password,
    and returns a JWT access token if successful.
    
    Args:
        request: Login request
        
    Returns:
        BaseResponse with access token
        
    Raises:
        HTTPException: If authentication fails
    """
    try:
        logger.info(f"Login attempt for email: {request.email}")
        
        # Authenticate user
        user = await auth_service.authenticate_user(
            email=request.email,
            password=request.password
        )
        
        if not user:
            logger.warning(f"Authentication failed for: {request.email}")
            raise HTTPException(
                status_code=401,
                detail="Invalid email or password"
            )
        
        # Create access token
        access_token = auth_service.create_access_token(
            user_id=user.id,
            email=user.email
        )
        
        logger.info(f"User logged in successfully: {user.id}")
        
        return BaseResponse(
            success=True,
            message="Login successful",
            data={
                "access_token": access_token,
                "token_type": "bearer",
                "user": {
                    "id": user.id,
                    "email": user.email,
                    "name": user.name,
                    "role": user.role,
                    "is_verified": user.is_verified
                }
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during login: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Login failed: {str(e)}"
        )
