"""
Gemini AI service for chat functionality.

This module provides integration with Google's Gemini AI.
"""

from typing import List, Optional, Dict, Any
import google.generativeai as genai

from ..core.config import settings
from ..core.logging import get_logger
from ..core.exceptions import ExternalServiceError, ConfigurationError
from ..models.responses import GeminiModel

logger = get_logger(__name__)


class GeminiService:
    """Service for interacting with Google's Gemini AI."""
    
    def __init__(self):
        """Initialize the Gemini service."""
        self._model = None
        self._initialize_client()
    
    def _initialize_client(self) -> None:
        """Initialize the Gemini client."""
        try:
            logger.info("Initializing Gemini AI client")
            
            # Configure the API key
            genai.configure(api_key=settings.gemini_api_key)
            
            # Initialize the model
            self._model = genai.GenerativeModel(settings.gemini_model)
            
            logger.info(f"Gemini AI client initialized with model: {settings.gemini_model}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Gemini client: {str(e)}")
            raise ConfigurationError(
                f"Failed to initialize Gemini AI: {str(e)}",
                details={"model": settings.gemini_model}
            )
    
    def generate_response(self, prompt: str) -> str:
        """
        Generate a response using Gemini AI.
        
        Args:
            prompt: Input prompt for the AI
            
        Returns:
            Generated response text
            
        Raises:
            ExternalServiceError: If the API call fails
        """
        if not self._model:
            raise ExternalServiceError(
                service_name="gemini",
                message="Gemini model not initialized"
            )
        
        if not prompt or not prompt.strip():
            raise ValueError("Prompt cannot be empty")
        
        try:
            logger.debug(f"Generating Gemini response for prompt: {prompt[:100]}...")
            
            # Generate content with a reasonable timeout
            response = self._model.generate_content(
                prompt.strip(),
                generation_config={
                    "max_output_tokens": 1024,
                    "temperature": 0.7,
                }
            )
            
            # Handle different response formats from Gemini
            if not response:
                raise ExternalServiceError(
                    service_name="gemini",
                    message="No response received from Gemini AI"
                )
                
            try:
                # Try to get the response text
                response_text = response.text
                if not response_text:
                    # Check if there's a finish reason that might explain the empty response
                    if hasattr(response, 'candidates') and response.candidates:
                        candidate = response.candidates[0]
                        if hasattr(candidate, 'finish_reason') and candidate.finish_reason == 2:
                            logger.warning("Gemini response was filtered or interrupted")
                            return "I'm sorry, but I can't provide a response to that request due to content safety restrictions."
                    
                    raise ExternalServiceError(
                        service_name="gemini",
                        message="Empty response from Gemini AI"
                    )
                
                logger.debug(f"Gemini response generated successfully: {len(response_text)} characters")
                return response_text
                
            except ValueError as ve:
                # Handle specific case where response.text access fails
                if "requires the response to contain a valid `Part`" in str(ve):
                    logger.warning(f"Gemini response format error: {str(ve)}")
                    if hasattr(response, 'prompt_feedback') and response.prompt_feedback.block_reason:
                        return "I'm sorry, but I can't process that request due to content safety restrictions."
                    return "I'm having trouble processing your request right now. Please try again or rephrase your question."
                raise  # Re-raise other ValueErrors
            
        except Exception as e:
            error_msg = str(e).lower()
            logger.error(f"Gemini API call failed: {str(e)}")
            
            # Check for timeout or deadline exceeded errors
            if any(term in error_msg for term in ["deadline exceeded", "timeout", "timed out", "504"]):
                logger.warning("Gemini API request timed out, returning fallback response")
                return "I'm having trouble connecting to the AI service right now. Please try again in a moment."
                
            # For other errors, raise the original exception
            raise ExternalServiceError(
                service_name="gemini",
                message=f"Failed to generate response: {str(e)}",
                details={"prompt": prompt[:100]}
            )
    
    def generate_chat_response(
        self,
        user_message: str,
        detected_intent: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
   ) -> str:
        """
        Generate a chat response with context.
        
        Args:
            user_message: User's message
            detected_intent: Detected intent (if any)
            context: Additional context information
            
        Returns:
            Generated chat response
        """
        try:
            # Log the incoming request
            logger.debug(
                f"[GEMINI_CHAT] Generating response. "
                f"Intent: {detected_intent or 'None'}, "
                f"Context keys: {list(context.keys()) if context else 'None'}"
            )
            
            # Build the prompt with context
            prompt_parts = [f"The user said: '{user_message}'."]
            
            if detected_intent:
                prompt_parts.append(f"The detected intent is: '{detected_intent}'.")
            
            if context:
                for key, value in context.items():
                    # Skip logging potentially large context values
                    log_value = f"{value}" if not isinstance(value, (list, dict)) or len(str(value)) < 100 else f"[{type(value).__name__} with {len(value)} items]"
                    prompt_parts.append(f"{key}: {log_value}")
            
            prompt_parts.append("Respond helpfully and naturally, taking into account the intent and context.")
            
            prompt = "\n".join(prompt_parts)
            
            # Log the generated prompt (truncated for logging)
            logger.debug(f"[GEMINI_CHAT] Generated prompt (truncated): {prompt[:200]}..." if len(prompt) > 200 else f"[GEMINI_CHAT] Generated prompt: {prompt}")
            
            # Generate the response
            response = self.generate_response(prompt)
            
            # Log the response (truncated for logging)
            logger.debug(f"[GEMINI_CHAT] Generated response (truncated): {response[:200]}..." if len(response) > 200 else f"[GEMINI_CHAT] Generated response: {response}")
            
            return response
            
        except Exception as e:
            logger.error(f"[GEMINI_CHAT] Error generating chat response: {str(e)}", exc_info=True)
            
            # Return a user-friendly error message
            if isinstance(e, ExternalServiceError):
                if "content safety" in str(e).lower():
                    return "I'm sorry, but I can't provide a response to that request due to content safety restrictions."
                
                if "timeout" in str(e).lower() or "deadline" in str(e).lower():
                    return "I'm having trouble connecting to the AI service right now. Please try again in a moment."
            
            return "I'm sorry, I'm having trouble processing your request right now. Please try again later."
        
        return self.generate_response(prompt)
    
    def list_available_models(self) -> List[GeminiModel]:
        """
        List available Gemini models.
        
        Returns:
            List of available models
            
        Raises:
            ExternalServiceError: If the API call fails
        """
        try:
            logger.debug("Fetching available Gemini models")
            
            models = genai.list_models()
            
            available_models = [
                GeminiModel(
                    name=model.name,
                    supported_generation_methods=model.supported_generation_methods
                )
                for model in models
            ]
            
            logger.debug(f"Found {len(available_models)} available Gemini models")
            
            return available_models
            
        except Exception as e:
            logger.error(f"Failed to list Gemini models: {str(e)}")
            raise ExternalServiceError(
                service_name="gemini",
                message=f"Failed to list models: {str(e)}"
            )
    
    def is_service_ready(self) -> bool:
        """Check if the service is ready for use."""
        return self._model is not None


# Global service instance
gemini_service = GeminiService()
