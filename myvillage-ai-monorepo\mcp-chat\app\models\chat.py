"""Chat message models."""
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum


class MessageType(str, Enum):
    """Message type enumeration."""
    TEXT = "TEXT"
    FILE = "FILE"
    URL = "URL"


class ChatMessage(BaseModel):
    """Chat message model matching DynamoDB schema."""
    id: str
    userId: str
    chatType: str
    message: str
    role: str  # "user" or "assistant"
    messageType: MessageType = MessageType.TEXT
    fileUrl: Optional[str] = ""
    isDeleted: str = "false"
    createdAt: str  # ISO datetime string
    
    class Config:
        use_enum_values = True


class ChatMessageCreate(BaseModel):
    """Request model for creating a chat message."""
    userId: str
    chatType: str
    message: str
    role: str
    messageType: MessageType = MessageType.TEXT
    fileUrl: Optional[str] = ""
    
    class Config:
        use_enum_values = True


class ChatMessageResponse(BaseModel):
    """Response model for chat message operations."""
    success: bool
    message: str
    data: Optional[ChatMessage] = None


class ConversationMetadata(BaseModel):
    """Metadata for a conversation."""
    conversationId: str
    userId: str
    chatType: str
    title: str  # First user message or auto-generated
    lastMessage: str
    lastMessageTime: str
    messageCount: int


class ConversationListResponse(BaseModel):
    """Response model for listing conversations."""
    success: bool
    message: str
    data: Optional[List[ConversationMetadata]] = None


class ChatHistoryRequest(BaseModel):
    """Request model for getting chat history."""
    userId: str
    chatType: Optional[str] = None
    limit: Optional[int] = 50


class ChatHistoryResponse(BaseModel):
    """Response model for chat history."""
    success: bool
    message: str
    data: Optional[List[ChatMessage]] = None


class DeleteMessageRequest(BaseModel):
    """Request model for deleting a message."""
    messageId: str
    userId: str


class DeleteMessageResponse(BaseModel):
    """Response model for delete operation."""
    success: bool
    message: str
