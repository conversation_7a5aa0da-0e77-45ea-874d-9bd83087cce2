"""Configuration settings for the orchestrator service."""

from pydantic_settings import BaseSettings
from typing import List


class Settings(BaseSettings):
    """Application settings."""
    
    app_name: str = "MVP Conversation Orchestrator"
    app_version: str = "1.0.0"
    debug: bool = True
    
    # Server
    host: str = "0.0.0.0"
    port: int = 8100
    
    # CORS
    cors_origins: List[str] = ["*"]
    cors_allow_credentials: bool = True
    cors_allow_methods: List[str] = ["*"]
    cors_allow_headers: List[str] = ["*"]
    
    # MCP Service URLs
    onboarding_mcp_url: str = "http://localhost:8000"
    activity_mcp_url: str = "http://localhost:8002"
    intent_mcp_url: str = "http://localhost:8003"
    
    # Logging
    log_level: str = "INFO"
    
    class Config:
        env_file = ".env"
        case_sensitive = False


settings = Settings()
