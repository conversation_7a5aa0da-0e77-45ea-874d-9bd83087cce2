from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, validator, HttpUrl
from enum import Enum

class SubmissionStatus(str, Enum):
    DRAFT = "draft"
    SUBMITTED = "submitted"
    IN_REVIEW = "in_review"
    APPROVED = "approved"
    REJECTED = "rejected"
    NEEDS_REVISION = "needs_revision"

class MediaType(str, Enum):
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    DOCUMENT = "document"
    OTHER = "other"

class MediaItem(BaseModel):
    url: str
    media_type: MediaType
    thumbnail_url: Optional[str] = None
    duration: Optional[float] = None  # in seconds for audio/video
    metadata: Dict[str, Any] = Field(default_factory=dict)

class SubmissionBase(BaseModel):
    activity_id: str
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=5000)
    content: Optional[Dict[str, Any]] = None
    category_type: Optional[str] = None
    location: Optional[Dict[str, Any]] = None
    is_public: bool = True
    media: List[MediaItem] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)

class SubmissionCreate(SubmissionBase):
    submitted_by: str

class SubmissionUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=5000)
    content: Optional[Dict[str, Any]] = None
    status: Optional[SubmissionStatus] = None
    feedback: Optional[str] = None
    grade: Optional[float] = Field(None, ge=0, le=100)
    is_public: Optional[bool] = None
    media: Optional[List[MediaItem]] = None
    metadata: Optional[Dict[str, Any]] = None

class Submission(SubmissionBase):
    id: str
    status: SubmissionStatus = SubmissionStatus.SUBMITTED
    submitted_by: str
    submitted_at: datetime
    updated_at: datetime
    feedback: Optional[str] = None
    grade: Optional[float] = Field(None, ge=0, le=100)
    graded_by: Optional[str] = None
    graded_at: Optional[datetime] = None
    view_count: int = 0
    like_count: int = 0

    class Config:
        from_attributes = True

class SubmissionListResponse(BaseModel):
    submissions: List[Submission]
    total: int
    limit: int
    offset: int
