"""Authentication service."""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "common"))

from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import logging
import uuid
import httpx
from jose import JWTError, jwt

from common.utils.security import hash_password, verify_password
from common.utils.validation import validate_email

from ..models.user import UserCreate, UserInDB, User
from ..core.config import settings

logger = logging.getLogger(__name__)


class AuthService:
    """Authentication service - uses external AWS APIs."""
    
    def __init__(self):
        """Initialize auth service."""
        self.auth_url = settings.auth_api_url
        self.signup_url = settings.signup_api_url
        self.timeout = settings.auth_api_timeout
        self.bearer_token = settings.api_bearer_token
    
    async def create_user(
        self,
        name: str,
        email: str,
        password: str,
        phone_number: Optional[str] = None
    ) -> User:
        """
        Create a new user via external API.
        
        Args:
            name: User's full name
            email: User's email address
            password: User's password
            phone_number: Optional phone number
            
        Returns:
            Created user
            
        Raises:
            ValueError: If signup fails
        """
        # Validate email
        if not validate_email(email):
            raise ValueError("Invalid email format")
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                # Prepare request data (matching old API format)
                request_data = {
                    "email": email,
                    "password": password,
                    "name": name,
                    "firstName": name.split()[0] if name else "",
                    "lastName": " ".join(name.split()[1:]) if len(name.split()) > 1 else "",
                    "phoneNumber": phone_number,
                    "role": "MEMBER",
                    "assignedRole": "MEMBER",
                    "userType": "loginUser"
                }
                
                headers = {
                    "Content-Type": "application/json",
                    "User-Agent": "OnboardingMCP/1.0"
                }
                
                if self.bearer_token:
                    headers["Authorization"] = f"Bearer {self.bearer_token}"
                
                logger.info(f"Creating user via external API: {email}")
                
                response = await client.post(
                    self.signup_url,
                    json=request_data,
                    headers=headers
                )
                
                response.raise_for_status()
                response_data = response.json()
                
                if response_data.get("success"):
                    logger.info(f"User created successfully: {email}")
                    
                    # Return user object
                    return User(
                        id=response_data.get("data", {}).get("id", str(uuid.uuid4())),
                        email=email,
                        name=name,
                        phone_number=phone_number,
                        role="user",
                        is_active=True,
                        is_verified=False,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow()
                    )
                else:
                    error_msg = response_data.get("message", "Signup failed")
                    raise ValueError(error_msg)
                    
            except httpx.HTTPStatusError as e:
                logger.error(f"HTTP error during signup: {e}")
                raise ValueError(f"Signup failed: {e.response.status_code}")
            except Exception as e:
                logger.error(f"Error creating user: {e}")
                raise ValueError(f"Failed to create user: {str(e)}")
    
    async def authenticate_user(self, email: str, password: str) -> Optional[UserInDB]:
        """
        Authenticate a user via external API.
        
        Args:
            email: User's email
            password: User's password
            
        Returns:
            User if authenticated, None otherwise
        """
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                request_data = {
                    "email": email,
                    "password": password
                }
                
                headers = {
                    "Content-Type": "application/json",
                    "User-Agent": "OnboardingMCP/1.0"
                }
                
                logger.info(f"Authenticating user via external API: {email}")
                
                response = await client.post(
                    self.auth_url,
                    json=request_data,
                    headers=headers
                )
                
                response.raise_for_status()
                response_data = response.json()
                
                if response_data.get("success"):
                    # API returns 'userData' not 'data'
                    user_data = response_data.get("userData") or response_data.get("data") or {}
                    logger.info(f"User authenticated successfully: {email}")
                    logger.info(f"Full API response body: {response_data}")
                    logger.info(f"Full API response data: {user_data}")
                    
                    # Extract role - check multiple possible fields, prioritize assignedRole
                    raw_role = user_data.get("assignedRole") or user_data.get("role") or "user"
                    # Normalize role to lowercase
                    raw_role = raw_role.lower() if isinstance(raw_role, str) else "user"
                    
                    # Map external roles to internal roles
                    role_mapping = {
                        "subscriber": "user",
                        "member": "user",
                        "admin": "admin",
                        "moderator": "moderator",
                        "super_admin": "super_admin"
                    }
                    role = role_mapping.get(raw_role, "user")
                    logger.info(f"Extracted role: {raw_role} -> mapped to: {role}")
                    
                    # Return UserInDB object
                    return UserInDB(
                        id=user_data.get("id", str(uuid.uuid4())),
                        email=email,
                        name=user_data.get("name", ""),
                        phone_number=user_data.get("phoneNumber"),
                        password_hash="",  # Not needed from external API
                        password_salt="",  # Not needed from external API
                        role=role,
                        is_active=True,
                        is_verified=user_data.get("isVerified", False),
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow()
                    )
                else:
                    logger.warning(f"Authentication failed for: {email}")
                    return None
                    
            except httpx.HTTPStatusError as e:
                logger.error(f"HTTP error during authentication: {e}")
                return None
            except Exception as e:
                logger.error(f"Error authenticating user: {e}")
                return None
    
    def create_access_token(self, user_id: str, email: str) -> str:
        """
        Create JWT access token.
        
        Args:
            user_id: User's ID
            email: User's email
            
        Returns:
            JWT token
        """
        expire = datetime.utcnow() + timedelta(seconds=settings.jwt_expiration)
        to_encode = {
            "sub": user_id,
            "email": email,
            "exp": expire
        }
        encoded_jwt = jwt.encode(
            to_encode,
            settings.jwt_secret,
            algorithm=settings.jwt_algorithm
        )
        return encoded_jwt
    
    def verify_token(self, token: str) -> Optional[dict]:
        """
        Verify JWT token.
        
        Args:
            token: JWT token
            
        Returns:
            Token payload if valid, None otherwise
        """
        try:
            payload = jwt.decode(
                token,
                settings.jwt_secret,
                algorithms=[settings.jwt_algorithm]
            )
            return payload
        except JWTError:
            return None
