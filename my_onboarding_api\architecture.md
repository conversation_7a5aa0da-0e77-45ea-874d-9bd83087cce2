# System Architecture Overview

## 🧱 High-Level Architecture (Best Practice)

The system is built on a modular, microservices-based architecture designed to support multiple client types while maintaining clean separation of concerns.

```
             ┌───────────────────────┐
             │     Custom Chatbot     │
             │  (Gemini / Your UI)    │
             └──────────┬────────────┘
                        │ (REST API)
                        ▼
              ┌──────────────────┐
              │  Orchestrator     │
              │  (Intent logic)   │
              └──────────┬────────┘
                        │          
           ┌─────────────┼────────────────────────┐
           ▼             ▼                        ▼
┌────────────────┐ ┌──────────────────┐ ┌──────────────────┐
│ Onboarding MCP │ │ Activities MCP    │ │ Rewards MCP       │
│ (FastAPI)      │ │ (FastAPI)         │ │ (FastAPI)         │
└────────────────┘ └──────────────────┘ └──────────────────┘
           ▲             ▲                        ▲
           │             │                        │
           │             │                        │
           └─────────────┴────────────────────────┘
                        ▼
               ┌─────────────────┐
               │ Database / APIs │
               └─────────────────┘
```

## Communication Patterns

### 🔵 ChatGPT Integration (Direct MCP)

ChatGPT connects directly to MCP servers without an intermediary orchestrator:

```
ChatGPT → MCP Server → Database
```

**Characteristics:**
- No orchestrator layer needed
- ChatGPT reads MCP manifests to understand available tools
- Automatic model-driven interactions
- Direct database operations

### 🟢 Custom Chatbot Integration (Orchestrator-Mediated)

Custom chatbots route requests through an orchestrator layer that manages intent detection and flow logic:

```
Custom Chatbot → Orchestrator → MCP Server → Database
```

**Characteristics:**
- Orchestrator handles intent detection and request routing
- Manages missing field collection and validation
- Implements business logic and flow control
- Maintains clean separation between frontend and backend

## Key Architecture Benefits

This dual-pattern design ensures:
- ✅ **Code Reusability**: Both ChatGPT and custom chatbots use the same MCP backends
- ✅ **Logic Separation**: Business logic stays clean and modular
- ✅ **Flexibility**: Support multiple client types without duplication
- ✅ **Maintainability**: Each component has a single, well-defined responsibility

## 📁 Monorepo Folder Structure

The recommended folder layout supports multiple deployment scenarios:

- Docker-based containerization
- Render platform deployment
- Local development
- ChatGPT MCP integration
- Custom chatbot with orchestrator

### Directory Layout

```
/your-monorepo
│
├── orchestrator/
│   ├── src/
│   │   ├── main.py                          # Application entry point
│   │   ├── intent_detection.py              # Intent classification logic
│   │   ├── flow_manager.py                  # Request flow orchestration
│   │   └── services/
│   │       ├── onboarding_client.py         # Onboarding MCP client
│   │       ├── activities_client.py         # Activities MCP client
│   │       ├── rewards_client.py            # Rewards MCP client
│   │       └── approval_client.py           # Approval MCP client
│   ├── requirements.txt                     # Python dependencies
│   └── Dockerfile                           # Container configuration
│
├── mcp-onboarding/
│   ├── src/
│   │   ├── server.py                        # MCP server implementation
│   │   └── tools/
│   │       ├── create_user.py               # User account creation tool
│   │       └── verify_otp.py                # OTP verification tool
│   ├── mcp-manifest.json                    # Tool definitions for clients
│   ├── requirements.txt                     # Python dependencies
│   └── Dockerfile                           # Container configuration
│
├── mcp-activities/
│   ├── src/
│   │   ├── server.py                        # MCP server implementation
│   │   └── tools/
│   │       ├── list_activities.py           # Activity listing tool
│   │       └── submit_assignment.py         # Assignment submission tool
│   ├── mcp-manifest.json                    # Tool definitions for clients
│   ├── requirements.txt                     # Python dependencies
│   └── Dockerfile                           # Container configuration
│
├── mcp-rewards/
│   ├── src/
│   │   ├── server.py                        # MCP server implementation
│   │   └── tools/
│   │       └── get_rewards.py               # Rewards retrieval tool
│   ├── mcp-manifest.json                    # Tool definitions for clients
│   ├── requirements.txt                     # Python dependencies
│   └── Dockerfile                           # Container configuration
│
├── mcp-approval/
│   ├── src/
│   │   ├── server.py                        # MCP server implementation
│   │   └── tools/
│   │       ├── approve_activity.py          # Activity approval tool
│   │       └── reject_activity.py           # Activity rejection tool
│   ├── mcp-manifest.json                    # Tool definitions for clients
│   ├── requirements.txt                     # Python dependencies
│   └── Dockerfile                           # Container configuration
│
├── common/
│   ├── models/                              # Shared data models
│   ├── utils/                               # Utility functions
│   ├── config/                              # Configuration management
│   └── constants.py                         # Application constants
│
├── docker-compose.yml                       # Multi-container orchestration
├── README.md                                # Project documentation
└── .github/
    └── workflows/
        └── render-deploy.yml                # CI/CD pipeline for Render
```

### Service Descriptions

| Service | Purpose | Type | Technology |
|---------|---------|------|------------|
| **Orchestrator** | Intent detection and flow management | API Gateway | Python FastAPI |
| **Onboarding MCP** | User account creation and verification | Microservice | Python FastAPI |
| **Activities MCP** | Activity management and submission | Microservice | Python FastAPI |
| **Rewards MCP** | Reward tracking and retrieval | Microservice | Python FastAPI |
| **Approval MCP** | Activity approval workflows | Microservice | Python FastAPI |
| **Common** | Shared code and configurations | Library | Python Modules |

## 🧩 Complete Workflow Example: User Signup

This section demonstrates how the same business process is handled differently depending on the client type.

### Scenario 1: User Signup via ChatGPT (Direct MCP Integration)

#### User Interaction

```
User: "Sign me up"
```

#### ChatGPT Processing Flow

**Step 1: Manifest Discovery**
- ChatGPT reads `mcp-onboarding/mcp-manifest.json`
- Identifies available tools and required parameters
- Determines: `create_user` tool requires `name` and `email` fields

**Step 2: Data Collection**
- ChatGPT asks user for missing information:
  ```
  "I need your name and email to create your account."
  ```

**Step 3: API Call to MCP**
- ChatGPT invokes the MCP tool with collected data:
  ```json
  {
    "name": "John",
    "email": "<EMAIL>"
  }
  ```

**Step 4: Data Processing**
- MCP server receives request
- Writes user data to database
- Returns success response to ChatGPT

**Result**: New user account created successfully

---

### Scenario 2: User Signup via Custom Chatbot (Orchestrator-Mediated)

#### User Interaction

```
User: "Signup me"
```

#### Orchestrator Processing Flow

**Step 1: Intent Detection**
- Custom chatbot sends request to orchestrator
- Orchestrator's intent detection identifies: `"signup"` intent

**Step 2: Request Validation & Data Collection**
- Orchestrator receives minimal user data from chatbot UI
- Identifies missing required fields (name, email)
- Returns prompt to frontend asking for additional information

**Step 3: Orchestrator-to-MCP Communication**
- After collecting all fields, orchestrator calls MCP client internally:
  ```python
  onboarding_client.create_user(
    name="John",
    email="<EMAIL>"
  )
  ```

**Step 4: Response Handling**
- MCP server processes the request (same as ChatGPT flow)
- Orchestrator receives success response
- Returns formatted response to custom chatbot frontend

**Result**: New user account created successfully + custom response handling

---

### Key Differences Summary

| Aspect | ChatGPT + MCP | Custom Chatbot + Orchestrator |
|--------|---------------|-------------------------------|
| **Intent Detection** | ChatGPT model | Orchestrator service |
| **Field Collection** | ChatGPT conversation | Orchestrator + UI |
| **Orchestration** | None (direct) | Full orchestrator layer |
| **Response Format** | ChatGPT natural language | Custom frontend-specific format |
| **Flow Control** | LLM-driven | Application logic-driven |

---

## 🧱 Why This Design is Perfect

### Code Reusability
- ✅ **No Duplication**: Both ChatGPT and custom chatbots use identical MCP backends
- ✅ **Single Source of Truth**: Business logic lives in one place

### Clean Architecture
- ✅ **ChatGPT → MCP**: No model training required; manifests handle tool discovery
- ✅ **Custom Bot → Orchestrator**: Full control over user flows and response formatting
- ✅ **MCP Modularity**: Each service has distinct responsibilities (onboarding, activities, rewards, approval)

### Deployment Flexibility
- ✅ **Individual Deployment**: Each MCP can be deployed independently
- ✅ **Platform Support**: Works with Render, Railway, Fly.io, and Docker
- ✅ **Scalability**: Services can scale independently based on demand

### Maintainability
- ✅ **Clear Separation**: Frontend concerns isolated from backend logic
- ✅ **Easy Testing**: Each service can be tested independently
- ✅ **Future-Proof**: Adding new MCP services doesn't require orchestrator changes

🧩 WORKFLOW EXAMPLE: User Signup

Let’s walk through one example from both sides.

🔵 1. User Signup via ChatGPT (using MCP)
User:
Sign me up

ChatGPT:

Reads your mcp-onboarding/mcp-manifest.json, identifies:

➡ Tool name: create_user
➡ Required fields: name, email

So ChatGPT asks user:

I need your name and email to create your account.


After gathering fields:

Calling MCP tool: onboarding.create_user


Then ChatGPT POSTs:

{
  "name": "John",
  "email": "<EMAIL>"
}


MCP → writes to database → returns success.

🟢 2. User Signup via Custom Chatbot (using Orchestrator)
User:
Signup me

Flow:
Custom chatbot UI
   → Orchestrator
      → Detect intent “signup”
      → Ask for missing fields
      → Call mcp-onboarding tool internally (HTTP call)


Orchestrator does something like:

onboarding_client.create_user(name, email)


Returns successful response to frontend.

✔ Your orchestrator makes custom chatbot flows easy
✔ MCP makes ChatGPT flows automatic

Both use SAME backend — no code duplication.

🧱 WHY THIS DESIGN IS PERFECT
✔ ChatGPT → MCP = NO model training
✔ Custom chatbot → orchestrator = Your own flow control
✔ MCP servers give you modular roles

(onboarding, activities, rewards, approval)

✔ Each MCP is individually deployable

Render / Railway / Fly.io all support monorepos with separate services.

✔ Orchestrator only needed for custom chatbot

AI models detect intent, MCP handles final task.