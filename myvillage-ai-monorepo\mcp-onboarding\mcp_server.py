import sys
from pathlib import Path
import logging

# Add parent directory and common to path
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))
sys.path.insert(0, str(parent_dir / "common"))

from mcp.server.fastmcp import FastMCP
from app.services.auth_service import AuthService
from app.models.user import UserCreate, UserLogin

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize MCP Server
mcp = FastMCP("Onboarding")

# Initialize Service
auth_service = AuthService()

@mcp.tool()
async def create_user(
    name: str,
    email: str,
    password: str,
    phone_number: str = None
) -> dict:
    """
    Create a new user account.
    
    Args:
        name: Full name
        email: Email address
        password: Password (min 8 characters)
        phone_number: Optional phone number
    """
    try:
        user_data = UserCreate(
            name=name,
            email=email,
            password=password,
            phone_number=phone_number
        )
        
        result = await auth_service.create_user(user_data)
        
        return {
            "success": True,
            "user_id": result.get("user_id"),
            "message": "User created successfully. Please verify your email."
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@mcp.tool()
async def login_user(email: str, password: str) -> dict:
    """
    Authenticate user and return access token.
    
    Args:
        email: Email address
        password: Password
    """
    try:
        # Authenticate user via external API
        user = await auth_service.authenticate_user(email, password)
        
        if not user:
            return {
                "success": False,
                "error": "Invalid email or password"
            }
        
        # Create access token
        access_token = auth_service.create_access_token(user.id, user.email)
        
        return {
            "success": True,
            "access_token": access_token,
            "user_id": user.id,
            "name": user.name,
            "email": user.email,
            "role": user.role,
            "message": "Login successful"
        }
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@mcp.tool()
async def verify_otp(email: str, otp: str) -> dict:
    """
    Verify OTP code for user.
    
    Args:
        email: Email address
        otp: 6-digit OTP code
    """
    try:
        result = await auth_service.verify_otp(email, otp)
        
        return {
            "success": True,
            "message": "OTP verified successfully"
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@mcp.tool()
async def update_profile(user_id: str, data: dict) -> dict:
    """
    Update user profile information.
    
    Args:
        user_id: User ID
        data: Profile data to update
    """
    try:
        result = await auth_service.update_profile(user_id, data)
        
        return {
            "success": True,
            "message": "Profile updated successfully"
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

if __name__ == "__main__":
    mcp.run()
