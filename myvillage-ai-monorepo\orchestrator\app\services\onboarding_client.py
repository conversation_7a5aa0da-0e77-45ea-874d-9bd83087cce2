"""Real HTTP client for Onboarding MCP."""
import httpx
from typing import Dict, Any
import logging
from ..core.config import settings

logger = logging.getLogger(__name__)


class OnboardingClient:
    """Client for calling Onboarding MCP service."""
    
    def __init__(self):
        self.base_url = settings.onboarding_mcp_url
        self.timeout = settings.mcp_timeout
    
    async def create_user(self, name: str, email: str, password: str) -> Dict[str, Any]:
        """Call create_user tool on Onboarding MCP."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                logger.info(f"Calling onboarding MCP: create_user")
                response = await client.post(
                    f"{self.base_url}/tools/create_user",
                    json={"name": name, "email": email, "password": password}
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"Error calling onboarding MCP: {e}")
                raise
    
    async def login_user(self, email: str, password: str) -> Dict[str, Any]:
        """Call login_user tool on Onboarding MCP."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                logger.info(f"Calling onboarding MCP: login_user")
                response = await client.post(
                    f"{self.base_url}/tools/login_user",
                    json={"email": email, "password": password}
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"Error calling onboarding MCP: {e}")
                raise
    
    async def health_check(self) -> bool:
        """Check if onboarding MCP is healthy."""
        async with httpx.AsyncClient(timeout=5) as client:
            try:
                response = await client.get(f"{self.base_url}/health")
                return response.status_code == 200
            except:
                return False
