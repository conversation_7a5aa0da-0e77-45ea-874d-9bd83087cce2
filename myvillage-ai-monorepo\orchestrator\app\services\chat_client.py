"""Chat client for communicating with MCP Chat service."""
import httpx
import logging
from typing import Optional, List, Dict, Any

logger = logging.getLogger(__name__)


class ChatClient:
    """Client for MCP Chat service."""
    
    def __init__(self, base_url: str = "http://mcp-chat:8000"):
        """
        Initialize chat client.
        
        Args:
            base_url: Base URL for chat service
        """
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def save_message(
        self,
        user_id: str,
        chat_type: str,
        message: str,
        role: str,
        message_type: str = "TEXT",
        file_url: str = ""
    ) -> Dict[str, Any]:
        """
        Save a chat message.
        
        Args:
            user_id: User ID
            chat_type: Chat type (e.g., "GeneralWeb")
            message: Message content
            role: Message role ("user" or "assistant")
            message_type: Message type ("TEXT", "FILE", "URL")
            file_url: Optional file URL
            
        Returns:
            Response data
        """
        try:
            response = await self.client.post(
                f"{self.base_url}/messages",
                json={
                    "userId": user_id,
                    "chatType": chat_type,
                    "message": message,
                    "role": role,
                    "messageType": message_type,
                    "fileUrl": file_url
                }
            )
            response.raise_for_status()
            return response.json()
        except httpx.HTTPError as e:
            logger.error(f"Error saving message: {e}")
            return {"success": False, "message": str(e)}
        except Exception as e:
            logger.error(f"Unexpected error saving message: {e}")
            return {"success": False, "message": str(e)}
    
    async def get_chat_history(
        self,
        user_id: str,
        chat_type: Optional[str] = None,
        limit: int = 50
    ) -> Dict[str, Any]:
        """
        Get chat history for a user.
        
        Args:
            user_id: User ID
            chat_type: Optional chat type filter
            limit: Maximum number of messages
            
        Returns:
            Response data with messages
        """
        try:
            response = await self.client.post(
                f"{self.base_url}/messages/history",
                json={
                    "userId": user_id,
                    "chatType": chat_type,
                    "limit": limit
                }
            )
            response.raise_for_status()
            return response.json()
        except httpx.HTTPError as e:
            logger.error(f"Error getting chat history: {e}")
            return {"success": False, "message": str(e), "data": []}
        except Exception as e:
            logger.error(f"Unexpected error getting chat history: {e}")
            return {"success": False, "message": str(e), "data": []}
    
    async def get_conversations(self, user_id: str) -> Dict[str, Any]:
        """
        Get list of conversations for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            Response data with conversations
        """
        try:
            response = await self.client.get(
                f"{self.base_url}/conversations/{user_id}"
            )
            response.raise_for_status()
            return response.json()
        except httpx.HTTPError as e:
            logger.error(f"Error getting conversations: {e}")
            return {"success": False, "message": str(e), "data": []}
        except Exception as e:
            logger.error(f"Unexpected error getting conversations: {e}")
            return {"success": False, "message": str(e), "data": []}
    
    async def delete_message(self, message_id: str, user_id: str) -> Dict[str, Any]:
        """
        Delete a message.
        
        Args:
            message_id: Message ID
            user_id: User ID
            
        Returns:
            Response data
        """
        try:
            response = await self.client.delete(
                f"{self.base_url}/messages/{message_id}",
                json={"userId": user_id}
            )
            response.raise_for_status()
            return response.json()
        except httpx.HTTPError as e:
            logger.error(f"Error deleting message: {e}")
            return {"success": False, "message": str(e)}
        except Exception as e:
            logger.error(f"Unexpected error deleting message: {e}")
            return {"success": False, "message": str(e)}
