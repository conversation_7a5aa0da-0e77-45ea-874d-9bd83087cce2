# MyVillage Chat UI - Setup Guide

## 🎉 Your ChatGPT-like UI is Ready!

A beautiful chat interface to test all your MyVillage AI features.

---

## 🚀 Quick Start (3 Steps)

### Step 1: Start Backend Services

```bash
cd ../myvillage-ai-monorepo
./start-all.sh
```

Wait for all services to show ✅ healthy status.

### Step 2: Start the Chat UI

```bash
cd ../myvillage-chat-ui
./start-ui.sh
```

Or manually:
```bash
npm run dev
```

### Step 3: Open in Browser

Visit: **http://localhost:3000**

---

## 🧪 Test All Features

### 1. Test Activities MCP
```
Type: "Show me available activities"
```
Expected: Response from Activities MCP service

### 2. Test Rewards MCP
```
Type: "Check my rewards"
```
Expected: Response from Rewards MCP service

### 3. Test Onboarding MCP
```
Type: "I want to sign up"
```
Expected: Response from Onboarding MCP service

### 4. Test General Chat
```
Type: "Hello, how are you?"
```
Expected: General conversation response

---

## 📊 What You'll See

- **Intent Detection**: Each message shows which intent was detected
- **MCP Service**: Shows which microservice handled the request
- **Real-time Responses**: Instant AI responses
- **Service Status**: Sidebar shows all running services
- **Chat History**: All messages in the conversation

---

## 🎨 Features

✅ ChatGPT-inspired design  
✅ Dark mode interface  
✅ Responsive (mobile & desktop)  
✅ Real-time intent detection  
✅ MCP service routing display  
✅ Service health monitoring  
✅ Message timestamps  
✅ Loading states  
✅ Error handling  

---

## 🔧 Configuration

### Change API URL

Edit `.env.local`:
```env
NEXT_PUBLIC_API_URL=http://localhost:8100
```

### Change Port

```bash
PORT=3001 npm run dev
```

---

## 📁 Project Structure

```
myvillage-chat-ui/
├── app/
│   ├── layout.tsx          # Root layout
│   ├── page.tsx            # Home page
│   └── globals.css         # Global styles
├── components/
│   ├── ChatInterface.tsx   # Main chat logic
│   ├── ChatMessage.tsx     # Message bubbles
│   ├── ChatInput.tsx       # Input field
│   └── Sidebar.tsx         # Sidebar with status
├── .env.local              # Environment variables
└── package.json            # Dependencies
```

---

## 🐛 Troubleshooting

### Backend not responding
```bash
# Check if services are running
curl http://localhost:8100/health

# Restart services
cd ../myvillage-ai-monorepo
./stop-all.sh
./start-all.sh
```

### Port 3000 already in use
```bash
# Kill process on port 3000
netstat -ano | findstr :3000
taskkill /PID <PID> /F

# Or use different port
PORT=3001 npm run dev
```

### Dependencies issues
```bash
# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

---

## 🎯 Next Steps

1. **Customize the UI**: Edit components in `components/` folder
2. **Add Features**: Extend `ChatInterface.tsx` with new functionality
3. **Style Changes**: Modify `tailwind.config.ts` for colors
4. **Add Chat History**: Implement persistent storage
5. **User Authentication**: Add login/signup flow

---

## 📚 Tech Stack

- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **HTTP**: Axios
- **Backend**: MyVillage AI Orchestrator

---

## 🎉 You're All Set!

Your chat UI is ready to test all MyVillage AI features. Enjoy! 🚀

For more details, see [README.md](./README.md)
