"""MCP Tool: Get conversations list."""
import logging
from typing import Dict, Any

from ..services.chat_service import get_chat_service

logger = logging.getLogger(__name__)


async def get_conversations(userId: str) -> Dict[str, Any]:
    """
    Get list of conversations for a user.
    
    Args:
        userId: User ID
        
    Returns:
        Dictionary with success status and conversations list
    """
    try:
        chat_service = get_chat_service()
        
        # Get conversations
        conversations = chat_service.get_conversations(userId)
        
        return {
            "success": True,
            "message": f"Retrieved {len(conversations)} conversations",
            "data": [conv.dict() for conv in conversations]
        }
        
    except Exception as e:
        logger.error(f"Error in get_conversations tool: {e}")
        return {
            "success": False,
            "message": f"Failed to retrieve conversations: {str(e)}",
            "data": []
        }


# MCP Tool metadata
TOOL_METADATA = {
    "name": "get_conversations",
    "description": "Get list of conversations for a user",
    "parameters": {
        "type": "object",
        "properties": {
            "userId": {
                "type": "string",
                "description": "User ID"
            }
        },
        "required": ["userId"]
    }
}
