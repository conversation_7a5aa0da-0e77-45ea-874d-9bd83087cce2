"""MCP Tool: Delete chat message."""
import logging
from typing import Dict, Any

from ..services.chat_service import get_chat_service

logger = logging.getLogger(__name__)


async def delete_message(messageId: str, userId: str) -> Dict[str, Any]:
    """
    Soft delete a chat message.
    
    Args:
        messageId: Message ID
        userId: User ID (for verification)
        
    Returns:
        Dictionary with success status
    """
    try:
        chat_service = get_chat_service()
        
        # Delete message
        success = chat_service.delete_message(messageId, userId)
        
        if success:
            return {
                "success": True,
                "message": "Message deleted successfully"
            }
        else:
            return {
                "success": False,
                "message": "Message not found or user mismatch"
            }
        
    except Exception as e:
        logger.error(f"Error in delete_message tool: {e}")
        return {
            "success": False,
            "message": f"Failed to delete message: {str(e)}"
        }


# MCP Tool metadata
TOOL_METADATA = {
    "name": "delete_message",
    "description": "Soft delete a chat message",
    "parameters": {
        "type": "object",
        "properties": {
            "messageId": {
                "type": "string",
                "description": "Message ID"
            },
            "userId": {
                "type": "string",
                "description": "User ID for verification"
            }
        },
        "required": ["messageId", "userId"]
    }
}
