"""Database configuration and utilities."""
import boto3
from typing import Optional
import logging
from .config import settings

logger = logging.getLogger(__name__)


class Database:
    """Database connection manager."""
    
    def __init__(self):
        """Initialize database connection."""
        self._resource = None
        self._client = None
    
    @property
    def resource(self):
        """Get DynamoDB resource."""
        if self._resource is None:
            self._resource = boto3.resource(
                'dynamodb',
                region_name=settings.aws_region
            )
        return self._resource
    
    @property
    def client(self):
        """Get DynamoDB client."""
        if self._client is None:
            self._client = boto3.client(
                'dynamodb',
                region_name=settings.aws_region
            )
        return self._client
    
    def get_table(self, table_name: str):
        """
        Get a DynamoDB table.
        
        Args:
            table_name: Name of the table
            
        Returns:
            DynamoDB table resource
        """
        return self.resource.Table(table_name)


# Global database instance
db = Database()


def get_db() -> Database:
    """
    Get database instance.
    
    Returns:
        Database instance
    """
    return db
