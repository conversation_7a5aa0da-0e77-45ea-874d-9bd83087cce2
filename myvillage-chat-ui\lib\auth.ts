// Authentication utilities

export type UserRole = 'admin' | 'stakeholder' | 'user' | 'guest'

export interface User {
  id: string
  email: string
  name: string
  role: UserRole
  token?: string
}

const USER_STORAGE_KEY = 'myvillage_user'

export const authUtils = {
  // Save user to localStorage
  saveUser: (user: User) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user))
    }
  },

  // Get user from localStorage
  getUser: (): User | null => {
    if (typeof window !== 'undefined') {
      const userStr = localStorage.getItem(USER_STORAGE_KEY)
      if (userStr) {
        try {
          return JSON.parse(userStr)
        } catch {
          return null
        }
      }
    }
    return null
  },

  // Remove user from localStorage
  clearUser: () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(USER_STORAGE_KEY)
    }
  },

  // Check if user is logged in
  isLoggedIn: (): boolean => {
    return authUtils.getUser() !== null
  },

  // Get display name (user name or "Guest")
  getDisplayName: (): string => {
    const user = authUtils.getUser()
    return user?.name || 'Guest'
  },

  // Get user role
  getRole: (): UserRole => {
    const user = authUtils.getUser()
    return user?.role || 'guest'
  },

  // Get role display label
  getRoleLabel: (): string => {
    const role = authUtils.getRole()
    const labels: Record<UserRole, string> = {
      admin: 'Admin',
      stakeholder: 'Stakeholder',
      user: 'User',
      guest: 'Guest'
    }
    return labels[role]
  },
}
