# 🗣️ Conversational Flow Implementation

## Overview

The MyVillage AI system now supports **step-by-step conversational flows** for signup, login, and activity creation. Instead of asking for all information at once, the system guides users through each step.

---

## ✨ How It Works

### 1. **Signup Flow**

**User**: "I want to sign up"

**System**: "Great! Let's get you signed up. What's your full name?"

**User**: "<PERSON>"

**System**: "Thanks <PERSON>! What's your email address?"

**User**: "<EMAIL>"

**System**: "Perfect! Now create a password (minimum 8 characters):"

**User**: "mypassword123"

**System**: "Almost done! What's your phone number? (optional, press skip to continue)"

**User**: "skip"

**System**: "🎉 Welcome John <PERSON>! Your account has been created successfully. You can now log in."

---

### 2. **Login Flow**

**User**: "I want to log in"

**System**: "Welcome back! What's your email address?"

**User**: "<EMAIL>"

**System**: "Great! Now enter your password:"

**User**: "mypassword123"

**System**: "✅ Welcome back! You're now logged in."

---

### 3. **Create Activity Flow**

**User**: "I want to create an activity"

**System**: "Let's create an activity! What's the title?"

**User**: "Math Quiz"

**System**: "Great title! Now provide a description:"

**User**: "A quiz about algebra and geometry"

**System**: "What type of activity is this? (assignment, quiz, project, discussion)"

**User**: "quiz"

**System**: "✅ Activity 'Math Quiz' created successfully!"

---

## 🔧 Technical Implementation

### Backend Components

#### 1. **Conversation Manager** (`conversation_manager.py`)
- Manages conversation state for each user
- Tracks current step in the flow
- Validates user input at each step
- Handles flow completion

#### 2. **Chat Router** (`chat_router.py`)
- Checks for active conversations
- Routes to conversation manager if flow is active
- Detects intent for new conversations
- Executes final action when flow completes

#### 3. **Flow Definitions**
```python
flows = {
    "signup": {
        "steps": [
            {"field": "name", "prompt": "What's your full name?"},
            {"field": "email", "prompt": "What's your email address?"},
            {"field": "password", "prompt": "Create a password:"},
            {"field": "phone", "prompt": "Phone number? (optional)", "optional": True}
        ]
    },
    "login": {
        "steps": [
            {"field": "email", "prompt": "What's your email address?"},
            {"field": "password", "prompt": "Enter your password:"}
        ]
    }
}
```

---

## 🎯 Features

### ✅ Step-by-Step Guidance
- One question at a time
- Clear prompts for each field
- Validation at each step

### ✅ Input Validation
- Email format checking
- Password length requirements
- Activity type validation

### ✅ Optional Fields
- Users can skip optional fields
- Type "skip" to continue

### ✅ Session Management
- 30-minute timeout for inactive conversations
- Automatic cleanup of expired sessions

### ✅ Cancel Anytime
- Type "cancel", "stop", "exit", or "quit" to cancel
- Returns to normal chat mode

---

## 🧪 Testing the Flows

### Test Signup
```
User: "I want to sign up"
→ Follow the prompts step by step
```

### Test Login
```
User: "I want to log in"
→ Provide email and password when asked
```

### Test Activity Creation
```
User: "Create an activity"
→ Provide title, description, and type
```

### Test Cancel
```
User: "I want to sign up"
System: "What's your full name?"
User: "cancel"
System: "Conversation cancelled. How else can I help you?"
```

---

## 📊 Conversation State

Each active conversation stores:
- **user_id**: Who is in the conversation
- **flow**: Which flow (signup, login, create_activity)
- **step**: Current step number
- **data**: Collected information so far
- **created_at**: When conversation started
- **updated_at**: Last activity timestamp

---

## 🔄 Flow Lifecycle

```
1. User triggers intent (e.g., "sign up")
   ↓
2. System starts conversation flow
   ↓
3. System asks first question
   ↓
4. User provides answer
   ↓
5. System validates input
   ↓
6. System stores data and moves to next step
   ↓
7. Repeat steps 3-6 until all fields collected
   ↓
8. System executes final action (create user, login, etc.)
   ↓
9. System returns success message
   ↓
10. Conversation ends
```

---

## 🛠️ Customization

### Add New Flow

Edit `conversation_manager.py`:

```python
self.flows["new_flow"] = {
    "steps": [
        {
            "field": "field_name",
            "prompt": "Your question here?",
            "validation": lambda x: len(x) > 0
        }
    ]
}
```

### Modify Validation

```python
{
    "field": "email",
    "prompt": "What's your email?",
    "validation": lambda x: "@" in x and "." in x
}
```

### Add Optional Fields

```python
{
    "field": "phone",
    "prompt": "Phone number? (optional, type skip)",
    "validation": lambda x: True,
    "optional": True
}
```

---

## 🐛 Troubleshooting

### Conversation Not Starting
- Check logs: `tail -f logs/orchestrator.log`
- Verify intent detection is working
- Ensure conversation_manager is imported

### Validation Failing
- Check validation lambda functions
- Test with different inputs
- Review error messages in logs

### Session Expired
- Default timeout is 30 minutes
- Adjust in `ConversationState.is_expired()`
- User needs to start over

---

## 📝 API Response Format

```json
{
  "success": true,
  "message": "What's your full name?",
  "response": "What's your full name?",
  "intent": "signup",
  "routed_to": "orchestrator",
  "mcp_service": "orchestrator",
  "data": {
    "in_progress": true
  }
}
```

---

## 🎉 Benefits

1. **Better UX**: Users aren't overwhelmed with multiple questions
2. **Validation**: Each input is validated immediately
3. **Flexibility**: Users can cancel anytime
4. **Clarity**: Clear prompts guide users through the process
5. **Scalability**: Easy to add new flows

---

## 🚀 Next Steps

1. Restart the orchestrator to load new code
2. Test the flows in the chat UI
3. Monitor logs for any issues
4. Customize flows as needed

```bash
cd myvillage-ai-monorepo
./stop-all.sh
./start-all.sh
```

Then test in the chat UI at http://localhost:3000

---

Enjoy the conversational experience! 🎊
