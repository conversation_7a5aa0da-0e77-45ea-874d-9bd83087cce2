'use client';

import { useEffect, useState } from 'react';

export default function HtmlWrapper({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Apply theme classes to the html element when mounted
    // Only adjust runtime attributes if they are not already set by SSR
    const docEl = document.documentElement;
    if (!docEl.classList.contains('light') && !docEl.classList.contains('dark')) {
      docEl.classList.add('light');
    }
    if (!docEl.classList.contains('scroll-smooth')) {
      docEl.classList.add('scroll-smooth');
    }
    if (!docEl.style.colorScheme) {
      docEl.style.colorScheme = 'light';
    }
  }, []);

  return <>{children}</>;
}
