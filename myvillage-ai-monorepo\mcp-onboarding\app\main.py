"""Onboarding MCP Service - User signup, login, verification."""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import sys
from pathlib import Path
import logging

# Add common to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "common"))

from common.models.base import HealthResponse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Onboarding MCP",
    version="1.0.0",
    description="MCP service for user onboarding, authentication, and profile management"
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import routers after app creation to avoid circular imports
from .tools import create_user, login_user, verify_otp, update_profile

# Include tool routers
app.include_router(create_user.router, prefix="/tools", tags=["tools"])
app.include_router(login_user.router, prefix="/tools", tags=["tools"])
app.include_router(verify_otp.router, prefix="/tools", tags=["tools"])
app.include_router(update_profile.router, prefix="/tools", tags=["tools"])


@app.get("/", response_model=HealthResponse)
async def root():
    """Root endpoint."""
    return HealthResponse(
        status="healthy",
        service="Onboarding MCP",
        version="1.0.0"
    )


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        service="Onboarding MCP",
        version="1.0.0"
    )


@app.get("/manifest")
async def get_manifest():
    """Return MCP manifest for ChatGPT integration."""
    return {
        "name": "onboarding-mcp",
        "version": "1.0.0",
        "description": "User onboarding, authentication, and profile management",
        "tools": [
            {
                "name": "create_user",
                "description": "Create a new user account",
                "parameters": {
                    "name": {"type": "string", "required": True, "description": "Full name"},
                    "email": {"type": "string", "required": True, "description": "Email address"},
                    "password": {"type": "string", "required": True, "description": "Password (min 8 chars)"},
                    "phone_number": {"type": "string", "required": False, "description": "Phone number"}
                }
            },
            {
                "name": "login_user",
                "description": "Authenticate user and return access token",
                "parameters": {
                    "email": {"type": "string", "required": True, "description": "Email address"},
                    "password": {"type": "string", "required": True, "description": "Password"}
                }
            },
            {
                "name": "verify_otp",
                "description": "Verify OTP code for user",
                "parameters": {
                    "email": {"type": "string", "required": True, "description": "Email address"},
                    "otp": {"type": "string", "required": True, "description": "6-digit OTP code"}
                }
            },
            {
                "name": "update_profile",
                "description": "Update user profile information",
                "parameters": {
                    "user_id": {"type": "string", "required": True, "description": "User ID"},
                    "data": {"type": "object", "required": True, "description": "Profile data"}
                }
            }
        ]
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
