#!/bin/bash

# Test script for MVP Conversation Orchestrator

echo "=========================================="
echo "Testing MVP Conversation Orchestrator"
echo "=========================================="
echo ""

# Health check
echo "1. Health Check:"
curl -X GET http://localhost:8000/health
echo -e "\n"

# Test onboarding intent (signup)
echo "2. Test Signup Intent:"
curl -X POST http://localhost:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user-123", "text": "I want to sign up", "session_id": "session-abc"}'
echo -e "\n"

# Test onboarding intent (login)
echo "3. Test Login Intent:"
curl -X POST http://localhost:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user-456", "text": "I need to log in", "session_id": "session-def"}'
echo -e "\n"

# Test activity intent
echo "4. Test Activity Intent:"
curl -X POST http://localhost:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user-789", "text": "Show me activities", "session_id": "session-ghi"}'
echo -e "\n"

# Test general query (defaults to activity)
echo "5. Test General Query:"
curl -X POST http://localhost:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user-999", "text": "Hello, what can you help me with?", "session_id": "session-jkl"}'
echo -e "\n"

echo "=========================================="
echo "Tests Complete!"
echo "=========================================="
