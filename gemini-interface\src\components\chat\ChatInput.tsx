'use client';

import { useState, useRef, useEffect } from 'react';
import ChatQuickActions from './ChatQuickActions';
import AuthenticatedQuickActions from './AuthenticatedQuickActions';
import { useAuth } from '@/contexts/AuthContext';

export default function ChatInput({ 
  onSendMessage, 
  onAttachFile,
  isLoading = false 
}: { 
  onSendMessage: (message: string) => void;
  onAttachFile?: () => void;
  isLoading?: boolean;
}) {
  const [message, setMessage] = useState('');
  const [messageHistory, setMessageHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isFocused, setIsFocused] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { isAuthenticated, user, isLoggingIn } = useAuth();

  const focusTextarea = () => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !isLoading) {
      onSendMessage(message);
      // Add to history if not a duplicate of the last message
      setMessageHistory(prev => {
        if (prev.length === 0 || prev[0] !== message) {
          return [message, ...prev];
        }
        return prev;
      });
      setMessage('');
      setHistoryIndex(-1);
      // Focus the input after state updates - use multiple approaches for reliability
      requestAnimationFrame(() => {
        focusTextarea();
      });
      // Fallback: also focus after a short delay to handle edge cases
      setTimeout(() => {
        focusTextarea();
      }, 50);
    }
  };

  const navigateHistory = (direction: 'up' | 'down') => {
    if (messageHistory.length === 0) return;
    
    let newIndex = historyIndex;
    
    if (direction === 'up') {
      if (newIndex === -1 && message.trim() !== '') {
        setMessageHistory(prev => [message, ...prev]);
        newIndex = 0;
      } else if (newIndex < messageHistory.length - 1) {
        newIndex++;
      }
    } else {
      if (newIndex > 0) {
        newIndex--;
      } else if (newIndex === 0) {
        newIndex = -1;
        setMessage('');
        return;
      } else {
        return;
      }
    }
    
    setHistoryIndex(newIndex);
    setMessage(newIndex >= 0 ? messageHistory[newIndex] : '');
  };

  useEffect(() => {
    focusTextarea();
  }, []);

  const handleQuickAction = (action: string) => {
    setMessage(action);
    // Focus the input in the next tick to ensure it's rendered
    requestAnimationFrame(() => {
      focusTextarea();
    });
  };

  // Don't show quick actions if loading, during login flow, or if there's no user role when authenticated
  const showQuickActions = !isLoading && 
                        !isLoggingIn && 
                        !message.trim() && 
                        (isAuthenticated ? !!user?.role : true);

  // Debug logging
  useEffect(() => {
    console.log('ChatInput Auth State:', {
      isAuthenticated,
      user,
      isLoading,
      isLoggingIn,
      showQuickActions,
      userRole: user?.role
    });
  }, [isAuthenticated, user, isLoading, isLoggingIn, showQuickActions]);

  return (
    <div className="w-full max-w-3xl mx-auto px-4 pb-4">
      {showQuickActions && (
        isAuthenticated ? (
          <AuthenticatedQuickActions 
            onAction={handleQuickAction} 
            userRole={user?.role} 
            key={`auth-actions-${user?.id || 'unauth'}`}
          />
        ) : (
          <ChatQuickActions 
            onAction={handleQuickAction}
            isLoggingIn={isLoggingIn}
            key="unauth-actions"
          />
        )
      )}
      <form onSubmit={handleSubmit}>
        <div className={`flex items-end gap-2 bg-card rounded-2xl border ${isFocused ? 'border-primary ring-2 ring-primary/20' : 'border-border'} p-2 shadow-sm transition-all duration-200`}>
          {/* Attachment Button */}
          <button
            type="button"
            onClick={onAttachFile}
            className="p-2 rounded-full text-muted-foreground hover:bg-muted transition-colors duration-200"
            aria-label="Attach file"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"/>
            </svg>
          </button>
          
          {/* Message Input */}
          <div className="flex-1 relative">
            <div className="relative">
              <textarea
                ref={textareaRef}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder={isLoading ? "Generating response..." : "Message Gemini..."}
                className="w-full bg-transparent border-none outline-none resize-none max-h-32 min-h-[40px] text-foreground placeholder-muted-foreground text-base pr-10 py-2.5"
                rows={1}
                disabled={isLoading}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(false)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSubmit(e);
                  } else if (e.key === 'ArrowUp' && (e.ctrlKey || e.metaKey || textareaRef.current?.selectionStart === 0)) {
                    e.preventDefault();
                    navigateHistory('up');
                  } else if (e.key === 'ArrowDown' && (e.ctrlKey || e.metaKey || 
                          (textareaRef.current?.selectionStart === textareaRef.current?.value.length))) {
                    e.preventDefault();
                    navigateHistory('down');
                  }
                }}
              />
            </div>
          </div>
          
          {/* Send Button */}
          <button
            type="submit"
            disabled={!message.trim() || isLoading}
            className={`p-2 rounded-full ${!message.trim() || isLoading 
              ? 'bg-muted text-muted-foreground cursor-not-allowed' 
              : 'bg-gradient-to-r from-primary to-secondary hover:from-primary-dark hover:to-secondary-dark text-primary-foreground'} 
              transition-all duration-200 flex-shrink-0`}
            aria-label={isLoading ? "Sending..." : "Send message"}
          >
            {isLoading ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="m22 2-7 20-4-9-9-4Z"/>
                <path d="M22 2 11 13"/>
              </svg>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
