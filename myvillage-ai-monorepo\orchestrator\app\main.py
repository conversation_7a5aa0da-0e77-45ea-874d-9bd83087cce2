"""Orchestrator Service - Intent detection and request routing."""
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import sys
from pathlib import Path
import logging

# Add common to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "common"))

from common.models.base import HealthResponse
from .core.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="MyVillage AI Orchestrator",
    version="1.0.0",
    description="Orchestrator for routing requests to appropriate MCP services"
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import routers
from .routers import chat_router

# Include routers
app.include_router(chat_router.router, tags=["chat"])


@app.get("/", response_model=HealthResponse)
async def root():
    """Root endpoint."""
    return HealthResponse(
        status="healthy",
        service="MyVillage AI Orchestrator",
        version="1.0.0"
    )


@app.get("/health")
async def health_check():
    """Health check endpoint with MCP service status."""
    from .services.health_checker import check_all_services
    
    services_status = await check_all_services()
    all_healthy = all(status["healthy"] for status in services_status.values())
    
    return {
        "status": "healthy" if all_healthy else "degraded",
        "service": "MyVillage AI Orchestrator",
        "version": "1.0.0",
        "mcp_services": services_status
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8100)
