# Fix MCP Server Pydantic Error

## Problem

The MCP server is failing with:
```
ImportError: cannot import name 'eval_type_backport' from 'pydantic._internal._typing_extra'
```

This is a Pydantic version conflict between:
- **fastmcp/mcp** packages (require older Pydantic internals)
- **Your installed Pydantic** (version 2.5.0 from orchestrator)

## Solution: Use API-Only Mode

Since you're now using the **external MVP Conversation Orchestrator**, you don't need the internal MCP server anymore!

### Step 1: Stop Current Processes

Press `Ctrl+C` to stop the current deployment.

### Step 2: Verify .env Configuration

Your `.env` file should have:

```env
# Run Mode (api, mcp, or both)
RUN_MODE=api

# Orchestrator Configuration
ORCHESTRATOR_URL=http://localhost:8100/chat
USE_ORCHESTRATOR=true
```

### Step 3: Restart with API-Only Mode

```bash
python deploy.py
```

You should see:
```
🚀 Starting unified deployment for API and MCP servers
Running in API-only mode
Starting API server on 0.0.0.0:8000
```

**No MCP server error!** ✅

## Alternative: Use run_api_only.py

If deploy.py still tries to run MCP server, use the dedicated script:

```bash
python run_api_only.py
```

This script **only** runs the API server and ignores MCP completely.

## Verify It's Working

### Test API Server

```bash
curl http://localhost:8000/api/v1/health
```

### Test Orchestrator Integration

```bash
curl -X POST http://localhost:8000/api/v1/chat-orchestrated \
  -H "Content-Type: application/json" \
  -H "session_id: test-123" \
  -d '{"message": "I want to sign up"}'
```

## Why This Works

### Old Architecture (with MCP server)
```
Frontend → Backend → Internal MCP Server → Services
```

### New Architecture (with Orchestrator)
```
Frontend → Backend → Orchestrator Client → External Orchestrator → MCPs
```

The internal MCP server is **replaced** by the external orchestrator, so you don't need it anymore!

## Complete Setup

### Terminal 1: Start Orchestrator
```bash
cd mvp-conversation-orchestrator
python -m app.main
```

### Terminal 2: Start API Server (API-only mode)
```bash
cd my_onboarding_api
python deploy.py
# OR
python run_api_only.py
```

### Terminal 3: Start Frontend
```bash
cd gemini-interface
npm run dev
```

### Test
Open: http://localhost:3000/onboarding-chat

## If You Still See MCP Error

The error might be from a previous run. Check:

1. **Kill old processes:**
   ```bash
   # Windows
   taskkill /F /IM python.exe
   
   # Or find specific PIDs
   netstat -ano | findstr :8000
   taskkill /F /PID <pid>
   ```

2. **Verify .env has RUN_MODE=api**

3. **Restart with clean state:**
   ```bash
   python run_api_only.py
   ```

## Long-term Fix (Optional)

If you want to fix the MCP server for future use:

### Option 1: Upgrade fastmcp/mcp
```bash
pip install --upgrade fastmcp mcp
```

### Option 2: Downgrade Pydantic
```bash
pip install pydantic==2.4.2
```

**But you don't need this now** since you're using the orchestrator! 🎉

---

**Status:** Use `RUN_MODE=api` and you're good to go!
