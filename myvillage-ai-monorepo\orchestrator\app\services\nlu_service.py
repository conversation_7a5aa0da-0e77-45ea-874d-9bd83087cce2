"""Natural Language Understanding Service with advanced intent detection and entity extraction."""
import re
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from enum import Enum

logger = logging.getLogger(__name__)


class Sentiment(str, Enum):
    """Sentiment types."""
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"


class EntityType(str, Enum):
    """Entity types that can be extracted."""
    ACTIVITY_TYPE = "activity_type"
    DATE = "date"
    DATE_RANGE = "date_range"
    CITY = "city"
    STATUS = "status"
    USER_ID = "user_id"
    ACTIVITY_ID = "activity_id"
    SUBJECT = "subject"
    ACTION = "action"
    POINTS = "points"


class NLUService:
    """Advanced Natural Language Understanding service."""
    
    def __init__(self):
        """Initialize NLU service with patterns and mappings."""
        
        # Activity type patterns
        self.activity_types = {
            "quiz": ["quiz", "quizzes", "test", "tests", "mcq"],
            "assignment": ["assignment", "assignments", "homework", "hw", "task"],
            "project": ["project", "projects"],
            "discussion": ["discussion", "discussions", "forum", "debate"],
            "event": ["event", "events", "activity", "activities"]
        }
        
        # Status patterns
        self.status_types = {
            "draft": ["draft", "drafts", "in progress", "working on"],
            "submitted": ["submitted", "turned in", "handed in", "sent"],
            "graded": ["graded", "scored", "marked", "evaluated"],
            "returned": ["returned", "given back"],
            "pending": ["pending", "waiting", "awaiting"],
            "approved": ["approved", "accepted"],
            "rejected": ["rejected", "declined", "denied"]
        }
        
        # Action patterns
        self.action_patterns = {
            "create": ["create", "make", "add", "new", "start"],
            "submit": ["submit", "turn in", "hand in", "send"],
            "list": ["show", "list", "display", "view", "see", "get"],
            "filter": ["filter", "search", "find", "where"],
            "redeem": ["redeem", "use", "spend", "claim"],
            "check": ["check", "view", "see", "show me"]
        }
        
        # Date patterns
        self.date_patterns = {
            "today": lambda: datetime.now().date(),
            "tomorrow": lambda: (datetime.now() + timedelta(days=1)).date(),
            "yesterday": lambda: (datetime.now() - timedelta(days=1)).date(),
            "this week": lambda: self._get_week_range(),
            "next week": lambda: self._get_week_range(offset=1),
            "this month": lambda: self._get_month_range(),
            "next month": lambda: self._get_month_range(offset=1)
        }
        
        # Sentiment keywords
        self.positive_keywords = [
            "great", "good", "excellent", "awesome", "wonderful", "thanks",
            "thank you", "perfect", "nice", "love", "like", "happy", "pleased"
        ]
        
        self.negative_keywords = [
            "bad", "terrible", "awful", "hate", "dislike", "angry", "frustrated",
            "confused", "difficult", "hard", "problem", "issue", "error", "wrong"
        ]
        
        # Intent patterns with confidence scores
        self.intent_patterns = {
            "signup": {
                "keywords": ["sign up", "signup", "register", "create account", "new account", "join"],
                "confidence": 0.95
            },
            "login": {
                "keywords": ["log in", "login", "sign in", "signin", "authenticate"],
                "confidence": 0.95
            },
            "activity_list": {
                "keywords": ["activities", "what can i do", "show activities", "list activities", "events"],
                "confidence": 0.85
            },
            "activity_create": {
                "keywords": ["create activity", "new activity", "add activity", "make activity"],
                "confidence": 0.90
            },
            "activity_submit": {
                "keywords": ["submit", "submit assignment", "turn in", "hand in", "submit homework"],
                "confidence": 0.90
            },
            "submission_list": {
                "keywords": ["submissions", "show submissions", "list submissions", "view submissions", 
                           "my submissions", "all submissions"],
                "confidence": 0.85
            },
            "rewards_get": {
                "keywords": ["rewards", "points", "my rewards", "check rewards", "balance", "my points"],
                "confidence": 0.90
            },
            "rewards_redeem": {
                "keywords": ["redeem", "use points", "spend points", "claim reward"],
                "confidence": 0.90
            },
            "approval_pending": {
                "keywords": ["pending", "approvals", "need approval", "waiting approval", "pending approvals"],
                "confidence": 0.85
            }
        }
    
    async def analyze_intent(self, text: str, context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Perform comprehensive NLU analysis on user input.
        
        Args:
            text: User input text
            context: Optional context (user info, conversation state, etc.)
            
        Returns:
            Dictionary containing:
            - primary_intent: Main detected intent
            - secondary_intents: List of other possible intents
            - entities: Extracted entities with types
            - confidence: Confidence score (0-1)
            - sentiment: Detected sentiment
            - action: Extracted action if any
            - context_aware: Whether context was used
        """
        if not text or not text.strip():
            return self._empty_analysis()
        
        text_lower = text.lower().strip()
        context = context or {}
        
        # Extract entities
        entities = self._extract_entities(text_lower)
        
        # Detect intent with confidence
        intent_result = self._detect_intent_with_confidence(text_lower, entities)
        
        # Analyze sentiment
        sentiment = self._analyze_sentiment(text_lower)
        
        # Extract action
        action = self._extract_action(text_lower)
        
        # Context-aware disambiguation
        if context:
            intent_result = self._apply_context(intent_result, entities, context)
        
        # Handle multi-intent scenarios
        multi_intent = self._detect_multi_intent(text_lower, entities)
        
        result = {
            "primary_intent": intent_result["intent"],
            "secondary_intents": multi_intent,
            "entities": entities,
            "confidence": intent_result["confidence"],
            "sentiment": sentiment,
            "action": action,
            "context_aware": bool(context),
            "raw_text": text,
            "normalized_text": text_lower
        }
        
        logger.info(f"NLU Analysis: intent={result['primary_intent']}, "
                   f"confidence={result['confidence']:.2f}, "
                   f"entities={len(entities)}, sentiment={sentiment}")
        
        return result
    
    def _extract_entities(self, text: str) -> Dict[str, Any]:
        """Extract entities from text."""
        entities = {}
        
        # Extract activity type
        activity_type = self._extract_activity_type(text)
        if activity_type:
            entities[EntityType.ACTIVITY_TYPE] = activity_type
        
        # Extract status
        status = self._extract_status(text)
        if status:
            entities[EntityType.STATUS] = status
        
        # Extract dates
        date_info = self._extract_dates(text)
        if date_info:
            entities.update(date_info)
        
        # Extract city (basic pattern matching)
        city = self._extract_city(text)
        if city:
            entities[EntityType.CITY] = city
        
        # Extract IDs (activity_id, user_id)
        ids = self._extract_ids(text)
        entities.update(ids)
        
        # Extract subject/topic
        subject = self._extract_subject(text)
        if subject:
            entities[EntityType.SUBJECT] = subject
        
        # Extract points/numbers
        points = self._extract_points(text)
        if points:
            entities[EntityType.POINTS] = points
        
        return entities
    
    def _extract_activity_type(self, text: str) -> Optional[str]:
        """Extract activity type from text."""
        for activity_type, keywords in self.activity_types.items():
            for keyword in keywords:
                if keyword in text:
                    return activity_type
        return None
    
    def _extract_status(self, text: str) -> Optional[str]:
        """Extract status from text."""
        for status, keywords in self.status_types.items():
            for keyword in keywords:
                if keyword in text:
                    return status
        return None
    
    def _extract_dates(self, text: str) -> Dict[str, Any]:
        """Extract date information from text."""
        dates = {}
        
        # Check for relative date patterns
        for pattern, date_func in self.date_patterns.items():
            if pattern in text:
                date_value = date_func()
                if isinstance(date_value, tuple):
                    dates[EntityType.DATE_RANGE] = {
                        "start": date_value[0].isoformat(),
                        "end": date_value[1].isoformat(),
                        "description": pattern
                    }
                else:
                    dates[EntityType.DATE] = {
                        "value": date_value.isoformat(),
                        "description": pattern
                    }
                break
        
        # Check for specific date patterns (YYYY-MM-DD, DD/MM/YYYY, etc.)
        date_regex_patterns = [
            r'\d{4}-\d{2}-\d{2}',  # YYYY-MM-DD
            r'\d{2}/\d{2}/\d{4}',  # DD/MM/YYYY
            r'\d{1,2}\s+(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*\s+\d{4}',  # DD Month YYYY
        ]
        
        for pattern in date_regex_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                dates[EntityType.DATE] = {
                    "value": match.group(0),
                    "description": "specific_date"
                }
                break
        
        return dates
    
    def _extract_city(self, text: str) -> Optional[str]:
        """Extract city name from text (basic pattern matching)."""
        # Common Indian cities (can be expanded)
        cities = [
            "mumbai", "delhi", "bangalore", "hyderabad", "chennai", "kolkata",
            "pune", "ahmedabad", "jaipur", "surat", "lucknow", "kanpur",
            "nagpur", "indore", "thane", "bhopal", "visakhapatnam", "pimpri"
        ]
        
        # Check for "in [city]" or "at [city]" patterns
        for city in cities:
            if f" in {city}" in text or f" at {city}" in text or f"{city} " in text:
                return city.title()
        
        return None
    
    def _extract_ids(self, text: str) -> Dict[str, str]:
        """Extract IDs from text."""
        ids = {}
        
        # Activity ID pattern
        activity_id_match = re.search(r'activity[_\s-]?id[:\s]+([a-zA-Z0-9-]+)', text, re.IGNORECASE)
        if activity_id_match:
            ids[EntityType.ACTIVITY_ID] = activity_id_match.group(1)
        
        # User ID pattern
        user_id_match = re.search(r'user[_\s-]?id[:\s]+([a-zA-Z0-9-]+)', text, re.IGNORECASE)
        if user_id_match:
            ids[EntityType.USER_ID] = user_id_match.group(1)
        
        return ids
    
    def _extract_subject(self, text: str) -> Optional[str]:
        """Extract subject/topic from text."""
        # Common subjects
        subjects = ["math", "science", "english", "history", "geography", 
                   "physics", "chemistry", "biology", "computer", "programming"]
        
        for subject in subjects:
            if subject in text:
                return subject.title()
        
        # Check for "for [subject]" pattern
        subject_match = re.search(r'for (?:the )?([a-z]+) (?:assignment|quiz|project)', text)
        if subject_match:
            return subject_match.group(1).title()
        
        return None
    
    def _extract_points(self, text: str) -> Optional[int]:
        """Extract points/numbers from text."""
        # Look for number patterns
        points_match = re.search(r'(\d+)\s*(?:points?|pts?)', text)
        if points_match:
            return int(points_match.group(1))
        
        return None
    
    def _extract_action(self, text: str) -> Optional[str]:
        """Extract action from text."""
        for action, keywords in self.action_patterns.items():
            for keyword in keywords:
                if keyword in text:
                    return action
        return None
    
    def _detect_intent_with_confidence(self, text: str, entities: Dict) -> Dict[str, Any]:
        """Detect intent with confidence score."""
        best_match = {
            "intent": "general",
            "confidence": 0.5
        }
        
        for intent, config in self.intent_patterns.items():
            for keyword in config["keywords"]:
                if keyword in text:
                    # Calculate confidence based on keyword match and entities
                    confidence = config["confidence"]
                    
                    # Boost confidence if relevant entities are present
                    if intent == "activity_list" and EntityType.CITY in entities:
                        confidence = min(1.0, confidence + 0.05)
                    elif intent == "activity_submit" and EntityType.ACTIVITY_TYPE in entities:
                        confidence = min(1.0, confidence + 0.05)
                    elif intent == "submission_list" and EntityType.STATUS in entities:
                        confidence = min(1.0, confidence + 0.05)
                    
                    if confidence > best_match["confidence"]:
                        best_match = {
                            "intent": intent,
                            "confidence": confidence
                        }
        
        return best_match
    
    def _analyze_sentiment(self, text: str) -> str:
        """Analyze sentiment of the text."""
        positive_count = sum(1 for keyword in self.positive_keywords if keyword in text)
        negative_count = sum(1 for keyword in self.negative_keywords if keyword in text)
        
        if positive_count > negative_count:
            return Sentiment.POSITIVE
        elif negative_count > positive_count:
            return Sentiment.NEGATIVE
        else:
            return Sentiment.NEUTRAL
    
    def _detect_multi_intent(self, text: str, entities: Dict) -> List[str]:
        """Detect multiple intents in a single query."""
        intents = []
        
        # Check for compound queries
        # Example: "Show me quizzes and my submissions"
        if "and" in text or "also" in text:
            for intent, config in self.intent_patterns.items():
                for keyword in config["keywords"]:
                    if keyword in text:
                        intents.append(intent)
        
        return list(set(intents))  # Remove duplicates
    
    def _apply_context(self, intent_result: Dict, entities: Dict, context: Dict) -> Dict[str, Any]:
        """Apply context to disambiguate intent."""
        # If user recently performed an action, consider it in intent detection
        if "last_intent" in context:
            last_intent = context["last_intent"]
            
            # If current query is vague but has entities, use context
            if intent_result["confidence"] < 0.7:
                # Example: User said "show activities", then says "in Mumbai"
                # We can infer they want to continue with activity listing
                if last_intent == "activity_list" and EntityType.CITY in entities:
                    intent_result["intent"] = "activity_list"
                    intent_result["confidence"] = 0.85
        
        # User role context
        if "user_role" in context:
            role = context["user_role"]
            # Admins might have different default intents
            if role in ["admin", "super_admin"] and intent_result["intent"] == "general":
                # Could default to admin-specific actions
                pass
        
        return intent_result
    
    def _get_week_range(self, offset: int = 0) -> tuple:
        """Get start and end of week."""
        today = datetime.now().date()
        start = today - timedelta(days=today.weekday()) + timedelta(weeks=offset)
        end = start + timedelta(days=6)
        return (start, end)
    
    def _get_month_range(self, offset: int = 0) -> tuple:
        """Get start and end of month."""
        today = datetime.now().date()
        if offset == 0:
            start = today.replace(day=1)
        else:
            # Next month
            if today.month == 12:
                start = today.replace(year=today.year + 1, month=1, day=1)
            else:
                start = today.replace(month=today.month + 1, day=1)
        
        # Get last day of month
        if start.month == 12:
            end = start.replace(year=start.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            end = start.replace(month=start.month + 1, day=1) - timedelta(days=1)
        
        return (start, end)
    
    def _empty_analysis(self) -> Dict[str, Any]:
        """Return empty analysis for invalid input."""
        return {
            "primary_intent": "general",
            "secondary_intents": [],
            "entities": {},
            "confidence": 0.0,
            "sentiment": Sentiment.NEUTRAL,
            "action": None,
            "context_aware": False,
            "raw_text": "",
            "normalized_text": ""
        }


# Global instance
nlu_service = NLUService()
