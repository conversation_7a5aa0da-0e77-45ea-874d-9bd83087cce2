# PowerShell test script for MVP Conversation Orchestrator

Write-Host "==========================================" -ForegroundColor Cyan
Write-Host "Testing MVP Conversation Orchestrator" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host ""

# Health check
Write-Host "1. Health Check:" -ForegroundColor Yellow
Invoke-RestMethod -Uri "http://localhost:8000/health" -Method Get | ConvertTo-Json
Write-Host ""

# Test onboarding intent (signup)
Write-Host "2. Test Signup Intent:" -ForegroundColor Yellow
$body1 = @{
    user_id = "user-123"
    text = "I want to sign up"
    session_id = "session-abc"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:8000/chat" -Method Post -Body $body1 -ContentType "application/json" | ConvertTo-Json
Write-Host ""

# Test onboarding intent (login)
Write-Host "3. Test Login Intent:" -ForegroundColor Yellow
$body2 = @{
    user_id = "user-456"
    text = "I need to log in"
    session_id = "session-def"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:8000/chat" -Method Post -Body $body2 -ContentType "application/json" | ConvertTo-Json
Write-Host ""

# Test activity intent
Write-Host "4. Test Activity Intent:" -ForegroundColor Yellow
$body3 = @{
    user_id = "user-789"
    text = "Show me activities"
    session_id = "session-ghi"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:8000/chat" -Method Post -Body $body3 -ContentType "application/json" | ConvertTo-Json
Write-Host ""

# Test general query (defaults to activity)
Write-Host "5. Test General Query:" -ForegroundColor Yellow
$body4 = @{
    user_id = "user-999"
    text = "Hello, what can you help me with?"
    session_id = "session-jkl"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:8000/chat" -Method Post -Body $body4 -ContentType "application/json" | ConvertTo-Json
Write-Host ""

Write-Host "==========================================" -ForegroundColor Cyan
Write-Host "Tests Complete!" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan
