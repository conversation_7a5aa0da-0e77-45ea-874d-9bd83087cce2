# Submission Listing - Complete Guide

## 🎯 Overview

Submissions can now be listed with flexible filtering options. Users can filter by activity, user, status, or view all submissions through a conversational interface.

## 🚀 How It Works

### User Flow
1. User asks: "Show me submissions" or "List submissions"
2. <PERSON><PERSON> asks: "Would you like to filter submissions by: Activity, User, Status, or All?"
3. User selects filter type (1-4 or name)
4. <PERSON><PERSON> shows filtered submissions (sorted by most recent)

### For MCP Clients (ChatGPT, Claude, etc.)
MCP tools can directly pass filter parameters:

```bash
# Direct API call with filters
GET /tools/list_submissions?activity_id=act-123&limit=10
GET /tools/list_submissions?user_id=user-456&limit=20
GET /tools/list_submissions?status=graded&limit=15
GET /tools/list_submissions?limit=50  # All submissions
```

## 📋 Implementation Details

### 1. Files Created/Modified

**Backend - New Files:**
- ✅ `mcp-activities/app/tools/list_submissions.py` - NEW: List submissions tool

**Backend - Modified Files:**
- ✅ `mcp-activities/app/main.py` - Added list_submissions route
- ✅ `orchestrator/app/services/conversation_manager.py` - Added list_submissions flow
- ✅ `orchestrator/app/routers/chat_router.py` - Added submission listing logic
- ✅ `orchestrator/app/services/activities_client.py` - Added list_submissions method
- ✅ `orchestrator/app/services/intent_detector.py` - Added submission_list intent

**Existing (Already Present):**
- ✅ `mcp-activities/app/services/submission_service.py` - list_submissions method exists
- ✅ `mcp-activities/app/models/submission.py` - Submission models exist

**Frontend:**
- No changes needed! The UI automatically displays submissions returned by the API

### 2. Database Schema

**Submissions Table:**
```json
{
  "id": "uuid",
  "activity_id": "string",
  "user_id": "string",
  "title": "string",
  "description": "string",
  "content": "string",
  "status": "draft|submitted|graded|returned",
  "is_public": "boolean",
  "created_at": "timestamp",
  "updated_at": "timestamp",
  "submitted_at": "timestamp",
  "grade": "float",
  "feedback": "string",
  "graded_by": "string",
  "graded_at": "timestamp",
  "likes_count": "integer"
}
```

### 3. API Endpoints

#### List Submissions (with filters)
```bash
GET /tools/list_submissions?activity_id=act-123&limit=10

# Optional parameters:
# - activity_id: filter by activity
# - user_id: filter by user
# - status: draft|submitted|graded|returned
# - limit: max results (1-100, default: 10)

# Response:
{
  "success": true,
  "message": "Found 5 submission(s)",
  "data": {
    "submissions": [
      {
        "id": "sub-123",
        "activity_id": "act-456",
        "user_id": "user-789",
        "title": "My Assignment Submission",
        "description": "Completed assignment",
        "status": "graded",
        "is_public": true,
        "created_at": "2025-11-21T10:00:00",
        "updated_at": "2025-11-21T15:30:00",
        "submitted_at": "2025-11-21T14:00:00",
        "grade": 95.0,
        "feedback": "Excellent work!",
        "graded_by": "teacher-001",
        "graded_at": "2025-11-21T15:30:00",
        "likes_count": 12
      }
    ],
    "count": 5
  }
}
```

## 🧪 Testing

### Test 1: Basic Flow - All Submissions
```bash
# Start orchestrator
cd myvillage-ai-monorepo/orchestrator
python -m app.main

# Start activities MCP
cd myvillage-ai-monorepo/mcp-activities
python -m app.main

# Test via API
curl -X POST http://localhost:8100/chat \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "test-user",
    "text": "Show me submissions"
  }'

# Expected: Bot asks for filter type

curl -X POST http://localhost:8100/chat \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "test-user",
    "text": "4"
  }'

# Expected: Bot shows all submissions
```

### Test 2: Filter by Activity
```bash
curl -X POST http://localhost:8100/chat \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "test-user",
    "text": "List submissions"
  }'

curl -X POST http://localhost:8100/chat \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "test-user",
    "text": "activity"
  }'

# Expected: Bot asks for activity ID
```

### Test 3: Direct MCP Call
```bash
# List all submissions
curl "http://localhost:8002/tools/list_submissions?limit=20"

# Filter by activity
curl "http://localhost:8002/tools/list_submissions?activity_id=act-123&limit=10"

# Filter by user
curl "http://localhost:8002/tools/list_submissions?user_id=user-456&limit=10"

# Filter by status
curl "http://localhost:8002/tools/list_submissions?status=graded&limit=10"
```

## 🔑 Key Features

### ✅ Conversational Flow
- Natural language interaction
- User-friendly filter selection
- Error handling for invalid inputs

### ✅ MCP-Compatible
- Works with ChatGPT, Claude, and other MCP clients
- Optional filter parameters
- Stateless tool design

### ✅ Sorted by Recent Updates
- Submissions sorted by `updated_at` DESC
- Most recently updated submissions appear first

### ✅ Flexible Filtering
- Filter by activity (see all submissions for an activity)
- Filter by user (see all submissions by a user)
- Filter by status (draft, submitted, graded, returned)
- View all submissions

## 🎨 UI Integration

The UI automatically handles the new submission listing flow:

1. **ChatInterface.tsx** - Displays conversation flow
2. **SubmissionList.tsx** - Shows submissions (if exists)
3. **ChatMessage.tsx** - Renders bot responses

## 🔐 Environment Variables

Add to your `.env` files:

```env
# Orchestrator & MCP Services
DYNAMODB_SUBMISSIONS_TABLE=submissions
DYNAMODB_ACTIVITIES_TABLE=activities
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-key
AWS_SECRET_ACCESS_KEY=your-secret
```

## 📊 Benefits

### For Users
- ✅ Simple: Just select filter type
- ✅ Fast: Instant submission lookup
- ✅ Flexible: Multiple filter options
- ✅ Helpful: Clear error messages

### For Developers
- ✅ MCP-compliant: Works with any MCP client
- ✅ Reusable: Service methods can be used elsewhere
- ✅ Maintainable: Clean separation of concerns
- ✅ Scalable: DynamoDB handles large datasets

### For Admins
- ✅ Trackable: Monitor submissions by activity/user
- ✅ Organized: Submissions grouped by filters
- ✅ Sortable: Recent submissions appear first

## 🚨 Error Handling

### No Submissions Found
```
📝 No submissions found at the moment.
```

### Service Error
```
❌ Sorry, there was an error fetching submissions. 
   Please try again.
```

## 🔄 Submission Status Flow

```
draft → submitted → graded → returned
```

## 📞 Filter Options

### 1. By Activity
Shows all submissions for a specific activity
- Useful for teachers/admins reviewing activity submissions
- Example: "Show all submissions for Assignment 1"

### 2. By User
Shows all submissions by a specific user
- Useful for viewing a student's submission history
- Example: "Show all submissions by user-123"

### 3. By Status
Shows submissions with a specific status
- draft: Work in progress
- submitted: Awaiting review
- graded: Reviewed and graded
- returned: Returned to student
- Example: "Show all graded submissions"

### 4. All Submissions
Shows all submissions (up to limit)
- Useful for overview and monitoring
- Example: "Show me all submissions"

## 🔄 Future Enhancements

1. **Date Range Filter**: Filter submissions by date range
2. **Grade Range Filter**: Filter by grade (e.g., 90-100)
3. **Search**: Search submissions by title/content
4. **Sorting Options**: Sort by grade, date, likes
5. **Pagination**: Navigate through large result sets
6. **Export**: Export submissions to CSV/PDF

## 📈 Architecture Alignment

This implementation follows the same architecture pattern as activities:

```
User Request → Orchestrator → Intent Detection → Conversation Flow
                    ↓
            Activities MCP → list_submissions tool
                    ↓
            Submission Service → DynamoDB
                    ↓
            Return Results → User
```

## 📝 Code Structure

```
myvillage-ai-monorepo/
├── orchestrator/
│   └── app/
│       ├── routers/
│       │   └── chat_router.py          # Added submission_list flow
│       └── services/
│           ├── activities_client.py    # Added list_submissions()
│           ├── conversation_manager.py # Added list_submissions flow
│           └── intent_detector.py      # Added submission_list intent
└── mcp-activities/
    └── app/
        ├── tools/
        │   └── list_submissions.py     # NEW: List submissions tool
        ├── services/
        │   └── submission_service.py   # Existing: list_submissions()
        └── models/
            └── submission.py           # Existing: Submission models
```

## ✅ Implementation Complete!

Submissions can now be listed with flexible filtering through:
- ✅ Conversational UI (orchestrator)
- ✅ Direct MCP calls (ChatGPT, Claude)
- ✅ REST API endpoints

---

**Status: ✅ READY FOR TESTING**

**Start with:** Test the conversational flow via chat UI

**For MCP clients:** Use direct API calls with filter parameters

**Always reference:** This guide for filter options and examples
