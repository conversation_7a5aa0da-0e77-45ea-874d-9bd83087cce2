# Deployment Guide: Unified API and MCP Server

This guide explains how to deploy both the FastAPI server and MCP (Model Context Protocol) server together on Render.com.

## Overview

The deployment includes:
- **FastAPI Server**: Handles onboarding intent classification and Gemini chat (Port 8000)
- **MCP Server**: Provides MCP tools for external clients (Port 5000)
- **Unified Deployment**: Both servers run concurrently in a single deployment

## Quick Start

### 1. Prepare Your Repository

Ensure your repository has the following files:
- `render.yaml` (deployment configuration)
- `my_onboarding_api/deploy.py` (unified startup script)
- `my_onboarding_api/requirements.txt` (with fastmcp dependency)
- `my_onboarding_api/.env.example` (environment template)

### 2. Deploy to Render

#### Option A: Using render.yaml (Recommended)

1. **Connect Repository**: 
   - Go to [Render Dashboard](https://dashboard.render.com)
   - Click "New" → "Blueprint"
   - Connect your GitHub repository
   - Render will automatically detect `render.yaml`

2. **Configure Environment Variables**:
   ```
   HF_TOKEN=hf_your_huggingface_token_here
   MODEL_NAME=jalpesh088/myvillage-onboarding-intent
   GEMINI_API_KEY=your_gemini_api_key_here
   GEMINI_MODEL=models/gemini-2.5-flash
   API_BEARER_TOKEN=your_optional_bearer_token (optional)
   ```

3. **Deploy**: Click "Apply" to start deployment

#### Option B: Manual Web Service

1. **Create Web Service**:
   - Go to Render Dashboard
   - Click "New" → "Web Service"
   - Connect your repository
   - Set root directory to `my_onboarding_api`

2. **Configure Build & Start**:
   ```bash
   # Build Command
   pip install --upgrade pip && pip install -r requirements.txt
   
   # Start Command
   python deploy.py
   ```

3. **Set Environment Variables** (same as Option A)

### 3. Access Your Deployed Services

After successful deployment:

- **API Server**: `https://your-app-name.onrender.com`
- **MCP Server**: `https://your-app-name.onrender.com:5000` (if exposed)
- **Health Check**: `https://your-app-name.onrender.com/health`
- **API Documentation**: `https://your-app-name.onrender.com/docs` (if DEBUG=true)

## MCP Server Access

### Global MCP Access

The MCP server runs on port 5000 and provides the following tools:
- `classify_intent(message: str)`: Classify user intent
- `chat_with_intent(message: str, session_id: str)`: Chat with Gemini AI

### Connecting MCP Clients

```python
from fastmcp import Client

# Connect to your deployed MCP server
client = Client("https://your-app-name.onrender.com:5000")

# Use the tools
result = client.classify_intent(message="I want to sign up")
chat_response = client.chat_with_intent(
    message="Hello", 
    session_id="user123"
)
```

## Environment Variables Reference

### Required Variables
- `HF_TOKEN`: Hugging Face API token (starts with `hf_`)
- `MODEL_NAME`: Hugging Face model name for intent classification
- `GEMINI_API_KEY`: Google Gemini API key
- `GEMINI_MODEL`: Gemini model name (default: `models/gemini-2.5-flash`)

### Optional Variables
- `API_BASE_URL`: Base URL for API server (auto-set in deployment)
- `MCP_HOST`: MCP server host (default: `0.0.0.0`)
- `MCP_PORT`: MCP server port (default: `5000`)
- `RUN_MODE`: Run mode - `both`, `api`, or `mcp` (default: `both`)
- `DEBUG`: Enable debug mode (default: `false`)
- `LOG_LEVEL`: Logging level (default: `INFO`)

## Troubleshooting

### Common Issues

1. **Import Errors**:
   - Ensure `fastmcp>=0.1.0` is in requirements.txt
   - Check that all dependencies are properly installed

2. **MCP Server Not Accessible**:
   - Verify port 5000 is exposed in Render configuration
   - Check that `MCP_HOST=0.0.0.0` in environment variables

3. **API Connection Errors**:
   - Ensure `API_BASE_URL` points to your deployed API server
   - Check that both servers are running (check logs)

### Debugging

1. **Check Logs**:
   ```bash
   # In Render dashboard, view service logs
   # Look for startup messages from both servers
   ```

2. **Test Individual Servers**:
   ```bash
   # Set RUN_MODE to test individual components
   RUN_MODE=api    # Run only API server
   RUN_MODE=mcp    # Run only MCP server
   RUN_MODE=both   # Run both (default)
   ```

3. **Health Check**:
   ```bash
   curl https://your-app-name.onrender.com/health
   ```

## Local Development

### Running Both Servers Locally

```bash
cd my_onboarding_api

# Copy environment template
cp .env.example .env
# Edit .env with your API keys

# Install dependencies
pip install -r requirements.txt

# Run both servers
python deploy.py
```

### Running Servers Separately

```bash
# Terminal 1: API Server
python run.py

# Terminal 2: MCP Server  
python mcp_server.py
```

## Security Considerations

1. **Environment Variables**: Never commit API keys to version control
2. **CORS**: Configure appropriate CORS origins for production
3. **Rate Limiting**: Default rate limiting is enabled (100 requests/minute)
4. **Bearer Token**: Use `API_BEARER_TOKEN` for additional API security

## Testing Deployment

### Automated Testing

Use the provided test script to verify your deployment:

```bash
# Test local deployment
cd my_onboarding_api
python test_deployment.py

# Test remote deployment
python test_deployment.py https://your-app-name.onrender.com
```

The test script checks:
- API server health and service status
- Intent classification functionality
- MCP server accessibility

### Manual Testing

1. **API Health Check**:
   ```bash
   curl https://your-app-name.onrender.com/health
   ```

2. **Intent Classification**:
   ```bash
   curl -X POST https://your-app-name.onrender.com/onboarding \
     -H "Content-Type: application/json" \
     -d '{"message": "I want to sign up"}'
   ```

3. **MCP Client Test**:
   ```python
   from fastmcp import Client
   client = Client("https://your-app-name.onrender.com:5000")
   result = client.classify_intent(message="Hello")
   ```

## Support

For issues or questions:
1. Run the test script: `python test_deployment.py`
2. Check the health endpoint: `/health`
3. Review server logs in Render dashboard
4. Verify environment variables are set correctly
5. Test MCP tools individually using the FastMCP client
