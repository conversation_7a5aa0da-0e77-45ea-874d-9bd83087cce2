"""Rewards MCP Service - Rewards tracking and redemption."""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import sys
from pathlib import Path
import logging

# Add common to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "common"))

from common.models.base import HealthResponse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Rewards MCP",
    version="1.0.0",
    description="MCP service for rewards tracking and redemption"
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import routers
from .tools import get_rewards, calculate_points, redeem_reward

# Include tool routers
app.include_router(get_rewards.router, prefix="/tools", tags=["tools"])
app.include_router(calculate_points.router, prefix="/tools", tags=["tools"])
app.include_router(redeem_reward.router, prefix="/tools", tags=["tools"])


@app.get("/", response_model=HealthResponse)
async def root():
    """Root endpoint."""
    return HealthResponse(
        status="healthy",
        service="Rewards MCP",
        version="1.0.0"
    )


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        service="Rewards MCP",
        version="1.0.0"
    )


@app.get("/manifest")
async def get_manifest():
    """Return MCP manifest for ChatGPT integration."""
    return {
        "name": "rewards-mcp",
        "version": "1.0.0",
        "description": "Rewards tracking and redemption",
        "tools": [
            {
                "name": "get_rewards",
                "description": "Get user's reward points and history",
                "parameters": {
                    "user_id": {"type": "string", "required": True}
                }
            },
            {
                "name": "calculate_points",
                "description": "Calculate points for an activity completion",
                "parameters": {
                    "activity_id": {"type": "string", "required": True},
                    "user_id": {"type": "string", "required": True}
                }
            },
            {
                "name": "redeem_reward",
                "description": "Redeem points for a reward",
                "parameters": {
                    "user_id": {"type": "string", "required": True},
                    "reward_id": {"type": "string", "required": True},
                    "points": {"type": "integer", "required": True}
                }
            }
        ]
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8003)
