# NLU Service Guide

## Overview

The **Natural Language Understanding (NLU) Service** provides advanced intent detection and entity extraction capabilities for the MyVillage AI chatbot. It replaces basic keyword matching with sophisticated pattern recognition, entity extraction, sentiment analysis, and context-aware disambiguation.

## Features

### 🎯 Advanced Intent Detection
- **Confidence Scoring**: Each intent detection includes a confidence score (0-1)
- **Multi-Intent Handling**: Detects multiple intents in compound queries
- **Context-Aware**: Uses conversation history and user context for better accuracy

### 📦 Entity Extraction
Automatically extracts the following entity types:

| Entity Type | Examples | Use Cases |
|------------|----------|-----------|
| **activity_type** | quiz, assignment, project, discussion | Filter activities by type |
| **status** | draft, submitted, graded, pending | Filter by submission status |
| **city** | Mumbai, Delhi, Bangalore | Location-based filtering |
| **date** | today, tomorrow, 2025-12-15 | Single date queries |
| **date_range** | this week, next month | Range-based queries |
| **activity_id** | ACT-12345 | Specific activity lookup |
| **user_id** | USER-789 | User-specific queries |
| **subject** | math, science, english | Subject-based filtering |
| **points** | 500 points | Rewards/points queries |
| **action** | create, submit, list, redeem | Action identification |

### 😊 Sentiment Analysis
Detects user sentiment to improve response quality:
- **Positive**: "Great!", "I love this", "Thanks"
- **Negative**: "Confused", "Difficult", "Problem"
- **Neutral**: Default for most queries

### 🔄 Context-Aware Disambiguation
Uses conversation context to improve intent detection:
- Previous intent history
- User role (student, teacher, admin)
- Collected entities from previous messages

## Usage

### Basic Usage

```python
from app.services.intent_detector import IntentDetector

detector = IntentDetector()

# Simple intent detection (backward compatible)
intent = detector.detect("show me all activities")
print(intent)  # "activity_list"
```

### Advanced Usage with NLU

```python
import asyncio
from app.services.intent_detector import IntentDetector

async def analyze_message():
    detector = IntentDetector()
    
    # Advanced intent detection with entity extraction
    result = await detector.detect_advanced(
        "Show me quizzes in Mumbai due this week"
    )
    
    print(f"Intent: {result['intent']}")
    print(f"Confidence: {result['confidence']}")
    print(f"Entities: {result['entities']}")
    print(f"Sentiment: {result['sentiment']}")

asyncio.run(analyze_message())
```

### Context-Aware Detection

```python
async def context_aware_example():
    detector = IntentDetector()
    
    # Provide context for better disambiguation
    context = {
        "user_id": "user123",
        "user_role": "student",
        "last_intent": "activity_list"
    }
    
    # Vague query that benefits from context
    result = await detector.detect_advanced(
        "in Mumbai",
        context=context
    )
    
    # Will correctly infer continuation of activity_list intent
    print(f"Intent: {result['intent']}")  # "activity_list"

asyncio.run(context_aware_example())
```

### Extracting Filters from Entities

```python
async def extract_filters_example():
    detector = IntentDetector()
    
    result = await detector.detect_advanced(
        "Show submitted assignments in Bangalore due this week"
    )
    
    # Convert entities to filter parameters
    filters = detector.extract_filters_from_entities(result['entities'])
    
    print(filters)
    # {
    #     'activity_type': 'assignment',
    #     'status': 'submitted',
    #     'city': 'Bangalore',
    #     'start_date': '2025-12-02',
    #     'end_date': '2025-12-08'
    # }

asyncio.run(extract_filters_example())
```

## Example Queries

### Activity Queries

| User Query | Detected Intent | Extracted Entities |
|-----------|----------------|-------------------|
| "Show me quizzes in Mumbai" | activity_list | activity_type=quiz, city=Mumbai |
| "List all assignments due tomorrow" | activity_list | activity_type=assignment, date=tomorrow |
| "What events are happening this week?" | activity_list | activity_type=event, date_range=this_week |

### Submission Queries

| User Query | Detected Intent | Extracted Entities |
|-----------|----------------|-------------------|
| "Show submitted assignments" | submission_list | activity_type=assignment, status=submitted |
| "I want to submit my math homework" | activity_submit | activity_type=assignment, subject=math |
| "Show all graded projects" | submission_list | activity_type=project, status=graded |

### Rewards Queries

| User Query | Detected Intent | Extracted Entities |
|-----------|----------------|-------------------|
| "Check my rewards" | rewards_get | - |
| "I want to redeem 500 points" | rewards_redeem | points=500 |
| "How many points do I have?" | rewards_get | - |

### Complex Multi-Entity Queries

| User Query | Extracted Entities |
|-----------|-------------------|
| "Show me quizzes in Mumbai due this week" | activity_type=quiz, city=Mumbai, date_range=this_week |
| "List all submitted assignments for math class in Bangalore" | activity_type=assignment, status=submitted, subject=math, city=Bangalore |
| "Show pending approvals for activities created today" | status=pending, date=today |

## Response Structure

### Advanced Detection Response

```python
{
    "intent": "activity_list",              # Primary detected intent
    "confidence": 0.95,                     # Confidence score (0-1)
    "sentiment": "positive",                # positive/negative/neutral
    "action": "list",                       # Extracted action
    "entities": {                           # Extracted entities
        "activity_type": "quiz",
        "city": "Mumbai",
        "date_range": {
            "start": "2025-12-02",
            "end": "2025-12-08",
            "description": "this week"
        }
    },
    "secondary_intents": [],                # Other possible intents
    "full_analysis": {                      # Complete NLU analysis
        "primary_intent": "activity_list",
        "normalized_text": "show me quizzes in mumbai due this week",
        "context_aware": false
    }
}
```

## Integration with Conversation Manager

The NLU service can be integrated with the conversation manager for multi-step flows:

```python
from app.services.conversation_manager import conversation_manager
from app.services.intent_detector import IntentDetector

async def handle_message(user_id: str, message: str):
    detector = IntentDetector()
    
    # Check for active conversation
    if conversation_manager.has_active_conversation(user_id):
        # Process as part of ongoing conversation
        return conversation_manager.process_input(user_id, message)
    
    # Detect intent with NLU
    result = await detector.detect_advanced(message)
    
    # If entities are already extracted, skip some conversation steps
    if result['entities']:
        # Pre-populate conversation with extracted entities
        filters = detector.extract_filters_from_entities(result['entities'])
        # Use filters to make direct API calls
        # ...
    else:
        # Start conversation flow to collect missing information
        return conversation_manager.start_flow(user_id, result['intent'])
```

## Date Extraction Examples

The NLU service supports various date formats:

### Relative Dates
- "today" → Current date
- "tomorrow" → Next day
- "yesterday" → Previous day

### Date Ranges
- "this week" → Monday to Sunday of current week
- "next week" → Monday to Sunday of next week
- "this month" → 1st to last day of current month
- "next month" → 1st to last day of next month

### Specific Dates
- "2025-12-15" → ISO format
- "15/12/2025" → DD/MM/YYYY format
- "15 December 2025" → Natural format

## Performance Considerations

### Confidence Thresholds

Use confidence scores to determine response strategy:

```python
result = await detector.detect_advanced(message)

if result['confidence'] >= 0.9:
    # High confidence - proceed directly
    execute_action(result['intent'], result['entities'])
elif result['confidence'] >= 0.7:
    # Medium confidence - confirm with user
    ask_confirmation(result['intent'])
else:
    # Low confidence - ask for clarification
    request_clarification()
```

### Caching

For frequently used patterns, consider caching NLU results:

```python
from functools import lru_cache

@lru_cache(maxsize=1000)
def get_cached_intent(message: str) -> str:
    # Cache simple intent detection
    return detector.detect(message)
```

## Extending the NLU Service

### Adding New Entity Types

Edit `orchestrator/app/services/nlu_service.py`:

```python
class EntityType(str, Enum):
    # ... existing types ...
    GRADE_LEVEL = "grade_level"  # Add new entity type

# Add extraction method
def _extract_grade_level(self, text: str) -> Optional[str]:
    grades = ["grade 1", "grade 2", "class 10", "class 12"]
    for grade in grades:
        if grade in text:
            return grade
    return None

# Update _extract_entities method
def _extract_entities(self, text: str) -> Dict[str, Any]:
    entities = {}
    # ... existing extractions ...
    
    grade_level = self._extract_grade_level(text)
    if grade_level:
        entities[EntityType.GRADE_LEVEL] = grade_level
    
    return entities
```

### Adding New Intent Patterns

```python
self.intent_patterns = {
    # ... existing patterns ...
    "grade_submission": {
        "keywords": ["grade", "evaluate", "score", "mark submission"],
        "confidence": 0.90
    }
}
```

## Testing

Run the example test suite:

```bash
cd orchestrator
python -m examples.nlu_examples
```

This will run comprehensive tests covering:
- Entity extraction
- Sentiment analysis
- Context-aware detection
- Multi-intent handling
- Real-world conversation scenarios

## Best Practices

1. **Always use async methods** for advanced detection to support future enhancements
2. **Provide context** when available to improve accuracy
3. **Check confidence scores** before taking automated actions
4. **Extract filters** from entities to reduce conversation steps
5. **Log NLU results** for monitoring and improvement
6. **Handle low confidence** gracefully with clarification prompts

## Migration from Basic Intent Detection

### Before (Basic)
```python
detector = IntentDetector()
intent = detector.detect("show activities")
# Returns: "activity_list"
```

### After (Advanced)
```python
detector = IntentDetector()
result = await detector.detect_advanced("show quizzes in Mumbai")
# Returns: {
#     "intent": "activity_list",
#     "entities": {"activity_type": "quiz", "city": "Mumbai"},
#     "confidence": 0.95
# }
```

The basic `detect()` method is still supported for backward compatibility.

## Troubleshooting

### Low Confidence Scores

If you're getting low confidence scores:
1. Check if the query matches known patterns
2. Add more keywords to intent patterns
3. Provide conversation context
4. Consider adding custom entity extractors

### Missing Entities

If entities aren't being extracted:
1. Verify the entity pattern exists in the NLU service
2. Check if the text format matches expected patterns
3. Add more pattern variations
4. Review logs for extraction attempts

### Incorrect Intent Detection

If wrong intents are detected:
1. Check keyword overlap between intents
2. Adjust confidence scores in intent patterns
3. Use context to disambiguate
4. Add negative examples to patterns

## Future Enhancements

Planned improvements for the NLU service:

- [ ] Machine learning-based intent classification
- [ ] Named Entity Recognition (NER) with spaCy
- [ ] Multi-language support
- [ ] Custom entity training
- [ ] Intent disambiguation UI
- [ ] A/B testing framework for intent patterns
- [ ] Analytics dashboard for NLU performance

## Support

For questions or issues with the NLU service:
1. Check the example file: `examples/nlu_examples.py`
2. Review logs in `logs/orchestrator.log`
3. Consult the API documentation
4. Contact the development team

---

**Version**: 1.0.0  
**Last Updated**: December 2, 2025  
**Maintained By**: MyVillage AI Team
