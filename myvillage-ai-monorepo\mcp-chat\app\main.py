"""MCP Chat Service - FastAPI application."""
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import sys
from pathlib import Path
import logging

# Add common to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "common"))

from common.models.base import HealthResponse
from .core.config import settings
from .models.chat import (
    ChatMessageCreate,
    ChatMessageResponse,
    ChatHistoryRequest,
    ChatHistoryResponse,
    ConversationListResponse,
    DeleteMessageRequest,
    DeleteMessageResponse
)
from .services.chat_service import get_chat_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="MCP Chat Service",
    version="1.0.0",
    description="Chat history management service for MyVillage AI"
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Get service instance
chat_service = get_chat_service()


@app.get("/", response_model=HealthResponse)
async def root():
    """Root endpoint."""
    return HealthResponse(
        status="healthy",
        service=settings.service_name,
        version=settings.service_version
    )


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        service=settings.service_name,
        version=settings.service_version
    )


@app.post("/messages", response_model=ChatMessageResponse)
async def save_message(message_data: ChatMessageCreate):
    """
    Save a chat message.
    
    Args:
        message_data: Chat message data
        
    Returns:
        Saved message response
    """
    try:
        saved_message = chat_service.save_message(message_data)
        return ChatMessageResponse(
            success=True,
            message="Message saved successfully",
            data=saved_message
        )
    except Exception as e:
        logger.error(f"Error saving message: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to save message: {str(e)}"
        )


@app.post("/messages/history", response_model=ChatHistoryResponse)
async def get_chat_history(request: ChatHistoryRequest):
    """
    Get chat history for a user.
    
    Args:
        request: Chat history request
        
    Returns:
        Chat history response
    """
    try:
        messages = chat_service.get_chat_history(
            request.userId,
            request.chatType,
            request.limit or 50
        )
        return ChatHistoryResponse(
            success=True,
            message=f"Retrieved {len(messages)} messages",
            data=messages
        )
    except Exception as e:
        logger.error(f"Error retrieving chat history: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve chat history: {str(e)}"
        )


@app.get("/conversations/{user_id}", response_model=ConversationListResponse)
async def get_conversations(user_id: str):
    """
    Get list of conversations for a user.
    
    Args:
        user_id: User ID
        
    Returns:
        Conversations list response
    """
    try:
        conversations = chat_service.get_conversations(user_id)
        return ConversationListResponse(
            success=True,
            message=f"Retrieved {len(conversations)} conversations",
            data=conversations
        )
    except Exception as e:
        logger.error(f"Error retrieving conversations: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve conversations: {str(e)}"
        )


@app.delete("/messages/{message_id}", response_model=DeleteMessageResponse)
async def delete_message(message_id: str, request: DeleteMessageRequest):
    """
    Soft delete a message.
    
    Args:
        message_id: Message ID
        request: Delete request with user ID
        
    Returns:
        Delete response
    """
    try:
        success = chat_service.delete_message(message_id, request.userId)
        if success:
            return DeleteMessageResponse(
                success=True,
                message="Message deleted successfully"
            )
        else:
            raise HTTPException(
                status_code=404,
                detail="Message not found or user mismatch"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting message: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete message: {str(e)}"
        )


@app.get("/.well-known/mcp-manifest.json")
async def get_mcp_manifest():
    """Return MCP manifest for ChatGPT integration."""
    from .tools import save_message, get_history, get_conversations, delete_message
    
    return {
        "schema_version": "1.0",
        "name": "chat",
        "description": "Chat history management service",
        "version": "1.0.0",
        "tools": [
            save_message.TOOL_METADATA,
            get_history.TOOL_METADATA,
            get_conversations.TOOL_METADATA,
            delete_message.TOOL_METADATA
        ]
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host=settings.api_host, port=settings.api_port)
