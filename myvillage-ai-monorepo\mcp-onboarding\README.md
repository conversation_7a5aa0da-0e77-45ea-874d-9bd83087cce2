# Onboarding MCP Service

User onboarding, authentication, and profile management microservice.

## 🎯 Purpose

This service handles:
- User registration (signup)
- User authentication (login)
- OTP verification
- Profile management

## 🔧 Tools

### 1. create_user
Create a new user account.

**Parameters:**
- `name` (string, required): Full name
- `email` (string, required): Email address
- `password` (string, required): Password (min 8 chars)
- `phone_number` (string, optional): Phone number

**Example:**
```bash
curl -X POST http://localhost:8001/tools/create_user \
  -H "Content-Type: application/json" \
  -d '{
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "password": "SecurePass123"
  }'
```

### 2. login_user
Authenticate user and get access token.

**Parameters:**
- `email` (string, required): Email address
- `password` (string, required): Password

### 3. verify_otp
Verify OTP code.

**Parameters:**
- `email` (string, required): Email address
- `otp` (string, required): 6-digit OTP code

### 4. update_profile
Update user profile.

**Parameters:**
- `user_id` (string, required): User ID
- `data` (object, required): Profile data to update

## 🚀 Running the Service

### Local Development

```bash
pip install -r requirements.txt
python -m app.main
```

### Docker

```bash
docker build -t mcp-onboarding .
docker run -p 8001:8001 mcp-onboarding
```

## 📡 Endpoints

- `GET /health` - Health check
- `GET /manifest` - MCP manifest
- `POST /tools/create_user` - Create user
- `POST /tools/login_user` - Login user
- `POST /tools/verify_otp` - Verify OTP
- `POST /tools/update_profile` - Update profile

## 🔐 Security

- Passwords hashed using PBKDF2-HMAC-SHA256
- JWT tokens for authentication
- Input validation on all endpoints

## 📦 Port

8001
