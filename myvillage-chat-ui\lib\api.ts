import axios from 'axios';
import { authUtils } from './auth';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8100';

export interface Conversation {
  conversationId: string;
  userId: string;
  chatType: string;
  title: string;
  lastMessage: string;
  lastMessageTime: string;
  messageCount: number;
}

export interface ChatMessage {
  id: string;
  userId: string;
  chatType: string;
  message: string;
  role: 'user' | 'assistant';
  messageType: 'TEXT' | 'FILE' | 'URL';
  fileUrl?: string;
  isDeleted: string;
  createdAt: string;
}

export interface City {
  id: string;
  name: string;
  state?: string;
  country?: string;
  population?: number;
  is_active?: boolean;
}

export const chatApi = {
  /**
   * Get list of conversations for a user
   */
  getConversations: async (userId: string): Promise<Conversation[]> => {
    try {
      const response = await axios.get(`${API_URL}/conversations/${userId}`);
      if (response.data.success) {
        return response.data.data || [];
      }
      return [];
    } catch (error) {
      console.error('Error fetching conversations:', error);
      return [];
    }
  },

  /**
   * Get chat history for a user/conversation
   */
  getChatHistory: async (userId: string, chatType?: string, limit: number = 50): Promise<ChatMessage[]> => {
    try {
      const response = await axios.post(`${API_URL}/chat/history`, {
        userId,
        chatType,
        limit
      });
      if (response.data.success) {
        return response.data.data || [];
      }
      return [];
    } catch (error) {
      console.error('Error fetching chat history:', error);
      return [];
    }
  },

  /**
   * Delete a message
   */
  deleteMessage: async (messageId: string, userId: string): Promise<boolean> => {
    try {
      const response = await axios.delete(`${API_URL}/messages/${messageId}`, {
        data: { userId }
      });
      return response.data.success;
    } catch (error) {
      console.error('Error deleting message:', error);
      return false;
    }
  },

  /**
   * Get list of available cities
   */
  getCities: async (): Promise<City[]> => {
    try {
      const response = await axios.get(`${API_URL}/cities`);
      if (response.data.success) {
        return response.data.data.cities || [];
      }
      return [];
    } catch (error) {
      console.error('Error fetching cities:', error);
      return [];
    }
  }
};
