# Orchestrator Service

Intent detection and request routing service for MyVillage AI.

## 🎯 Purpose

This service:
- Detects user intent from messages
- Routes requests to appropriate MCP services
- Aggregates responses
- Provides unified API for frontend

## 🔧 Features

- **Intent Detection**: Keyword-based intent classification
- **Service Routing**: Routes to onboarding, activities, or rewards MCPs
- **Health Monitoring**: Checks health of all MCP services
- **Error Handling**: Graceful error handling and fallbacks

## 📡 Endpoints

### POST /chat
Main chat endpoint for processing user messages.

**Request:**
```json
{
  "user_id": "user-123",
  "text": "Show me activities",
  "session_id": "session-abc"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Found 5 activities",
  "intent": "activity_list",
  "routed_to": "activities",
  "data": {...}
}
```

### GET /health
Health check with MCP service status.

**Response:**
```json
{
  "status": "healthy",
  "service": "MyVillage AI Orchestrator",
  "version": "1.0.0",
  "mcp_services": {
    "onboarding": {"healthy": true, "url": "http://localhost:8001"},
    "activities": {"healthy": true, "url": "http://localhost:8002"},
    "rewards": {"healthy": true, "url": "http://localhost:8003"}
  }
}
```

## 🚀 Running the Service

### Local Development

```bash
pip install -r requirements.txt
python -m app.main
```

### Docker

```bash
docker build -t orchestrator .
docker run -p 8100:8100 orchestrator
```

## 🎯 Intent Detection

**Supported Intents:**
- `signup` - User registration
- `login` - User authentication
- `activity_list` - List activities
- `activity_create` - Create activity
- `activity_submit` - Submit assignment
- `rewards_get` - Get reward balance
- `rewards_redeem` - Redeem points
- `approval_pending` - Pending approvals

## 🔗 MCP Services

- **Onboarding MCP** (Port 8001): User auth and profiles
- **Activities MCP** (Port 8002): Activity management
- **Rewards MCP** (Port 8003): Rewards tracking
- **Approval MCP** (Port 8004): Approval workflows

## 🧪 Testing

```bash
# Health check
curl http://localhost:8100/health

# Chat endpoint
curl -X POST http://localhost:8100/chat \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user-123",
    "text": "Show me activities"
  }'
```

## 📦 Port

8100
