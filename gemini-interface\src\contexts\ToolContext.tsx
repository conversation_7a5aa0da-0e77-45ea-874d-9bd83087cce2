'use client';

import { createContext, useContext, ReactNode, useState } from 'react';

type ToolCall = {
  id: string;
  toolName: string;
  timestamp: Date;
  status: 'pending' | 'success' | 'error';
  duration?: number;
  error?: string;
};

type ToolContextType = {
  toolCalls: ToolCall[];
  addToolCall: (toolName: string) => string;
  updateToolCall: (id: string, updates: Partial<Omit<ToolCall, 'id' | 'toolName' | 'timestamp'>>) => void;
  clearToolCalls: () => void;
};

const ToolContext = createContext<ToolContextType | undefined>(undefined);

export function ToolProvider({ children }: { children: ReactNode }) {
  const [toolCalls, setToolCalls] = useState<ToolCall[]>([]);

  const addToolCall = (toolName: string): string => {
    const id = `tool-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newToolCall: ToolCall = {
      id,
      toolName,
      timestamp: new Date(),
      status: 'pending',
    };
    
    setToolCalls(prev => [newToolCall, ...prev]);
    return id;
  };

  const updateToolCall = (id: string, updates: Partial<Omit<ToolCall, 'id' | 'toolName' | 'timestamp'>>) => {
    setToolCalls(prev => 
      prev.map(tool => 
        tool.id === id 
          ? { 
              ...tool, 
              ...updates,
              ...(updates.status === 'success' || updates.status === 'error' 
                ? { duration: new Date().getTime() - tool.timestamp.getTime() }
                : {})
            } 
          : tool
      )
    );
  };

  const clearToolCalls = () => {
    setToolCalls([]);
  };

  return (
    <ToolContext.Provider value={{ toolCalls, addToolCall, updateToolCall, clearToolCalls }}>
      {children}
    </ToolContext.Provider>
  );
}

export function useToolContext() {
  const context = useContext(ToolContext);
  if (context === undefined) {
    throw new Error('useToolContext must be used within a ToolProvider');
  }
  return context;
}
