# Architecture Alignment Changes Required

## Executive Summary

After reviewing both `my_onboarding_api` and `mvp-conversation-orchestrator` against the architecture document (`my_onboarding_api/architecture.md`), there are significant structural and functional gaps that need to be addressed to align with the intended microservices architecture.

**Current State:**
- `my_onboarding_api`: Monolithic application with all functionality (onboarding, activities, submissions, auth) in one service
- `mvp-conversation-orchestrator`: Basic orchestrator with mock MCP clients

**Target State (per architecture.md):**
- Separate MCP services: `mcp-onboarding`, `mcp-activities`, `mcp-rewards`, `mcp-approval`
- Orchestrator that routes to these independent services
- Clean separation of concerns with each MCP handling specific domains

---

## 🔴 Critical Architectural Misalignments

### 1. **Monolithic vs Microservices Structure**

**Current Problem:**
- `my_onboarding_api` contains ALL functionality in one service:
  - Onboarding flows (signup/login)
  - Activities management
  - Submissions handling
  - Authentication
  - Intent classification
  - Gemini chat
  - Session management

**Architecture Requirement:**
```
orchestrator/          → Intent detection & routing only
mcp-onboarding/       → User signup/login/verification only
mcp-activities/       → Activity listing & submission only
mcp-rewards/          → Rewards retrieval only
mcp-approval/         → Activity approval workflows only
```

**Required Changes:**
1. Extract onboarding functionality into standalone `mcp-onboarding` service
2. Extract activities functionality into standalone `mcp-activities` service
3. Create new `mcp-rewards` service
4. Create new `mcp-approval` service
5. Refactor orchestrator to only handle routing (no business logic)

---

### 2. **MCP Manifest Missing**

**Current Problem:**
- No `mcp-manifest.json` files exist in either service
- Architecture requires manifests for ChatGPT integration

**Architecture Requirement:**
Each MCP service needs a manifest file defining its tools:

```json
{
  "name": "onboarding-mcp",
  "version": "1.0.0",
  "tools": [
    {
      "name": "create_user",
      "description": "Create a new user account",
      "parameters": {
        "name": {"type": "string", "required": true},
        "email": {"type": "string", "required": true}
      }
    }
  ]
}
```

**Required Changes:**
1. Create `mcp-manifest.json` for each MCP service
2. Define all available tools with parameters
3. Implement manifest endpoint in each MCP service

---

### 3. **Orchestrator Functionality Mismatch**

**Current Problem:**
- `mvp-conversation-orchestrator` has mock clients that don't call real services
- `my_onboarding_api` has its own orchestrator logic mixed with business logic

**Architecture Requirement:**
- Orchestrator should ONLY:
  - Detect intent
  - Route to appropriate MCP
  - Return unified response
- NO business logic in orchestrator

**Required Changes:**
1. Remove all business logic from orchestrator
2. Implement real HTTP clients to call MCP services
3. Standardize request/response formats between orchestrator and MCPs
4. Remove duplicate orchestrator logic from `my_onboarding_api`

---

### 4. **Service Communication Pattern**

**Current Problem:**
- Services don't follow the architecture's communication pattern
- No standardized API contracts between services

**Architecture Requirement:**
```
Custom Chatbot → Orchestrator → MCP Server → Database
                     ↓
              Intent Detection
                     ↓
              Route to MCP
                     ↓
           Unified Response
```

**Required Changes:**
1. Define standard API contracts for all MCP services
2. Implement consistent request/response schemas
3. Add proper error handling and retry logic
4. Implement health check endpoints for all services

---

## 📋 Detailed Change Requirements

### A. Create Separate MCP Services

#### 1. **mcp-onboarding/** (Extract from my_onboarding_api)

**Structure:**
```
mcp-onboarding/
├── src/
│   ├── server.py                    # MCP server implementation
│   └── tools/
│       ├── create_user.py           # User account creation
│       ├── verify_otp.py            # OTP verification
│       ├── login_user.py            # User login
│       └── update_profile.py        # Profile updates
├── mcp-manifest.json                # Tool definitions
├── requirements.txt
├── Dockerfile
└── README.md
```

**Files to Extract:**
- `app/services/auth_service.py` → `src/tools/create_user.py`, `src/tools/login_user.py`
- `app/services/session_service.py` → Session management logic
- `app/api/auth.py` → Authentication endpoints
- `app/models/user.py` → User models
- `app/db/user.py` → User database operations

**New Endpoints:**
- `POST /tools/create_user` - Create new user account
- `POST /tools/verify_otp` - Verify OTP code
- `POST /tools/login_user` - Authenticate user
- `POST /tools/update_profile` - Update user profile
- `GET /manifest` - Return MCP manifest
- `GET /health` - Health check

---

#### 2. **mcp-activities/** (Extract from my_onboarding_api)

**Structure:**
```
mcp-activities/
├── src/
│   ├── server.py                    # MCP server implementation
│   └── tools/
│       ├── list_activities.py       # List available activities
│       ├── get_activity.py          # Get activity details
│       ├── create_activity.py       # Create new activity
│       ├── submit_assignment.py     # Submit activity assignment
│       └── update_activity.py       # Update activity
├── mcp-manifest.json                # Tool definitions
├── requirements.txt
├── Dockerfile
└── README.md
```

**Files to Extract:**
- `app/api/endpoints/activities.py` → Activity endpoints
- `app/api/endpoints/submissions.py` → Submission endpoints
- `app/services/activity_service.py` → Activity business logic
- `app/services/submission_service.py` → Submission business logic
- `app/models/activity.py` → Activity models
- `app/models/submission.py` → Submission models

**New Endpoints:**
- `POST /tools/list_activities` - List activities
- `POST /tools/get_activity` - Get activity details
- `POST /tools/create_activity` - Create activity
- `POST /tools/submit_assignment` - Submit assignment
- `POST /tools/update_activity` - Update activity
- `GET /manifest` - Return MCP manifest
- `GET /health` - Health check

---

#### 3. **mcp-rewards/** (New Service)

**Structure:**
```
mcp-rewards/
├── src/
│   ├── server.py                    # MCP server implementation
│   └── tools/
│       ├── get_rewards.py           # Get user rewards
│       ├── calculate_points.py      # Calculate reward points
│       └── redeem_reward.py         # Redeem rewards
├── mcp-manifest.json                # Tool definitions
├── requirements.txt
├── Dockerfile
└── README.md
```

**New Endpoints:**
- `POST /tools/get_rewards` - Get user rewards
- `POST /tools/calculate_points` - Calculate points
- `POST /tools/redeem_reward` - Redeem reward
- `GET /manifest` - Return MCP manifest
- `GET /health` - Health check

---

#### 4. **mcp-approval/** (New Service)

**Structure:**
```
mcp-approval/
├── src/
│   ├── server.py                    # MCP server implementation
│   └── tools/
│       ├── approve_activity.py      # Approve activity
│       ├── reject_activity.py       # Reject activity
│       └── get_pending.py           # Get pending approvals
├── mcp-manifest.json                # Tool definitions
├── requirements.txt
├── Dockerfile
└── README.md
```

**New Endpoints:**
- `POST /tools/approve_activity` - Approve activity
- `POST /tools/reject_activity` - Reject activity
- `POST /tools/get_pending` - Get pending approvals
- `GET /manifest` - Return MCP manifest
- `GET /health` - Health check

---

### B. Refactor Orchestrator

**Current:** `mvp-conversation-orchestrator/`

**Required Changes:**

1. **Remove Mock Clients** - Replace with real HTTP clients:

```python
# mvp-conversation-orchestrator/app/services/onboarding_client.py
class OnboardingClient:
    def __init__(self, base_url: str):
        self.base_url = base_url  # http://mcp-onboarding:8001
    
    async def create_user(self, name: str, email: str, password: str):
        """Call mcp-onboarding service to create user"""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/tools/create_user",
                json={"name": name, "email": email, "password": password}
            )
            return response.json()
```

2. **Standardize Intent Detection**:
   - Move intent classification logic from `my_onboarding_api` to orchestrator
   - Use consistent intent labels across all services

3. **Add Service Discovery**:
   - Implement health checks for all MCP services
   - Add retry logic and circuit breakers
   - Handle service unavailability gracefully

4. **Update Routing Logic**:

```python
# Orchestrator routing map
INTENT_TO_SERVICE = {
    "signup": "onboarding_mcp",
    "login": "onboarding_mcp",
    "activity_list": "activities_mcp",
    "activity_submit": "activities_mcp",
    "rewards_get": "rewards_mcp",
    "approval_pending": "approval_mcp"
}
```

---

### C. Common Shared Library

**Create:** `common/` directory for shared code

**Structure:**
```
common/
├── models/
│   ├── user.py              # Shared user models
│   ├── activity.py          # Shared activity models
│   └── response.py          # Standard response models
├── utils/
│   ├── validation.py        # Input validation
│   ├── security.py          # Security utilities
│   └── database.py          # Database utilities
├── config/
│   └── settings.py          # Shared configuration
└── constants.py             # Application constants
```

**Purpose:**
- Avoid code duplication across services
- Ensure consistent data models
- Share utility functions

---

## 🔧 Implementation Priority

### Phase 1: Foundation (Week 1)
1. ✅ Create `common/` shared library
2. ✅ Define standard API contracts and schemas
3. ✅ Create MCP manifest templates
4. ✅ Set up service structure for all MCPs

### Phase 2: Extract Onboarding (Week 2)
1. ✅ Create `mcp-onboarding/` service
2. ✅ Extract auth and user management code
3. ✅ Implement MCP tools for onboarding
4. ✅ Create manifest and documentation
5. ✅ Test standalone deployment

### Phase 3: Extract Activities (Week 3)
1. ✅ Create `mcp-activities/` service
2. ✅ Extract activity and submission code
3. ✅ Implement MCP tools for activities
4. ✅ Create manifest and documentation
5. ✅ Test standalone deployment

### Phase 4: New Services (Week 4)
1. ✅ Create `mcp-rewards/` service
2. ✅ Create `mcp-approval/` service
3. ✅ Implement basic functionality
4. ✅ Create manifests and documentation

### Phase 5: Orchestrator Integration (Week 5)
1. ✅ Refactor orchestrator to call real MCPs
2. ✅ Remove mock clients
3. ✅ Implement service discovery
4. ✅ Add error handling and retries
5. ✅ Test end-to-end flows

### Phase 6: Testing & Deployment (Week 6)
1. ✅ Integration testing
2. ✅ Performance testing
3. ✅ Docker compose setup
4. ✅ Deployment documentation
5. ✅ Monitoring and logging

---

## 📦 Deployment Configuration

### Docker Compose Structure

```yaml
version: '3.8'

services:
  orchestrator:
    build: ./orchestrator
    ports:
      - "8100:8100"
    environment:
      - ONBOARDING_MCP_URL=http://mcp-onboarding:8001
      - ACTIVITIES_MCP_URL=http://mcp-activities:8002
      - REWARDS_MCP_URL=http://mcp-rewards:8003
      - APPROVAL_MCP_URL=http://mcp-approval:8004
    depends_on:
      - mcp-onboarding
      - mcp-activities
      - mcp-rewards
      - mcp-approval

  mcp-onboarding:
    build: ./mcp-onboarding
    ports:
      - "8001:8001"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}

  mcp-activities:
    build: ./mcp-activities
    ports:
      - "8002:8002"
    environment:
      - DATABASE_URL=${DATABASE_URL}

  mcp-rewards:
    build: ./mcp-rewards
    ports:
      - "8003:8003"
    environment:
      - DATABASE_URL=${DATABASE_URL}

  mcp-approval:
    build: ./mcp-approval
    ports:
      - "8004:8004"
    environment:
      - DATABASE_URL=${DATABASE_URL}
```

---

## 🎯 Key Benefits After Changes

### 1. **True Microservices Architecture**
- Each service has single responsibility
- Independent deployment and scaling
- Easier maintenance and debugging

### 2. **ChatGPT Integration Ready**
- MCP manifests enable direct ChatGPT integration
- No orchestrator needed for ChatGPT flows
- Automatic tool discovery

### 3. **Custom Chatbot Support**
- Orchestrator provides controlled flows
- Custom business logic possible
- Consistent API for frontend

### 4. **Code Reusability**
- Both ChatGPT and custom chatbots use same MCPs
- No code duplication
- Single source of truth for business logic

### 5. **Scalability**
- Services scale independently
- Load balancing per service
- Resource optimization

### 6. **Maintainability**
- Clear separation of concerns
- Easier testing (unit and integration)
- Simpler debugging

---

## 📝 Migration Strategy

### Option 1: Big Bang (Not Recommended)
- Refactor everything at once
- High risk, long downtime
- Difficult to test incrementally

### Option 2: Strangler Pattern (Recommended)
1. Keep `my_onboarding_api` running
2. Create new MCP services one by one
3. Route traffic gradually to new services
4. Deprecate old endpoints progressively
5. Remove monolith when all traffic migrated

### Option 3: Parallel Run
1. Deploy new architecture alongside old
2. Run both systems in parallel
3. Compare results for validation
4. Switch traffic when confident
5. Decommission old system

**Recommendation:** Use **Strangler Pattern** for lowest risk and incremental validation.

---

## 🚨 Breaking Changes

### API Endpoints
- Old: `POST /gemini-chat-with-intent` (monolith)
- New: `POST /chat` (orchestrator) → routes to MCPs

### Authentication
- Old: Session-based in monolith
- New: JWT tokens shared across MCPs

### Database
- Old: Single database for all
- New: Database per service (or shared with proper isolation)

### Configuration
- Old: Single `.env` file
- New: Separate config per service

---

## 📚 Documentation Needs

### For Each MCP Service:
1. ✅ README.md with setup instructions
2. ✅ API documentation (OpenAPI/Swagger)
3. ✅ MCP manifest file
4. ✅ Deployment guide
5. ✅ Testing guide

### For Orchestrator:
1. ✅ Architecture overview
2. ✅ Intent detection logic
3. ✅ Routing rules
4. ✅ Error handling
5. ✅ Integration guide

### For Overall System:
1. ✅ System architecture diagram
2. ✅ Service communication flows
3. ✅ Deployment guide (Docker, Render, etc.)
4. ✅ Monitoring and logging setup
5. ✅ Troubleshooting guide

---

## 🔍 Testing Requirements

### Unit Tests
- Each MCP tool function
- Orchestrator routing logic
- Shared utility functions

### Integration Tests
- Orchestrator → MCP communication
- End-to-end user flows
- Error scenarios

### Performance Tests
- Load testing each service
- Latency measurements
- Resource usage monitoring

### Security Tests
- Authentication/authorization
- Input validation
- SQL injection prevention
- XSS protection

---

## 📊 Success Metrics

### Technical Metrics
- ✅ All services deployable independently
- ✅ < 200ms latency for orchestrator routing
- ✅ 99.9% uptime for each service
- ✅ Zero data loss during migration

### Business Metrics
- ✅ No user-facing disruption
- ✅ Same or better response times
- ✅ Improved developer productivity
- ✅ Faster feature deployment

---

## 🎬 Next Steps

1. **Review this document** with the team
2. **Prioritize changes** based on business needs
3. **Create detailed tickets** for each phase
4. **Set up development environment** for new services
5. **Begin Phase 1** (Foundation work)

---

## 📞 Questions to Address

1. **Database Strategy**: Single shared database or database per service?
2. **Authentication**: JWT tokens or session-based? Shared auth service?
3. **Deployment Platform**: Docker Compose, Kubernetes, or Render?
4. **Monitoring**: What tools for logging and monitoring?
5. **CI/CD**: How to automate testing and deployment?
6. **Backwards Compatibility**: Support old API during migration?

---

## 📎 Appendix

### A. File Mapping

**From my_onboarding_api → To mcp-onboarding:**
- `app/services/auth_service.py` → `src/tools/create_user.py`
- `app/api/auth.py` → `src/server.py` (endpoints)
- `app/models/user.py` → `common/models/user.py`

**From my_onboarding_api → To mcp-activities:**
- `app/api/endpoints/activities.py` → `src/tools/list_activities.py`
- `app/services/activity_service.py` → `src/tools/` (various)
- `app/models/activity.py` → `common/models/activity.py`

### B. Environment Variables

**Orchestrator:**
```env
ONBOARDING_MCP_URL=http://localhost:8001
ACTIVITIES_MCP_URL=http://localhost:8002
REWARDS_MCP_URL=http://localhost:8003
APPROVAL_MCP_URL=http://localhost:8004
LOG_LEVEL=INFO
```

**Each MCP:**
```env
DATABASE_URL=postgresql://...
JWT_SECRET=your-secret-key
PORT=800X
LOG_LEVEL=INFO
```

---

**Document Version:** 1.0  
**Last Updated:** 2025-11-18  
**Author:** Architecture Review Team
