# MyVillage Architecture Guide

## Overview

This document outlines the current architecture and improvement plan for the MyVillage web application, built with Next.js 15, TypeScript, and Shadcn UI. The focus is on improving the existing structure for better maintainability and scalability without adding new features.

## Table of Contents

1. [Project Structure](#project-structure)
2. [Core Principles](#core-principles)
3. [Component Architecture](#component-architecture)
4. [State Management](#state-management)
5. [API Layer](#api-layer)
6. [Styling System](#styling-system)
7. [Testing Strategy](#testing-strategy)
8. [Performance Considerations](#performance-considerations)

## Project Structure

```
src/
├── app/                          # App Router
│   ├── (auth)/                   # Auth-related routes
│   ├── (dashboard)/             # Protected routes
│   └── api/                     # API routes
│
├── components/                  # Shared components
│   ├── ui/                      # Shadcn UI components
│   └── layout/                  # Layout components
│
├── features/                    # Feature modules
│   ├── auth/                    # Authentication feature
│   └── chat/                    # Chat feature
│
├── lib/                         # Shared utilities
│   ├── api/                     # API client
│   └── utils/                   # Helper functions
│
└── providers/                   # Global providers
```

## Core Principles

1. **Feature-First Organization**
   - Group by feature, not by file type
   - Co-locate related code
   - Enable code splitting by feature

2. **Type Safety**
   - Full TypeScript support
   - Runtime validation with Zod
   - Shared type definitions

3. **Performance**
   - Code splitting
   - Lazy loading
   - Image optimization

## Component Architecture

### UI Components (`/components/ui`)

- Built on top of Shadcn UI
- Headless components with Radix UI primitives
- Styled with Tailwind CSS
- Fully typed with TypeScript

### Layout Components (`/components/layout`)

- App-wide layouts
- Navigation components
- Page templates

### Feature Components (`/features/*/components`)

- Feature-specific components
- Connected to feature state
- Reusable within the feature

## State Management

### Server State

- **React Query** for data fetching and caching
- Automatic background updates
- Request deduplication
- Optimistic updates

### Client State

- **Zustand** for global client state
- **Context API** for theme and auth
- URL state for UI state

### Local State

- React `useState` for component state
- `useReducer` for complex state logic
- `useCallback` and `useMemo` for optimization

## API Layer

### API Client (`/lib/api`)

```typescript
// Example API client
import axios from 'axios';

const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export default api;
```

### API Routes (`/app/api`)

- Route Handlers for API endpoints
- Server-side data fetching
- Authentication and authorization

## Styling System

### Tailwind CSS

- Utility-first CSS framework
- Custom theme configuration
- Responsive design utilities

### Class Variance Authority (CVA)

- Type-safe component variants
- Composable styling API
- Theme-aware components

## Testing Strategy

### Unit Testing

- Jest and React Testing Library
- Component testing
- Utility function testing

### Integration Testing

- End-to-end testing with Playwright
- API route testing
- Authentication flows

### Visual Regression

- Storybook for component documentation
- Chromatic for visual testing

## Performance Considerations

### Code Splitting

- Dynamic imports for routes
- Lazy loading components
- Route-based code splitting

### Image Optimization

- Next.js Image component
- Automatic format optimization
- Lazy loading

### Bundle Analysis

- `@next/bundle-analyzer`
- Performance budgets
- Code splitting analysis

## Best Practices

1. **Component Design**
   - Keep components small and focused
   - Use composition over inheritance
   - Follow the single responsibility principle

2. **State Management**
   - Colocate state with components
   - Lift state up when needed
   - Use appropriate state management solution

3. **Performance**
   - Memoize expensive calculations
   - Avoid unnecessary re-renders
   - Use code splitting

## Getting Started

### Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

### Testing

```bash
# Run unit tests
npm test

# Run E2E tests
npm run test:e2e

# Run Storybook
npm run storybook
```

## Contributing

1. Create a feature branch
2. Follow the code style guide
3. Write tests for new features
4. Submit a pull request

## Current Architecture Analysis

### Strengths
- Clear separation of concerns with dedicated context providers
- Organized component structure with feature-based grouping
- TypeScript support throughout the codebase
- Shadcn UI integration for consistent design

### Areas for Improvement
1. **Component Organization**
   - Inconsistent component grouping (some in root, some in feature folders)
   - Duplicate ThemeProvider implementations
   - Mix of UI and business logic in components

2. **State Management**
   - Multiple context providers that could be consolidated
   - Potential prop drilling in some components
   - No clear data fetching strategy

3. **Project Structure**
   - Inconsistent file naming conventions
   - Services directory exists but is empty
   - Some utility functions could be better organized

## Improvement Plan

### Phase 1: Component Reorganization
1. **Consolidate Providers**
   - [ ] Merge duplicate ThemeProvider implementations
   - [ ] Create a single AppProvider to wrap all contexts
   - [ ] Document context usage patterns

2. **Component Structure**
   - [ ] Move all UI components to `/components/ui`
   - [ ] Group feature components by domain (chat, tools, etc.)
   - [ ] Update import paths to use absolute imports

### Phase 2: State Management Refinement
1. **Context Optimization**
   - [ ] Review and optimize AuthContext
   - [ ] Review and optimize ChatContext
   - [ ] Review and optimize ToolContext

2. **Data Fetching**
   - [ ] Move API calls to dedicated service files
   - [ ] Implement consistent error handling
   - [ ] Add loading states where missing

### Phase 3: Code Quality & Performance
1. **Type Safety**
   - [ ] Add missing TypeScript types
   - [ ] Create shared type definitions
   - [ ] Fix any TypeScript errors

2. **Performance**
   - [ ] Implement React.memo for expensive components
   - [ ] Add proper loading states
   - [ ] Optimize re-renders

3. **Documentation**
   - [ ] Document component props and usage
   - [ ] Add JSDoc comments to functions and hooks
   - [ ] Document state management patterns

## License

[Your License Here]
